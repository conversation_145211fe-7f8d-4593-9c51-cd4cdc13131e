import { useState } from 'react';

export default function ProfileTabs({ setActiveTabs, activeTab, }: any) {
    const [tabss, setTab] = useState('propertylistings');

    const tabs = [
        { id: 'propertylistings', label: 'Applications' },
        { id: 'projects', label: 'User Activity' },
        { id: 'profile', label: 'Sales & Revenue' },
        { id: 'services', label: 'Geographics' },
        { id: 'agents', label: 'Referrals' },
    ];

    return (
        <>
            <div className=" px-5 pb-0.5 pt-6 ">
                {/* Tabs Section */}
                <div className="flex items-center   justify-between gap-2 bg-white  max-md:flex-col   md:border-gray-300">
                    <div className="flex space-x-1 shadow-[0px_4px_20px_0px_rgba(21,32,70,0.07)]  rounded-t-xl border max-sm:w-full max-sm:overflow-x-auto">
                        {tabs.map((tab, index) => (
                            <button
                                key={tab.id}
                                onClick={() => {
                                    setTab(tab.id);
                                    setActiveTabs(index + 1);
                                }}
                                className={` px-8 py-4 text-sm font-medium shadow-sm   transition-all sm:px-16
                                    ${tabss === tab.id ? 'bg-[#732323]  text-white' : 'text-gray-600'}
                                    ${activeTab === 1 ? ' rounded-tl-xl   ' : ' '}
                                    ${activeTab === 5 ? ' rounded-tr-xl' : '  border-r '}

                                    `}
                            >
                                {tab.label}
                            </button>
                        ))}
                    </div>

                    {/* Right Side Button */}
                </div>
            </div>
            {/* <div className="px-5">
                <hr />
            </div> */}
        </>
    );
}
