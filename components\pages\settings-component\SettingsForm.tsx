'use client';

import { showMessage } from '@/app/lib/Alert';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrumButton from '@/components/reusable/BreadCrumButton';
import BreadCrums from '@/components/reusable/BreadCrums';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function SettingsForm() {
    const { push } = useRouter();
    const [form, setForm] = useState({
        app_name: '',
        copyright_text: '',
        monthly_discount_type: 'percentage',
        monthly_discount_value: '',
        monthly_coupon_code: '',
        yearly_discount_type: 'percentage',
        yearly_discount_value: '',
        yearly_coupon_code: '',
        logo: null as File | null,
        favicon: null as File | null,
    });

    const [preview, setPreview] = useState({
        logo: '',
        favicon: '',
    });

    const [loading, setLoading] = useState(false);

    useEffect(() => {
        async function fetchSettings() {
            try {
                const res = await fetch(API_ENDPOINTS.SETTINGS, {
                    method: 'GET',
                    credentials: 'include',
                });
                const json = await res.json();
                if (json?.data) {
                    setForm((prev) => ({
                        ...prev,
                        ...json.data,
                    }));

                    if (json.data.logo) setPreview((p) => ({ ...p, logo: `${process.env.NEXT_PUBLIC_DOCUMENTS_BASE_URL}${json.data.logo}` }));
                    if (json.data.favicon) setPreview((p) => ({ ...p, favicon: `${process.env.NEXT_PUBLIC_DOCUMENTS_BASE_URL}${json.data.favicon}` }));
                }
            } catch (err) {
                console.error('Failed to load settings', err);
            }
        }

        fetchSettings();
    }, []);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setForm((prev) => ({ ...prev, [name]: value }));
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, files } = e.target;
        if (files?.[0]) {
            setForm((prev) => ({ ...prev, [name]: files[0] }));
            const url = URL.createObjectURL(files[0]);
            setPreview((prev) => ({ ...prev, [name]: url }));
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);

        try {
            const formData = new FormData();
            Object.entries(form).forEach(([key, value]) => {
                if (value !== null && value !== undefined) {
                    formData.append(key, value as any);
                }
            });

            const res = await fetch(API_ENDPOINTS.SETTINGS, {
                method: 'POST',
                credentials: 'include',
                body: formData,
            });

            const result = await res.json();
            if (res.ok) {
                showMessage('Settings saved successfully!');
            } else {
                showMessage(result?.message || 'Failed to save settings.', 'error');
            }
        } catch (err) {
            console.error('Error submitting settings:', err);
            showMessage('Error occurred while saving.', 'error');
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <DefaultPageLayout>
                <BreadCrums
                    mainHeading="Settings"
                    breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Application Settings' }]}
                    ButonComponent={
                        <BreadCrumButton
                            onClick={() => {
                                push('/settings');
                            }}
                        />
                    }
                />

                {/* Header Section - Made Responsive */}
                <div className="mb-5 flex flex-col justify-end gap-4 px-4 pt-5 lg:flex-row lg:items-center lg:px-5">
                    <form onSubmit={handleSubmit} className="mt-10 w-full space-y-6 px-4 lg:px-6">
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            {/* App Name */}
                            <div>
                                <label className="mb-1 block text-sm font-medium text-gray-700">App Name</label>
                                <input
                                    type="text"
                                    name="app_name"
                                    value={form.app_name}
                                    onChange={handleChange}
                                    placeholder="Enter app name"
                                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                                />
                            </div>

                            <div>
                                <label className="mb-1 block text-sm font-medium text-gray-700">Copyright</label>
                                <input
                                    type="text"
                                    name="copyright_text"
                                    value={form.copyright_text}
                                    onChange={handleChange}
                                    placeholder="Enter copyright"
                                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                                />
                            </div>

                            {/* Monthly Discount Type & Value */}
                            <div>
                                <label className="mb-1 block text-sm font-medium text-gray-700">Monthly Discount Type</label>
                                <select
                                    name="monthly_discount_type"
                                    value={form.monthly_discount_type}
                                    onChange={handleChange}
                                    className="w-full appearance-none rounded-md border border-gray-300 bg-white px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                                >
                                    <option value="percentage">Percentage</option>
                                    <option value="fixed">Fixed</option>
                                </select>
                            </div>

                            <div>
                                <label className="mb-1 block text-sm font-medium text-gray-700">Monthly Discount Value</label>
                                <input
                                    type="number"
                                    name="monthly_discount_value"
                                    value={form.monthly_discount_value}
                                    onChange={handleChange}
                                    placeholder="e.g., 20"
                                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                                />
                            </div>

                            <div>
                                <label className="mb-1 block text-sm font-medium text-gray-700">Monthly Coupon Code</label>
                                <input
                                    type="text"
                                    name="monthly_coupon_code"
                                    value={form.monthly_coupon_code}
                                    onChange={handleChange}
                                    placeholder="e.g., SAVE20"
                                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                                />
                            </div>

                            {/* Yearly Discount Type & Value */}
                            <div>
                                <label className="mb-1 block text-sm font-medium text-gray-700">Yearly Discount Type</label>
                                <select
                                    name="yearly_discount_type"
                                    value={form.yearly_discount_type}
                                    onChange={handleChange}
                                    className="w-full appearance-none rounded-md border border-gray-300 bg-white px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                                >
                                    <option value="percentage">Percentage</option>
                                    <option value="fixed">Fixed</option>
                                </select>
                            </div>

                            <div>
                                <label className="mb-1 block text-sm font-medium text-gray-700">Yearly Discount Value</label>
                                <input
                                    type="number"
                                    name="yearly_discount_value"
                                    value={form.yearly_discount_value}
                                    onChange={handleChange}
                                    placeholder="e.g., 100"
                                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                                />
                            </div>

                            <div>
                                <label className="mb-1 block text-sm font-medium text-gray-700">Yearly Coupon Code</label>
                                <input
                                    type="text"
                                    name="yearly_coupon_code"
                                    value={form.yearly_coupon_code}
                                    onChange={handleChange}
                                    placeholder="e.g., YEARLY100"
                                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                                />
                            </div>

                            {/* Logo Upload */}
                            <div>
                                <label className="mb-1 block text-sm font-medium text-gray-700">Logo</label>
                                <input type="file" name="logo" accept="image/*" onChange={handleFileChange} className="block text-sm text-gray-700" />
                                {preview.logo && <img src={preview.logo} alt="Logo Preview" className="mt-2 h-16 object-contain" />}
                            </div>

                            {/* Favicon Upload */}
                            <div>
                                <label className="mb-1 block text-sm font-medium text-gray-700">Favicon</label>
                                <input type="file" name="favicon" accept="image/*" onChange={handleFileChange} className="block text-sm text-gray-700" />
                                {preview.favicon && <img src={preview.favicon} alt="Favicon Preview" className="mt-2 h-10 object-contain" />}
                            </div>
                        </div>

                        {/* Submit Button */}
                        <div className="flex justify-end">
                            <button type="submit" disabled={loading} className="rounded-md bg-[#1D7EB6] px-6 py-2 text-sm font-medium text-white hover:bg-[#1D7EB6]/90 disabled:opacity-50">
                                {loading ? 'Saving...' : 'Save Settings'}
                            </button>
                        </div>
                    </form>
                </div>
            </DefaultPageLayout>
        </>
    );
}
