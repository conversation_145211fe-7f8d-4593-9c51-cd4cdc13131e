'use client';
 
import { CallNowIcon, CloseIconModel } from '@/components/icon/Icon';
import CallButton from '../CallButton';

const LetsConnectModel = ({ onClose, onSuccessfulDelete }: any) => {
    const handleDelete = () => {
        onClose(true);
    };
    return (
        <>
            <div className=" w-full">
                <div className=" relative flex items-center justify-between ">
                    <div className=" relative flex items-start justify-start "></div>
                    <div className=" relative flex items-end justify-end ">
                        <span className="cursor-pointer   " onClick={handleDelete}>
                            <CloseIconModel />
                        </span>
                    </div>
                </div>
                <div className="h-100 flex flex-col justify-between px-4 py-5">
                    <div className=" ">
                        <div className="flex items-center justify-center  ">
                            <CallNowIcon />
                        </div>
                        <div className=" py-5 text-center">
                            <h3 className="justify-start">
                                <span className="font-inter text-2xl font-semibold text-zinc-800">Let’s </span>
                                <span className="text-2xl font-semibold text-orange-800  ">Connect</span>
                            </h3>
                            <p className="justify-start px-10 self-stretch text-center font-inter text-base font-normal leading-normal text-zinc-600">
                                Choose an option to connect with the agent and discuss your requirements.
                            </p>
                        </div>
                        <div className="grid grid-cols-2 gap-4 pt-5 max-md:grid-cols-1">
                        <CallButton onClick={() =>  {}} text="Live Chat"  className={"py-5 text-lg "}/>
                        <CallButton onClick={() =>  {}} text="WhatsApp"  className={"py-5 text-lg "}/>
                        <CallButton onClick={() =>  {}} text="Email"className={"py-5 text-lg "}/>
                        <CallButton onClick={() =>  {}} text="Call" className={"py-5 text-lg "}/>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default LetsConnectModel;
