'use client';

import React from 'react';
import { useState, useRef, useEffect } from 'react';
import SearchDropDown from './SearchDropDown';
import AddSalespersonPopup from './AddSalespersonPopup';
import SalespersonsTab from './SalespersonsTab';

// Add type definitions at the top of the file
interface Client {
    name: string;
    email: string;
}

interface Salesperson {
    name: string;
    id: string;
}

interface Commission {
    amount: string;
    rate: string;
}

interface Dates {
    referred: string;
    converted: string;
    paid?: string;
}

interface Referral {
    client: Client;
    salesperson: Salesperson;
    plan: string;
    value: string;
    commission: Commission;
    status: string;
    dates: Dates;
}

export default function ReferralManagementData() {
    const [activeTab, setActiveTab] = useState('overview');
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('All Status');
    const [monthFilter, setMonthFilter] = useState('All Months');
    const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
    const [notification, setNotification] = useState<{ message: string; subMessage: string } | null>(null);

    // Add pagination states
    const [currentReferralPage, setCurrentReferralPage] = useState(1);
    const [referralsPerPage, setReferralsPerPage] = useState(10);

    const dropdownRef = useRef<HTMLDivElement>(null);
    const [isStatusOpen, setIsStatusOpen] = useState(false);
    const [isMonthOpen, setIsMonthOpen] = useState(false);
    const statusDropdownRef = useRef<HTMLDivElement>(null);
    const monthDropdownRef = useRef<HTMLDivElement>(null);

    // Add state for the popup
    const [isAddSalespersonOpen, setIsAddSalespersonOpen] = useState(false);

    // Salespersons Data
    const salespersonsData = [
        {
            name: 'Ahmed Hassan',
            email: '<EMAIL>',
            phone: '+971501234567',
            referralId: 'REF001',
            commissionRate: '10%',
            referrals: 15,
            totalCommission: {
                total: '$2,400',
                paid: '$1,950',
            },
            pending: '$450',
            status: 'active',
        },
        {
            name: 'Sarah Al-Zahra',
            email: '<EMAIL>',
            phone: '+971529876543',
            referralId: 'REF002',
            commissionRate: '8%',
            referrals: 22,
            totalCommission: {
                total: '$3,200',
                paid: '$2,880',
            },
            pending: '$320',
            status: 'active',
        },
        {
            name: 'Mohammed Ali',
            email: '<EMAIL>',
            phone: '+971503334444',
            referralId: 'REF003',
            commissionRate: '12%',
            referrals: 18,
            totalCommission: {
                total: '$2,800',
                paid: '$2,500',
            },
            pending: '$300',
            status: 'active',
        },
        {
            name: 'Fatima Khan',
            email: '<EMAIL>',
            phone: '+971567778888',
            referralId: 'REF004',
            commissionRate: '9%',
            referrals: 12,
            totalCommission: {
                total: '$1,900',
                paid: '$1,600',
            },
            pending: '$300',
            status: 'active',
        },
        {
            name: 'Omar Rahman',
            email: '<EMAIL>',
            phone: '+971542223333',
            referralId: 'REF005',
            commissionRate: '11%',
            referrals: 20,
            totalCommission: {
                total: '$3,100',
                paid: '$2,800',
            },
            pending: '$300',
            status: 'inactive',
        },
        {
            name: 'Layla Ahmed',
            email: '<EMAIL>',
            phone: '+971589994444',
            referralId: 'REF006',
            commissionRate: '10%',
            referrals: 16,
            totalCommission: {
                total: '$2,600',
                paid: '$2,300',
            },
            pending: '$300',
            status: 'active',
        },
        {
            name: 'Hassan Malik',
            email: '<EMAIL>',
            phone: '+971551112222',
            referralId: 'REF007',
            commissionRate: '9%',
            referrals: 14,
            totalCommission: {
                total: '$2,200',
                paid: '$1,900',
            },
            pending: '$300',
            status: 'active',
        },
        {
            name: 'Noor Ali',
            email: '<EMAIL>',
            phone: '+971563334444',
            referralId: 'REF008',
            commissionRate: '10%',
            referrals: 19,
            totalCommission: {
                total: '$2,900',
                paid: '$2,600',
            },
            pending: '$300',
            status: 'active',
        },
        {
            name: 'Zainab Hussein',
            email: '<EMAIL>',
            phone: '+971523334444',
            referralId: 'REF009',
            commissionRate: '8%',
            referrals: 11,
            totalCommission: {
                total: '$1,800',
                paid: '$1,500',
            },
            pending: '$300',
            status: 'inactive',
        },
        {
            name: 'Karim Shah',
            email: '<EMAIL>',
            phone: '+971599998888',
            referralId: 'REF010',
            commissionRate: '11%',
            referrals: 17,
            totalCommission: {
                total: '$2,700',
                paid: '$2,400',
            },
            pending: '$300',
            status: 'active',
        },
    ];

    // Add pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const itemsPerPageOptions = [10, 20, 30];

    // Calculate pagination values
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = salespersonsData.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(salespersonsData.length / itemsPerPage);

    // Handle page change
    const handlePageChange = (pageNumber: number) => {
        setCurrentPage(pageNumber);
    };

    // Handle items per page change
    const handleItemsPerPageChange = (value: number) => {
        setItemsPerPage(value);
        setCurrentPage(1); // Reset to first page when changing items per page
    };

    // Copy to clipboard function
    const copyToClipboard = async (text: string) => {
        try {
            await navigator.clipboard.writeText(text);
            setNotification({
                message: 'Copied!',
                subMessage: `Referral ID ${text} copied to clipboard`,
            });
        } catch (err) {
            setNotification({
                message: 'Failed to copy',
                subMessage: 'Please try again',
            });
        }
    };

    // Export to CSV function
    const exportToCSV = () => {
        // Define CSV headers
        const headers = ['Name', 'Email', 'Phone', 'Referral ID', 'Commission Rate', 'Referrals', 'Total Commission', 'Paid Commission', 'Pending', 'Status'].join(',');

        // Convert data to CSV rows
        const rows = salespersonsData.map((person) =>
            [
                person.name,
                person.email,
                person.phone,
                person.referralId,
                person.commissionRate,
                person.referrals,
                person.totalCommission.total,
                person.totalCommission.paid,
                person.pending,
                person.status,
            ].join(',')
        );

        // Combine headers and rows
        const csvContent = [headers, ...rows].join('\n');

        // Create blob and download
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'salespersons_data.csv');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setNotification({
            message: 'Export Successful',
            subMessage: 'Salespersons data has been downloaded',
        });
    };

    // Close dropdowns when clicking outside
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target as Node)) {
                setIsStatusOpen(false);
            }
            if (monthDropdownRef.current && !monthDropdownRef.current.contains(event.target as Node)) {
                setIsMonthOpen(false);
            }
        }

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Auto-hide notification after 3 seconds
    useEffect(() => {
        if (notification) {
            const timer = setTimeout(() => {
                setNotification(null);
            }, 3000);
            return () => clearTimeout(timer);
        }
    }, [notification]);

    // Stats for Overview
    const stats = {
        totalSalespersons: {
            value: '2',
            label: 'Total Salespersons',
            subtext: '2 active',
        },
        totalReferrals: {
            value: '2',
            label: 'Total Referrals',
            subtext: '2 confirmed',
        },
        commissionsPaid: {
            value: '$4,830',
            label: 'Commissions Paid',
            subtext: '$770 pending',
        },
        conversionRate: {
            value: '100.0%',
            label: 'Conversion Rate',
            subtext: '$2,415.00 avg commission',
        },
    };

    // Top Performers Data
    const topPerformers = [
        {
            id: 'REF002',
            name: 'Sarah Al-Zahra',
            amount: '$3,200',
            referrals: '22 referrals',
            rank: 1,
        },
        {
            id: 'REF001',
            name: 'Ahmed Hassan',
            amount: '$2,400',
            referrals: '15 referrals',
            rank: 2,
        },
    ];

    // Recent Referrals Data
    const recentReferrals = [
        {
            name: 'Mohammed Al-Rashid',
            referredBy: 'Ahmed Hassan',
            type: 'Premium',
            status: 'confirmed',
            amount: '$29.90',
        },
        {
            name: 'Fatima Al-Mansoori',
            referredBy: 'Sarah Al-Zahra',
            type: 'Enterprise',
            status: 'paid',
            amount: '$71.92',
        },
    ];

    // Add Referral Data
    const referralData: Referral[] = [
        {
            client: {
                name: 'Mohammed Al-Rashid',
                email: '<EMAIL>',
            },
            salesperson: {
                name: 'Ahmed Hassan',
                id: 'REF001',
            },
            plan: 'Premium',
            value: '$299',
            commission: {
                amount: '$29.90',
                rate: '10% rate',
            },
            status: 'confirmed',
            dates: {
                referred: '2025-01-15',
                converted: '2025-01-16',
            },
        },
        {
            client: {
                name: 'Fatima Al-Mansoori',
                email: '<EMAIL>',
            },
            salesperson: {
                name: 'Sarah Al-Zahra',
                id: 'REF002',
            },
            plan: 'Enterprise',
            value: '$899',
            commission: {
                amount: '$71.92',
                rate: '8% rate',
            },
            status: 'paid',
            dates: {
                referred: '2025-01-10',
                converted: '2025-01-12',
                paid: '2025-01-20',
            },
        },
    ];

    // Add Plans options for Referrals filter
    const plansDropdownOptions = [{ label: 'All Plans' }, { label: 'Basic' }, { label: 'Premium' }, { label: 'Enterprise' }];

    const handleActionClick = (personId: string, event: React.MouseEvent) => {
        event.stopPropagation();
        setActiveDropdown(activeDropdown === personId ? null : personId);
    };

    const handleViewDetails = (person: any) => {
        // setShowViewDetails(person.referralId); // This state was removed
        setActiveDropdown(null);
        setNotification({
            message: 'View Details',
            subMessage: `Viewing details for ${person.name}`,
        });
    };

    const handleEditSalesperson = (person: any) => {
        // setShowEditSalesperson(person.referralId); // This state was removed
        setActiveDropdown(null);
        setNotification({
            message: 'Edit Salesperson',
            subMessage: `Editing ${person.name}`,
        });
    };

    const handleDeactivate = (person: any) => {
        // Update the person's status in your data
        const updatedData = salespersonsData.map((p) => (p.referralId === person.referralId ? { ...p, status: 'inactive' } : p));
        // In a real app, you would update this through an API
        setActiveDropdown(null);
        setNotification({
            message: 'Status Updated',
            subMessage: 'Salesperson status changed to inactive',
        });
    };

    // Define dropdown options
    const statusDropdownOptions = [{ label: 'All Status' }, { label: 'Pending' }, { label: 'Active' }, { label: 'Inactive' }];

    const monthDropdownOptions = [
        { label: 'All Months' },
        { label: 'January' },
        { label: 'February' },
        { label: 'March' },
        { label: 'April' },
        { label: 'May' },
        { label: 'June' },
        { label: 'July' },
        { label: 'August' },
        { label: 'September' },
        { label: 'October' },
        { label: 'November' },
        { label: 'December' },
    ];

    // Add new handlers for referral actions
    const handleViewReferralDetails = (referral: Referral) => {
        setActiveDropdown(null);
        setNotification({
            message: 'View Details',
            subMessage: `Viewing details for referral ${referral.client.name}`,
        });
    };

    const handleEditReferral = (referral: Referral) => {
        setActiveDropdown(null);
        setNotification({
            message: 'Edit Referral',
            subMessage: `Editing referral for ${referral.client.name}`,
        });
    };

    const handleGenerateInvoice = (referral: Referral) => {
        setActiveDropdown(null);
        setNotification({
            message: 'Generate Invoice',
            subMessage: `Generating invoice for ${referral.client.name}`,
        });
    };

    // Calculate pagination for referrals
    const indexOfLastReferral = currentReferralPage * referralsPerPage;
    const indexOfFirstReferral = indexOfLastReferral - referralsPerPage;
    const currentReferrals = referralData.slice(indexOfFirstReferral, indexOfLastReferral);
    const totalReferralPages = Math.ceil(referralData.length / referralsPerPage);

    // Handle referral page change
    const handleReferralPageChange = (pageNumber: number) => {
        setCurrentReferralPage(pageNumber);
    };

    // Handle referrals per page change
    const handleReferralsPerPageChange = (value: number) => {
        setReferralsPerPage(value);
        setCurrentReferralPage(1);
    };

    // Add handler for form submission
    const handleAddSalesperson = (data: any) => {
        setNotification({
            message: 'Success',
            subMessage: 'New salesperson has been added successfully',
        });
    };

    return (
        <div className="min-h-screen bg-[#f8f9fa] p-6">
            {/* Header Section */}
            <div className="mb-8 flex items-center justify-between">
                <div>
                    <h1 className="font-inter text-2xl font-semibold text-[#2d2d2e]">Referral Management</h1>
                    <p className="mt-1 text-[#636363]">Manage salespersons, track referrals, and calculate commissions</p>
                </div>

                {activeTab === 'salespersons' && (
                    <button
                        onClick={() => setIsAddSalespersonOpen(true)}
                        className="flex items-center gap-2 rounded-md border border-[#1D7EB6] bg-[#1D7EB6] px-4 py-3 text-sm font-medium text-white transition hover:bg-[#166da0]"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        Add Salesperson
                    </button>
                )}
            </div>

            {/* Navigation Tabs */}
            <div className="mb-8 border-b border-[#e4e4e4]">
                <nav className="-mb-px flex space-x-8">
                    <button
                        onClick={() => setActiveTab('overview')}
                        className={`border-b-2 px-1 pb-4 text-sm font-medium ${
                            activeTab === 'overview' ? 'border-[#1D7EB6] text-[#1D7EB6]' : 'border-transparent text-[#636363] hover:border-[#636363] hover:text-[#2d2d2e]'
                        }`}
                    >
                        Overview
                    </button>
                    <button
                        onClick={() => setActiveTab('salespersons')}
                        className={`border-b-2 px-1 pb-4 text-sm font-medium ${
                            activeTab === 'salespersons' ? 'border-[#1D7EB6] text-[#1D7EB6]' : 'border-transparent text-[#636363] hover:border-[#636363] hover:text-[#2d2d2e]'
                        }`}
                    >
                        Salespersons
                    </button>
                    <button
                        onClick={() => setActiveTab('referrals')}
                        className={`border-b-2 px-1 pb-4 text-sm font-medium ${
                            activeTab === 'referrals' ? 'border-[#1D7EB6] text-[#1D7EB6]' : 'border-transparent text-[#636363] hover:border-[#636363] hover:text-[#2d2d2e]'
                        }`}
                    >
                        Referrals
                    </button>
                </nav>
            </div>

            {/* Overview Content */}
            {activeTab === 'overview' && (
                <>
                    {/* Stats Cards */}
                    <div className="mb-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                        {Object.entries(stats).map(([key, stat]) => (
                            <div key={key} className="rounded-lg bg-white p-6 shadow-sm">
                                <div className="text-sm font-medium text-[#636363]">{stat.label}</div>
                                <div className="mt-2 text-3xl font-semibold text-[#2d2d2e]">{stat.value}</div>
                                <div className="mt-1 text-sm text-[#636363]">{stat.subtext}</div>
                            </div>
                        ))}
                    </div>

                    {/* Top Performers Section */}
                    <div className="mb-8 rounded-lg bg-white p-6">
                        <h2 className="mb-6 flex items-center gap-2 text-lg font-semibold text-[#2d2d2e]">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            Top Performers
                        </h2>
                        <div className="space-y-4">
                            {topPerformers.map((performer) => (
                                <div key={performer.id} className="flex items-center justify-between rounded-lg bg-gray-50 p-4">
                                    <div className="flex items-center gap-4">
                                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-sm font-medium text-blue-800">#{performer.rank}</div>
                                        <div>
                                            <div className="font-medium text-[#2d2d2e]">{performer.name}</div>
                                            <div className="text-sm text-[#636363]">ID: {performer.id}</div>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="font-semibold text-[#2d2d2e]">{performer.amount}</div>
                                        <div className="text-sm text-[#636363]">{performer.referrals}</div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Recent Referrals Section */}
                    <div className="rounded-lg bg-white p-6">
                        <h2 className="mb-6 flex items-center gap-2 text-lg font-semibold text-[#2d2d2e]">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Recent Referrals
                        </h2>
                        <div className="space-y-4">
                            {recentReferrals.map((referral, index) => (
                                <div key={index} className="flex items-center justify-between rounded-lg bg-gray-50 p-4">
                                    <div>
                                        <div className="font-medium text-[#2d2d2e]">{referral.name}</div>
                                        <div className="text-sm text-[#636363]">
                                            Referred by <span className="text-[#1D7EB6]">{referral.referredBy}</span> • {referral.type}
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="font-semibold text-[#2d2d2e]">{referral.amount}</div>
                                        <div className={`inline-block rounded-full px-2 py-1 text-xs ${referral.status === 'confirmed' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}`}>
                                            {referral.status}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </>
            )}

            {/* Salespersons Content */}
            {activeTab === 'salespersons' && <SalespersonsTab setIsAddSalespersonOpen={setIsAddSalespersonOpen} isAddSalespersonOpen={isAddSalespersonOpen} />}

            {/* Referrals Content */}
            {activeTab === 'referrals' && (
                <div className="space-y-6">
                    {/* Header with Export */}
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold text-[#2d2d2e]">Referral Records</h2>
                        <button onClick={exportToCSV} className="flex items-center gap-2 rounded-md border border-[#e4e4e4] px-4 py-2 text-sm font-medium text-[#636363] hover:bg-gray-50">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            Export ({referralData.length})
                        </button>
                    </div>

                    {/* Filters Section */}
                    <div className="rounded-lg bg-white p-6">
                        <div className="mb-4 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#636363]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                                />
                            </svg>
                            <span className="ml-2 font-medium text-[#2d2d2e]">Filters</span>
                        </div>
                        <div className="flex w-full flex-col flex-wrap items-center gap-5 md:flex-row">
                            <div className="w-full min-w-[287px] flex-1 md:max-w-[287px]">
                                <div className="relative">
                                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                        <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path
                                                fillRule="evenodd"
                                                d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                                clipRule="evenodd"
                                            />
                                        </svg>
                                    </div>
                                    <input
                                        type="text"
                                        placeholder="Search referrals..."
                                        className="h-14 w-full rounded-lg border border-[#e4e4e4] pl-10 pr-4 text-sm focus:border-[#1D7EB6] focus:outline-none"
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>
                            </div>
                            <div className="w-auto min-w-[287px] flex-1 md:max-w-[287px]">
                                <SearchDropDown classes="!h-14 w-full" dropdownOptions={statusDropdownOptions} initail="All Status" setSelectedStatus={setStatusFilter} />
                            </div>
                            <div className="w-auto min-w-[287px] flex-1 md:max-w-[287px]">
                                <SearchDropDown classes="!h-14 w-full" dropdownOptions={plansDropdownOptions} initail="All Plans" setSelectedStatus={setStatusFilter} />
                            </div>
                            <div className="w-auto min-w-[287px] flex-1 md:max-w-[287px]">
                                <SearchDropDown classes="!h-14 w-full" dropdownOptions={monthDropdownOptions} initail="All Months" setSelectedStatus={setMonthFilter} />
                            </div>
                        </div>
                    </div>

                    {/* Table */}
                    <div className="rounded-lg bg-white">
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead className="bg-gray-50 text-left">
                                    <tr>
                                        <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Client</th>
                                        <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Salesperson</th>
                                        <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Plan</th>
                                        <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Value</th>
                                        <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Commission</th>
                                        <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Status</th>
                                        <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Dates</th>
                                        <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Actions</th>
                                    </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-100">
                                    {currentReferrals.map((referral: Referral, index: number) => (
                                        <tr key={index} className="hover:bg-gray-50">
                                            <td className="px-6 py-4">
                                                <div>
                                                    <div className="font-medium text-[#2d2d2e]">{referral.client.name}</div>
                                                    <div className="text-sm text-[#636363]">{referral.client.email}</div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <div>
                                                    <div className="font-medium text-[#2d2d2e]">{referral.salesperson.name}</div>
                                                    <div className="text-sm text-[#636363]">{referral.salesperson.id}</div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <span
                                                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                        referral.plan === 'Premium' ? 'bg-purple-100 text-purple-800' : 'bg-orange-100 text-orange-800'
                                                    }`}
                                                >
                                                    {referral.plan}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 font-medium text-[#2d2d2e]">{referral.value}</td>
                                            <td className="px-6 py-4">
                                                <div className="font-medium text-[#2d2d2e]">{referral.commission.amount}</div>
                                                <div className="text-sm text-[#636363]">{referral.commission.rate}</div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <span
                                                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                        referral.status === 'confirmed' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                                                    }`}
                                                >
                                                    {referral.status}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="space-y-1 text-sm">
                                                    <div>Referred: {referral.dates.referred}</div>
                                                    <div>Converted: {referral.dates.converted}</div>
                                                    {referral.dates.paid && <div>Paid: {referral.dates.paid}</div>}
                                                </div>
                                            </td>
                                            <td className="relative px-6 py-4">
                                                <button onClick={(e) => handleActionClick(referral.client.name, e)} className="text-[#636363] hover:text-[#2d2d2e]">
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                        <circle cx="6" cy="10" r="2" />
                                                        <circle cx="10" cy="10" r="2" />
                                                        <circle cx="14" cy="10" r="2" />
                                                    </svg>
                                                </button>
                                                {activeDropdown === referral.client.name && (
                                                    <div ref={dropdownRef} className="absolute right-0 z-10 mt-2 w-48 rounded-md border border-gray-100 bg-white py-1 shadow-lg">
                                                        <button
                                                            onClick={() => handleViewReferralDetails(referral)}
                                                            className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                                <path
                                                                    strokeLinecap="round"
                                                                    strokeLinejoin="round"
                                                                    strokeWidth={2}
                                                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                                                />
                                                            </svg>
                                                            View Details
                                                        </button>
                                                        <button onClick={() => handleEditReferral(referral)} className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                                            <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path
                                                                    strokeLinecap="round"
                                                                    strokeLinejoin="round"
                                                                    strokeWidth={2}
                                                                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                                                />
                                                            </svg>
                                                            Edit Referral
                                                        </button>
                                                        <button onClick={() => handleGenerateInvoice(referral)} className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                                            <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path
                                                                    strokeLinecap="round"
                                                                    strokeLinejoin="round"
                                                                    strokeWidth={2}
                                                                    d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                                                />
                                                            </svg>
                                                            Generate Invoice
                                                        </button>
                                                    </div>
                                                )}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {/* Pagination Controls */}
                        <div className="flex items-center justify-between border-t border-gray-200 px-6 py-3">
                            <div className="flex items-center gap-2">
                                <span className="text-sm text-gray-700">Show</span>
                                <select value={referralsPerPage} onChange={(e) => handleReferralsPerPageChange(Number(e.target.value))} className="rounded border border-gray-300 px-2 py-1 text-sm">
                                    {[10, 20, 30].map((option) => (
                                        <option key={option} value={option}>
                                            {option}
                                        </option>
                                    ))}
                                </select>
                                <span className="text-sm text-gray-700">entries</span>
                            </div>

                            <div className="flex items-center gap-2">
                                <span className="text-sm text-gray-700">
                                    Showing {indexOfFirstReferral + 1} to {Math.min(indexOfLastReferral, referralData.length)} of {referralData.length} entries
                                </span>
                                <div className="flex gap-1">
                                    <button
                                        onClick={() => handleReferralPageChange(currentReferralPage - 1)}
                                        disabled={currentReferralPage === 1}
                                        className={`rounded border px-3 py-1 text-sm ${
                                            currentReferralPage === 1 ? 'cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400' : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                                        }`}
                                    >
                                        Previous
                                    </button>
                                    {Array.from({ length: totalReferralPages }, (_, i) => i + 1).map((number) => (
                                        <button
                                            key={number}
                                            onClick={() => handleReferralPageChange(number)}
                                            className={`rounded px-3 py-1 text-sm ${
                                                currentReferralPage === number ? 'bg-[#1D7EB6] text-white' : 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                                            }`}
                                        >
                                            {number}
                                        </button>
                                    ))}
                                    <button
                                        onClick={() => handleReferralPageChange(currentReferralPage + 1)}
                                        disabled={currentReferralPage === totalReferralPages}
                                        className={`rounded border px-3 py-1 text-sm ${
                                            currentReferralPage === totalReferralPages
                                                ? 'cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400'
                                                : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                                        }`}
                                    >
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Notification Toast */}
            {notification && (
                <div className="fixed bottom-4 right-4 z-50 w-80 rounded-lg bg-white p-4 shadow-lg">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="font-medium text-gray-900">{notification.message}</h3>
                            <p className="mt-1 text-sm text-gray-500">{notification.subMessage}</p>
                        </div>
                        <button onClick={() => setNotification(null)} className="text-gray-400 hover:text-gray-500">
                            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path
                                    fillRule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clipRule="evenodd"
                                />
                            </svg>
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
}
