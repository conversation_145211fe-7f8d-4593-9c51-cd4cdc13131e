'use client';
import React, { useEffect, useState } from 'react';
import { DateFilter } from './DateFilter';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import Loading from '@/components/layouts/loading';
import SearchDropDown from '@/components/reusable/SearchDropDown';
import { useRouter } from 'next/navigation';

const Application = () => {
    const dropdown = [{ label: 'All' }, { label: 'Pending' }, { label: 'Activated' }, { label: 'Rejected' }, { label: 'Incomplete' }, { label: 'Suspended' }];
    const [selectedStatus, setSelectedStatus] = useState('All');
    const [loader, setLoader] = useState(false);
    const [date, setDate] = useState({
        startDate: '',
        endDate: '',
    });
    const [data, setData] = useState({
        agentApplications: 0,
        agencyApplications: 0,
        advertisements: 0,
    });
    const { push } = useRouter();

    const handleDateChange = (startDate: string, endDate: string) => {
        setDate({
            startDate: startDate,
            endDate: endDate,
        });
    };

    const fetchData = async () => {
        try {
            const myHeaders = new Headers();
            setLoader(true);
            const requestOptions: RequestInit = {
                method: 'GET',
                headers: myHeaders,
                redirect: 'follow',
                credentials: 'include',
            };
            await fetch(API_ENDPOINTS.APPLICATION_COUNT + `?startDate=${date.startDate}&endDate=${date.endDate}&status=${selectedStatus}`, requestOptions)
                .then((response) => response.json())
                .then((result) => {
                    const formattedData = {
                        agentApplications: 0,
                        agencyApplications: 0,
                        advertisements: 0,
                    };

                    result.data.forEach((item: any) => {
                        if (item.type === 'agencies') {
                            formattedData.agencyApplications = Number(item.count);
                        } else if (item.type === 'agents') {
                            formattedData.agentApplications = Number(item.count);
                        }
                    });

                    setData(formattedData);
                    setLoader(false);
                })
                .catch((error) => {
                    console.error(error);
                    setLoader(false);
                });
            // setFetchedAgentApplications(data);
        } catch (error) {
            console.error(error);
            setLoader(false);
        }
    };

    // useEffect(() => {
    //     fetchData();
    // }, []);

    useEffect(() => {
        fetchData();
    }, [date, selectedStatus]);
    return (
        <>
            {loader && <Loading />}
            <div className="w-full gap-4 lg:w-[70%]">
                <div className="flex flex-col gap-4 rounded-lg border p-5 pb-7 pt-5 font-inter">
                    {/* Header */}
                    <div className="flex flex-wrap items-center justify-between gap-2">
                        <div className="">
                            <p className="flex flex-wrap text-[20px] font-semibold leading-7 md:text-[26px]">
                                <span className="text-redMain me-2">New Requests and Ad </span>
                                <span className="text-[#2d2d2e]">Status</span>
                            </p>

                            <div className="justify-start font-inter text-base font-normal leading-7 text-zinc-600">Newly requested applications and advertisements</div>
                        </div>
                        <div className="w-auto flex-1 sm:min-w-[287px] md:max-w-[287px]">
                            <SearchDropDown classes="!h-14 w-full" dropdownOptions={dropdown} initail={selectedStatus} setSelectedStatus={setSelectedStatus} />
                        </div>
                        <div className="sm:w-40 w-full">
                            <DateFilter onDateChange={handleDateChange} />
                        </div>
                    </div>
                    <div className="grid-row-3 grid gap-4 pt-5 sm:grid-cols-3">
                        <Card
                            value={data?.agentApplications}
                            label="Agent Applications"
                            onClick={() => push(`/dashboard/agents?status=${selectedStatus}&startDate=${date.startDate}&endDate=${date.endDate}&role=agent`)}
                        />
                        <Card
                            value={data?.agencyApplications}
                            label="Agency Applications"
                            onClick={() => push(`/dashboard/agents?status=${selectedStatus}&startDate=${date.startDate}&endDate=${date.endDate}&role=agency`)}
                        />
                        <Card value={data?.advertisements} label="Advertisements" />
                    </div>
                </div>
            </div>
        </>
    );
};

export default Application;

const Card = ({ value, label, onClick }: any) => (
    <div
        className="flex shrink grow basis-0 cursor-pointer flex-col items-center justify-center gap-2.5 rounded-[20px] border-2 border-[#e4e4e4] bg-neutral-100 p-5 shadow-[0px_4px_44px_-4px_rgba(12,12,13,0.05)] 2xl:p-10"
        onClick={onClick}
    >
        <div className=" flex  flex-col items-center justify-start gap-1.5">
            <div className="text-center font-golosText text-5xl font-semibold text-[#993333]   ">{value}</div>
            <div className="text-center text-base font-medium leading-relaxed  text-[#636363]">{label}</div>
        </div>
    </div>
);
