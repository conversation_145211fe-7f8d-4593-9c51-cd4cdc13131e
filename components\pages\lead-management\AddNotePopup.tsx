import API_ENDPOINTS from '@/app/lib/apiRoutes';
import { result } from 'lodash';
import React, { use, useEffect, useState } from 'react';

interface AddNotePopupProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (id: number, note: string) => void;
    companyName: string;
    id: number;
}

const AddNotePopup: React.FC<AddNotePopupProps> = ({ isOpen, onClose, onSave, companyName, id }) => {
  

    const [isSubmited, setIsSubmitted] = useState(false);
    const [notes, setNotes] = useState<string[]>([]);

    useEffect(() => {
        if (isOpen) setIsSubmitted(false);
    }, [isOpen]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitted(true);
        const formData = new FormData(e.target as HTMLFormElement);
        const note = formData.get('note') as string;
        onSave(id, note);
    };

    useEffect(() => {
        const fetchNotes = async () => {
            try {
                  const requestOptions: RequestInit = {
            method: 'GET',
            headers: new Headers(),
            credentials: 'include', 
        };
                const response = await fetch(API_ENDPOINTS.GET_NOTES+  id,requestOptions);
              
                const result = await response.json();
                if(result.success){
                    console.log('Notes fetched successfully:', result.data);
                setNotes(result.data.reverse());
                }
              
            } catch (error) {
                console.error('Error fetching notes:', error);
            }
        }
        fetchNotes()
    },[id ])

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);  
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
        });
    }

    return (
        <>
         {isOpen &&  <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 font-inter">
            <div className="relative w-full max-w-md rounded-lg bg-white">
                {/* Close button */}
                <button onClick={onClose} className="absolute right-3 top-3 text-gray-400 hover:text-gray-600">
                    ×
                </button>

                <div className="p-6">
                    <h2 className="mb-2 text-lg font-medium">Add Note</h2>
                    <p className="mb-6 text-sm text-gray-600">Add a note to {companyName}</p>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        {/* Note */}
                        <div>
                            <label htmlFor="note" className="mb-1 block text-sm font-medium text-gray-700">
                                Note
                            </label>
                            <textarea
                                id="note"
                                name="note"
                                placeholder="Enter your note here..."
                                rows={4}
                                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                                required
                            />
                        </div>

                        {/* Action Buttons */}
                        <div className="mt-6 flex justify-end gap-3">
                            <button type="button" onClick={onClose} className="rounded-md border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                                Cancel
                            </button>
                            <button
                                type="submit"
                                disabled={isSubmited}
                                className={`rounded-md px-4 py-2 text-sm font-medium text-white ${isSubmited ? 'cursor-not-allowed bg-red-400' : 'bg-red-600 hover:bg-red-700'}`}
                            >
                                {isSubmited ? 'Submitting…' : 'Add Note'}
                            </button>
                        </div>
                    </form>

                     { notes?.length >0 &&  
                     
                     <>
                       <p className="mb-6 text-sm text-gray-600">Previous notes:</p>
                      <div className='max-h-40 overflow-y-auto'>
                      

                                            {notes.map((note:any, index) => (
                                                <div key={index} className="mb-2 p-2 border rounded-md bg-gray-50">
                                                    <div className='flex justify-between mb-2'>

                                                    <p className="text-sm text-gray-800 font-semibold">Note {index + 1}:</p>         <p className="text-sm text-gray-600">    {note?.created_at ?  formatDate(note?.created_at)   : '—'}</p>
                                                    </div>
                                           
                                                    <p className="text-sm text-gray-700">{note?.note}</p>
                                                </div>
                                            ))}
                        </div>
                     </>
                    
                        
                        
                        
                        
                        
                        }
                    
                </div>
            </div>
        </div>}
        </>
     
    );
};

export default AddNotePopup;
