 
import ContentAnimation from '@/components/layouts/content-animation'; 
import Header from '@/components/layouts/header';
import MainContainer from '@/components/layouts/main-container';
import Overlay from '@/components/layouts/overlay'; 
import Sidebar from '@/components/layouts/sidebar';
import Portals from '@/components/portals';

export default async function DashboardLayout({ children }: { children: React.ReactNode }) {
    return (
        <>
            <div className="relative">
                <Overlay />
                <MainContainer>
                    <Sidebar />
                    <Header />
                    <div className="xl:main-content flex min-h-screen flex-col">
                        <ContentAnimation>{children}</ContentAnimation>
                        <Portals />
                    </div>
                </MainContainer>
            </div>
        </>
    );
}
