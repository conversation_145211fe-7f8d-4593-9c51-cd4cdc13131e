import { AGENT_ACCOUNT_TYPE } from '../types/AgentProfile';
import { useState, useEffect } from 'react';

// Generate table data
export const generateDocumentTableData = (documents: any[]) => {
    const tableData: any[] = [];
    let docId = 1;

    documents.forEach((doc) => {
        let agentName = '';

        if (doc.profileAccountType === AGENT_ACCOUNT_TYPE.INDIVIDUAL) {
            agentName = `${doc.firstName || ''} ${doc.middleName || ''} ${doc.lastName || ''}`.trim();
        } else {
            agentName = `${doc.name || ''}`.trim();
        }

        const finalImages = Array.isArray(doc.final_images) ? doc.final_images : [];

        finalImages.forEach((img: any) => {
            const docType = getTypeFromUrl(img.url);

            tableData.push({
                id: docId++,
                imgId: img.id,
                imgUrl: `${process.env.NEXT_PUBLIC_DOCUMENTS_BASE_URL}${img.url}`,
                extension: getExtensionFromUrl(img.url),
                document: img.title || img.url.split('/').pop() || 'Unknown Document',
                agent: agentName,
                type: docType,
                status: img.statusName,
                uploadDateRaw: new Date(img.createdOn), // use for sorting
                uploadDate: new Date(img.createdOn).toLocaleDateString('en-GB', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric',
                }),
                expiryDate: img.expiryDate
                    ? new Date(img.expiryDate).toLocaleDateString('en-GB', {
                          day: '2-digit',
                          month: 'short',
                          year: 'numeric',
                      })
                    : '-',
                size: formatSize(img.size) || '0 MB',
            });
        });
    });

    // 🔽 Sort tableData by uploadDateRaw (latest first)
    tableData.sort((a, b) => b.uploadDateRaw.getTime() - a.uploadDateRaw.getTime());

    // 🧹 Optionally remove raw date after sorting
    return tableData.map(({ uploadDateRaw, ...rest }) => rest);
};

// Helper to extract document type from URL (basic keyword matching)
const getTypeFromUrl = (url: string): string => {
    const lower = url.toLowerCase();

    if (lower.includes('license')) return 'License';
    if (lower.includes('passport')) return 'Passport';
    if (lower.includes('visa')) return 'Visa';
    if (lower.includes('emiratesid') || lower.includes('emiratesid')) return 'Emirates ID';
    if (lower.includes('employment') || lower.includes('contract')) return 'Proof of Employment';
    if (lower.includes('support')) return 'Supporting Document';
    if (lower.includes('companyLogo')) return 'Company Logo';
    if (lower.includes('professionalPhoto')) return 'Professional Photo';

    return 'Other';
};

const formatSize = (bytes: any) => {
    if (bytes >= 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    if (bytes >= 1024) return (bytes / 1024).toFixed(2) + ' KB';
    return bytes + ' bytes';
};

const getExtensionFromUrl = (url: string) => {
    const lastSegment = url.split('/').pop(); // get the filename
    if (lastSegment && lastSegment.includes('.')) {
        return lastSegment?.split('.')?.pop()?.toLowerCase();
    }
    return '';
};

// use this in debouncing effect.
export function useDebouncedValue(value: string, delay: number = 300): string {
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(timer); // Cleanup timeout if value changes
        };
    }, [value, delay]);

    return debouncedValue;
}

// Get pagination range
export function getPaginationRange(currentPage: number, totalPages: number, maxButtons: number = 3): number[] {
    const range = [];
    let start = Math.max(currentPage - 1, 0);
    let end = Math.min(currentPage + 1, totalPages - 1);

    if (end - start < maxButtons - 1) {
        if (start === 0) {
            end = Math.min(start + maxButtons - 1, totalPages - 1);
        } else {
            start = Math.max(end - (maxButtons - 1), 0);
        }
    }

    for (let i = start; i <= end; i++) {
        range.push(i);
    }

    return range;
}

export const statusOptions = [
    { label: 'New', value: 'new' },
    { label: 'Contacted', value: 'contacted' },
    { label: 'Qualified', value: 'qualified' },
    { label: 'Lost', value: 'lost' },
    { label: 'Converted', value: 'converted' },
    { label: 'Re-opened', value: 're-opened' },
];

export const leadTypeOptions = [
    { label: 'Agent', value: 'agent' },
    { label: 'Agency', value: 'agency' },
];
