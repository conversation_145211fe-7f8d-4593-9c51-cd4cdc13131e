import React from 'react';

interface ConvertToUserPopupProps {
    isOpen: boolean;
    onClose: () => void;
    onConvert: (userType: string) => void;
    companyName: string;
}

const ConvertToUserPopup: React.FC<ConvertToUserPopupProps> = ({
    isOpen,
    onClose,
    onConvert,
    companyName
}) => {
    if (!isOpen) return null;

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const formData = new FormData(e.target as HTMLFormElement);
        onConvert(formData.get('userType') as string);
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 font-inter">
            <div className="bg-white rounded-lg w-full max-w-md relative">
                {/* Close button */}
                <button 
                    onClick={onClose}
                    className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                >
                    ×
                </button>

                <div className="p-6">
                    <h2 className="text-lg font-medium mb-2">Convert Lead to User</h2>
                    <p className="text-sm text-gray-600 mb-6">
                        Convert {companyName} from a lead to an active user in the system.
                    </p>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        {/* User Type */}
                        <div>
                            <label htmlFor="userType" className="block text-sm font-medium text-gray-700 mb-1">
                                User Type
                            </label>
                            <select
                                id="userType"
                                name="userType"
                                defaultValue="Web User"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#1D7EB6] focus:border-[#1D7EB6]"
                                required
                            >
                                <option value="Web User">Web User</option>
                                <option value="Admin User">Admin User</option>
                            </select>
                        </div>

                        {/* Info Text */}
                        <p className="text-sm text-[#1D7EB6]">
                            This will create a new user account and move the lead to converted status.
                        </p>

                        {/* Action Buttons */}
                        <div className="flex justify-end gap-3 mt-6">
                            <button
                                type="button"
                                onClick={onClose}
                                className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 border border-gray-300 rounded-md"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                className="px-4 py-2 text-sm font-medium text-white bg-[#1D7EB6] hover:bg-[#1D7EB6]/90 rounded-md"
                            >
                                Convert to User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default ConvertToUserPopup; 