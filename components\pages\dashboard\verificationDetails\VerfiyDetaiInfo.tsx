import React, { useState } from 'react';
import Image from 'next/image';

import InputField from '@/components/reusable/InputField';

import { ButtonBorder } from '@/components/reusable/ButtonBorder';
import LabelInput from '@/components/reusable/LabelInput';

import { InfoIcon, MinusIcon, PlusIcon, RedInfoIcon, ToolTipIcon } from '@/components/icon/Icon';
import images from '@/public/assets/images/main/agent-image.png';
import { Dropdown } from '@/components/reusable/Dropdown';
import SelectDate from '@/components/reusable/DatePicker';
import NoExpiryCheckbox from '@/components/reusable/CheckboxEx';
import CustomFileUploader from '@/components/reusable/PdfButton';

export default function VerfiyDetailInfo() {
    const [disabled, setDisabled] = useState(false);
    return (
        <div className="mb-5 rounded-lg border p-5 font-inter">
            <div className="flex flex-col gap-4 pt-4 md:flex-row md:gap-0">
                <div className="flex flex-col gap-6 md:w-2/3">
                    <div>
                        <p className="pb-3 pl-3 text-lg font-bold text-[#2d2d2e]">Profile Information</p>
                        <div className="flex flex-wrap">
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <InputField id="username" label="Username*" value="mabdullah231" placeholder="Input" />
                            </div>
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <InputField id="account-name" label="Account Name*" value="M Abdullah" placeholder="Input" />
                            </div>
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <InputField id="contact" label="Contact*" value="+12 3456 789" placeholder="Input" />
                            </div>
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <InputField id="email" label="Email*" value="<EMAIL>" placeholder="Input" />
                            </div>
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <InputField id="location" label="Location*" value="Dubai" placeholder="Input" />
                            </div>

                            <div className="w-full px-2 py-2">
                                <InputField id="short-description" label="Short Description" value="Deals in rent, sale, and purchase of residential properties." placeholder="Input" />
                            </div>
                            <div className="w-full px-2 py-2">
                                <InputField id="short-description" label="Description" value="Lorem ipsum dolor sit amet consectetur. " placeholder="Input" />
                            </div>
                            <div className="w-full px-2 py-2">
                                <InputField id="address-line-1" label="Address Line 1*" value="Building 123, 21a Street, Al Qusais 3, Dubai." placeholder="Input" />
                            </div>
                            <div className="w-full px-2 py-2">
                                <InputField id="address-line-2" label="" value="" placeholder="Address Line 2" />
                            </div>
                        </div>
                    </div>
                    <div>
                        <p className="pb-3 pl-3 text-lg font-bold">Account Type and Association</p>
                        <div className="flex flex-wrap">
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <Dropdown label="Select industry*" />
                            </div>
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <Dropdown label="Select Type*" />
                            </div>
                        </div>
                        <div className="flex flex-wrap">
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <Dropdown label="Association*" />
                            </div>
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <Dropdown label="Associated Company Name*" />
                            </div>
                        </div>
                    </div>
                </div>
                <div className="pl-5 md:w-1/3">
                    <div className="ml-auto flex flex-col items-center gap-3 md:block md:max-w-[80%]">
                        <Image src={images} alt="agent-image" className="h-full" width={300} height={300}  />
                        <div className="w-auto pt-3 md:w-full">
                            <ButtonBorder value="Upload Photo" />
                        </div>
                        <div className="text-grayText flex items-center gap-2 py-3 pl-2 text-sm">
                            <RedInfoIcon className="h-5 w-5" />
                            <p>Upload 500x500 pixel images.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div className="w-full py-5 pl-2 pt-10 font-inter">
                <div className="pb-3 pl-3">
                    <p className=" pb-2 text-lg font-bold">Documents for Verification</p>
                    <p className="flex gap-1 pb-2">
                        UAE National ID (Front and Back) <ToolTipIcon />
                    </p>
                    <p className="text-base text-[#2D2D2E]">
                        To verify your identity, please provide clear, color scans or photographs of both the front and back of your National ID Card. Ensure that all details are legible, with no
                        blurriness, glare, or obstructions. The entire card, including all edges, should be visible in each image. Avoid using filters or editing the images. For best results, place
                        the card on a flat, well-lit surface and hold the camera steady when capturing the images. Once captured, save the images in PDF format and submit them for verification.
                        <CustomFileUploader />
                        <p className="flex gap-1 pt-4 pb-2">
                        Broker Card Issued by RERA <ToolTipIcon />
                    </p>
                    <CustomFileUploader />
                    </p>
                </div>
            </div>
        </div>
    );
}
