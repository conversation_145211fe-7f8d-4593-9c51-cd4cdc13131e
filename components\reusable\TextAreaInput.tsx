import React, { type FC, type ReactNode } from "react";

interface InputProps {
  id: string;
  label?: string;
  type?: string;
  placeholder?: string;
  className?: string;
  icon?: ReactNode;
  onChange?: React.ChangeEventHandler<HTMLTextAreaElement> ;
  value?: string;
  disable?: boolean;
  required?: boolean;
}

const TextAreaInput: FC<InputProps> = ({
  id,
  label,
  placeholder,
  icon,
  onChange,
  value,
  className = "",
  type = "text",
  disable,
  required
}) => {
  return (
    <div className="relative p">

      {label ? (
        <label
          htmlFor={id}
          className="  text-[#555555] text-xs font-normal px-1 absolute z-10 top-2 left-3"
        >
          {label}
        </label>
      ) : (
        ""
      )}
      <div className="relative">
        <textarea
          id={id}
          name={id}
          rows={8}
          placeholder={placeholder}
          className={`px-4 py-2  rounded-lg border text-black focus:outline-none font-normal w-full text-sm ${className}   ${label? "pt-6 " :""}`}
          onChange={onChange}
          value={value}
          disabled={disable}
          required={required}
        ></textarea>

      </div>
    </div>
  );
};

export default TextAreaInput;
