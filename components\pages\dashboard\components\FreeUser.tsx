'use client';
import React, { useState } from 'react';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import ProfileTabs from '../tabs/Tabs';
import Application from '../tabs/Application';
const FreeUser = () => {
    const [activeTab, setActiveTabs] = useState(1);
    return (
        <DefaultPageLayout>
            <BreadCrums mainHeading="Dashboard" breadcrumbs={[{ text: 'Dashboard' }]} />

            <ProfileTabs activeTab={activeTab} setActiveTabs={setActiveTabs} />

            <div className="px-5 pt-5">{activeTab === 1 ? <Application /> : null}</div>
            {/* {activeTab === 1 ? <ProfileDetails formData={profileData} fetchData={GetProfileData} isEditAble={isEditAble} setFormData={setProfileData} /> : null}
                {activeTab === 2 ? <ProfileProjects /> : null}
                {activeTab === 4 ? <ProfileCreateService /> : null}
                {activeTab === 5 ? <ProfileSaleAgent /> : null}
                {activeTab === 6 ? <ProfilePortfolio /> : null}
                {activeTab === 7 ? <ProfileReview /> : null} */}
        </DefaultPageLayout>
    );
};

export default FreeUser;
