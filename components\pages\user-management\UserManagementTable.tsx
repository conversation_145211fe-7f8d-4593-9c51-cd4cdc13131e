'use client';

import React from 'react';
import { useState, useRef, useEffect, MouseEvent } from 'react';
import { useRouter } from 'next/navigation';
import { DatatableAccendingSortingIcon, DatatableDeccendingSortingIcon, DatatableSortingIcon } from '@/components/icon/Icon';
import Modal from '@/components/reusable/modals/modal';
import SearchInput from '@/components/reusable/SearchBar';
import Loading from '@/components/layouts/loading';
import SearchDropDown from '@/components/reusable/SearchDropDown';
import {
    USERS_API,
    USERS_DOWNLOAD_CSV_API,
    USERS_ADD_USER_API,
    USERS_BULK_EMAIL_API,
    USERS_INDIVIDUAL_EMAIL_API,
    USERS_DEACTIVATE_API,
    USERS_RESET_PASSWORD_API,
    USERS_ADD_NOTE_API,
    USERS_GET_NOTES_API,
    USERS_ACTIVATE_API,
} from '@/app/lib/apiRoutes';
import { showMessage } from '@/app/lib/Alert';
import { Mail, RotateCcw, Trash2, X, Upload, FileText, NotebookPen, Eye } from 'lucide-react';
import { getAndDecryptCookie } from '@/app/lib/cookies';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
const columns = [
    { key: 'user', label: 'User', width: 'w-[25%]' },
    { key: 'type', label: 'Type', width: 'w-[10%]' },
    { key: 'status', label: 'Status', width: 'w-[10%]' },
    { key: 'lastLogin', label: 'Last Login', width: 'w-[15%]' },
    { key: 'joinDate', label: 'Join Date', width: 'w-[15%]' },
    { key: 'action', label: 'Actions', width: 'w-[25%]' },
];

const dropdown = [{ label: 'All Status' }, { label: 'Activated' }, { label: 'Deactivated' }, { label: 'Pending' }];

const dropdown2 = [
    { label: 'All User Types' },
    { label: 'Web User' },
    { label: 'Agent' },
    { label: 'Agency' },
    { label: 'Agency Admin' },
    // { label: 'Developer' },
    // { label: 'Super Admin' },
    { label: 'Sub-Admin' },
];

// Placeholder modal content for Bulk Email
interface ModalBasicProps {
    isOpen: boolean;
    onClose: () => void;
}

export interface BulkEmailModalProps extends ModalBasicProps {
    recipients: Array<{ name: string; email: string }>;
    isIndividual?: boolean;
    isLead?: boolean;
    onSubmitSuccess?: () => void;
}

interface UserDetailsModalProps extends ModalBasicProps {
    user: any; // Using 'any' for now, but ideally a more specific user type
}

interface ConfirmationModalProps extends ModalBasicProps {
    message: string;
    onConfirm: () => void;
    isLoading?: boolean;
}

// Format dates for display
const formatJoinedDate = (dateString: string) => {
    if (!dateString) return 'N/A';

    try {
        // Match format: dd/mm/yyyy hh:mm
        const [datePart, timePart] = dateString.split(' ');
        const [day, month, year] = datePart.split('/');

        // Construct ISO format: yyyy-mm-ddThh:mm
        const isoString = `${year}-${month}-${day}T${timePart || '00:00'}`;
        const date = new Date(isoString);

        if (isNaN(date.getTime())) {
            return 'N/A';
        }

        return date
            .toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short', // short month name like "Jun"
                year: 'numeric',
            })
            .replace(/ /g, '-'); // replace spaces with hyphens to get "dd-MMM-yyyy";
    } catch (e) {
        return 'Invalid Date';
    }
};

// New ConfirmationModal component
function ConfirmationModal({ isOpen, onClose, message, onConfirm, isLoading = false }: ConfirmationModalProps): JSX.Element {
    return (
        <Modal isOpen={isOpen} onClose={onClose} classes="!max-w-sm">
            <div className="p-6 text-center">
                <h2 className="mb-4 text-xl font-semibold">Confirm Action</h2>
                <p className="mb-6 text-gray-700">{message}</p>
                <div className="flex justify-center gap-3">
                    <button type="button" onClick={onClose} className="rounded border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-100" disabled={isLoading}>
                        Cancel
                    </button>
                    <button type="button" onClick={onConfirm} className="rounded bg-red-600 px-4 py-2 text-white hover:bg-red-700 disabled:opacity-50" disabled={isLoading}>
                        {isLoading ? 'Processing...' : 'Confirm'}
                    </button>
                </div>
            </div>
        </Modal>
    );
}

// Add a helper function to split and trim emails
function splitEmails(str: string) {
    return str.split(',').map(e => e.trim()).filter(Boolean);
}

// Add a reusable EmailChips component
function EmailChips({ emails, onRemove }: { emails: string[], onRemove: (email: string) => void }) {
    return (
        <div className="flex flex-wrap gap-2 mt-1 mb-2">
            {emails.map((email, idx) => (
                <span key={idx} className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                    {email}
                    <button type="button" className="ml-1 text-blue-500 hover:text-red-600" onClick={() => onRemove(email)}>
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
                    </button>
                </span>
            ))}
        </div>
    );
}

// Replace EmailChips with an inline chip input for To, CC, BCC fields
function EmailInputChips({ value, onChange, placeholder }: { value: string, onChange: (val: string) => void, placeholder: string }) {
    const inputRef = useRef<HTMLInputElement>(null);
    const emails = value.split(',').map(e => e.trim()).filter(Boolean);
    const [input, setInput] = useState('');

    // Simple email validation
    const isValidEmail = (email: string) => {
        return /^\S+@\S+\.\S+$/.test(email);
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setInput(e.target.value);
    };
    const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if ((e.key === 'Enter' || e.key === ',' || e.key === 'Tab' || e.key === ' ') && input.trim()) {
            e.preventDefault();
            if (!emails.includes(input.trim()) && isValidEmail(input.trim())) {
                onChange([...emails, input.trim()].join(', '));
            }
            setInput('');
        } else if (e.key === 'Backspace' && !input && emails.length > 0) {
            onChange(emails.slice(0, -1).join(', '));
        }
    };
    const handleInputBlur = () => {
        if (input.trim() && isValidEmail(input.trim()) && !emails.includes(input.trim())) {
            onChange([...emails, input.trim()].join(', '));
            setInput('');
        }
    };
    const removeEmail = (email: string) => {
        onChange(emails.filter(e => e !== email).join(', '));
    };
    return (
        <div className="flex flex-wrap items-center gap-1 px-2 py-1 border rounded min-h-[42px] bg-white focus-within:ring-2 focus-within:ring-blue-400">
            {emails.map((email, idx) => (
                <span key={idx} className="flex items-center bg-blue-100 text-blue-800 rounded-full px-2 py-1 text-xs mr-1 mb-1">
                    {email}
                    <button type="button" className="ml-1 text-blue-500 hover:text-red-600 focus:outline-none" onClick={() => removeEmail(email)}>
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
                    </button>
                </span>
            ))}
            <input
                ref={inputRef}
                className="flex-1 min-w-[120px] border-none outline-none py-1 px-2 text-sm bg-transparent"
                value={input}
                onChange={handleInputChange}
                onKeyDown={handleInputKeyDown}
                onBlur={handleInputBlur}
                placeholder={emails.length === 0 ? placeholder : ''}
            />
        </div> 
    );
}

// New BulkEmailModal (adapted from BulkEmailDialog.tsx)
export function BulkEmailModal({ isOpen, onClose, recipients = [], isIndividual = false, isLead = false, onSubmitSuccess }: BulkEmailModalProps): JSX.Element {
    const [to, setTo] = useState('');
    const [cc, setCc] = useState('');
    const [bcc, setBcc] = useState('');
    const [subject, setSubject] = useState('');
    const [message, setMessage] = useState('');
    const [attachments, setAttachments] = useState<File[]>([]);
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        console.log('BulkEmailModal recipients:', recipients, 'isOpen:', isOpen, 'isIndividual:', isIndividual);
        if (isOpen && recipients.length > 0) {
            if (isIndividual) {
                setTo(recipients.map(r => r.email).join(', '));
                setCc('');
                setBcc('');
            } else {
                setBcc(recipients.map(r => r.email).join(', '));
                setTo('');
                setCc('');
            }
        } else if (!isOpen) {
            setTo('');
            setCc('');
            setBcc('');
            setSubject('');
            setMessage('');
            setAttachments([]);
            setIsSubmitting(false);
        }
    }, [isOpen, recipients, isIndividual]);

    const handleSendEmail = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        let endpoint = '';
        let payload: any = {
            subject,
            message,
        };

        if (isIndividual) {
            if (!to.trim()) {
                showMessage('Please enter an email address in the To field', 'error');
                setIsSubmitting(false);
                return;
            }
            endpoint = USERS_INDIVIDUAL_EMAIL_API;
            payload.to = to.split(',').map(email => email.trim());
            if (cc.trim()) payload.cc = cc.split(',').map(email => email.trim());
            if (bcc.trim()) payload.bcc = bcc.split(',').map(email => email.trim());
        } else {
            if (!bcc.trim()) {
                showMessage('Please enter email addresses in the BCC field', 'error');
                setIsSubmitting(false);
                return;
            }
            endpoint = USERS_BULK_EMAIL_API;
            payload.bcc = bcc.split(',').map(email => email.trim());
            if (to.trim()) payload.to = to.split(',').map(email => email.trim());
            if (cc.trim()) payload.cc = cc.split(',').map(email => email.trim());
        }

        const formData = new FormData();
        formData.append('subject', subject);
        formData.append('message', message);

        formData.append('isLead', String(isLead));

        // if (isIndividual) {
        //     formData.append('to', JSON.stringify(payload.to));
        // } else {
        //     formData.append('bcc', JSON.stringify(payload.bcc));
        // }
        
        // if (payload.to) formData.append('to', JSON.stringify(payload.to));
        // if (payload.cc) formData.append('cc', JSON.stringify(payload.cc));
        // if (payload.bcc) formData.append('bcc', JSON.stringify(payload.bcc));
        
        formData.append('to', JSON.stringify(payload.to || []));
        formData.append('cc', JSON.stringify(payload.cc || []));
        formData.append('bcc', JSON.stringify(payload.bcc || []));

        attachments.forEach((file) => {
            formData.append('attachments', file);
        });

        console.log(`Sending ${isIndividual ? 'individual' : 'bulk'} email:`, payload);

        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                body: formData,
                credentials: 'include', // Important for sending cookies
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || `Failed to send ${isIndividual ? 'individual' : 'bulk'} email.`);
            }

            const result = await response.json();
            showMessage(result.message || `Email sent successfully!`, 'success');
            onSubmitSuccess?.();
            onClose();
        } catch (error: any) {
            console.error(`Error sending ${isIndividual ? 'individual' : 'bulk'} email:`, error);
            showMessage(error.message || `Failed to send ${isIndividual ? 'individual' : 'bulk'} email.`, 'error');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files) {
            const newFiles = Array.from(files);
            setAttachments((prev) => [...prev, ...newFiles]);
        }
    };

    const removeAttachment = (index: number) => {
        setAttachments((prev) => prev.filter((_, i) => i !== index));
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Remove email from a field
    const removeEmail = (field: 'to' | 'cc' | 'bcc', email: string) => {
        if (field === 'to') setTo(splitEmails(to).filter(e => e !== email).join(', '));
        if (field === 'cc') setCc(splitEmails(cc).filter(e => e !== email).join(', '));
        if (field === 'bcc') setBcc(splitEmails(bcc).filter(e => e !== email).join(', '));
    };

    return (
        <Modal isOpen={isOpen} onClose={onClose} classes="">
            <div className="p-6 bg-white rounded-md  max-h-[80vh] max-w-lg ">
                <div className='overflow-y-auto max-h-[75vh]'>
 <h2 className="mb-4 flex items-center gap-2 text-xl font-semibold">
                    <Mail className="h-5 w-5 text-gray-600" />
                    {isIndividual ? 'Contact User' : 'Send Bulk Email'}
                </h2>
                <form onSubmit={handleSendEmail} className="space-y-4">

                    {/* To field */}
                    <div>
                        <label htmlFor="bulk-to" className="block text-sm font-medium text-gray-700">
                            To
                        </label>
                        <EmailInputChips value={to} onChange={setTo} placeholder="Enter email address(es) separated by commas" />
                        <p className="mt-1 text-xs text-gray-500">Separate multiple email addresses with commas</p>
                    </div>

                    {/* CC Field */}
                    <div>
                        <label htmlFor="cc-field" className="block text-sm font-medium text-gray-700">
                            CC
                        </label>
                        <EmailInputChips value={cc} onChange={setCc} placeholder="Enter CC email address(es) separated by commas" />
                        <p className="mt-1 text-xs text-gray-500">Separate multiple email addresses with commas</p>
                    </div>

                    {/* BCC Field */}
                    <div>
                        <label htmlFor="bulk-bcc" className="block text-sm font-medium text-gray-700">
                            BCC
                        </label>
                        <EmailInputChips value={bcc} onChange={setBcc} placeholder="Enter BCC email address(es) separated by commas" />
                        <p className="mt-1 text-xs text-gray-500">Recipients will be sent as BCC to protect privacy</p>
                    </div>

                    {/* Subject field */}
                    <div>
                        <label htmlFor="bulk-subject" className="block text-sm font-medium text-gray-700">
                            Subject
                        </label>
                        <input
                            id="bulk-subject"
                            value={subject}
                            onChange={(e) => setSubject(e.target.value)}
                            placeholder="Enter email subject"
                            className="mt-1 w-full rounded border px-3 py-2"
                            required
                        />
                    </div>

                    {/* Message field */}
                    <div>
                        <label htmlFor="bulk-message" className="block text-sm font-medium text-gray-700">
                            Message
                        </label>

                         <ReactQuill
                                                theme="snow"
                                                value={message}
                                                onChange={value => setMessage(value)}
                        
                                            />
                        {/* <textarea
                            id="bulk-message"
                            value={message}
                            onChange={(e) => setMessage(e.target.value)}
                            placeholder="Enter your message"
                            rows={8}
                            className="mt-1 w-full rounded border px-3 py-2"
                            required
                        /> */}
                    </div>

                    {/* Attachments */}
                    <div>
                        <label htmlFor="attachments" className="block text-sm font-medium text-gray-700">
                            Attachments
                        </label>
                        <div className="space-y-2">
                            <div className="flex items-center gap-2">
                                <input id="attachments" type="file" multiple onChange={handleFileUpload} className="hidden" />
                                <button
                                    type="button"
                                    className="flex items-center gap-2 rounded border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-100"
                                    onClick={() => document.getElementById('attachments')?.click()}
                                >
                                    <Upload className="h-4 w-4" />
                                    Upload Files
                                </button>
                                <span className="text-xs text-gray-500">{attachments.length > 0 ? `${attachments.length} file(s) selected` : 'No files selected'}</span>
                            </div>

                            {attachments.length > 0 && (
                                <div className="max-h-32 space-y-2 overflow-y-auto rounded border border-gray-200 bg-white p-2">
                                    {attachments.map((file, index) => (
                                        <div key={index} className="flex items-center justify-between rounded-md bg-gray-50 p-2">
                                            <div className="flex min-w-0 flex-1 items-center gap-2">
                                                <FileText className="h-4 w-4 flex-shrink-0 text-gray-500" />
                                                <span className="truncate text-sm">{file.name}</span>
                                                <span className="flex-shrink-0 text-xs text-gray-400">({formatFileSize(file.size)})</span>
                                            </div>
                                            <button type="button" className="flex-shrink-0 rounded-full p-1 text-red-500 hover:bg-gray-100 hover:text-red-700" onClick={() => removeAttachment(index)}>
                                                <X className="h-4 w-4" />
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                        <p className="mt-1 text-xs text-gray-500">You can upload multiple files. Common formats: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, Images</p>
                    </div>

                    <div className="flex justify-end gap-3 pt-4">
                        <button type="button" onClick={onClose} className="rounded border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-100">
                            Cancel
                        </button>
                        <button type="submit" className="rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:opacity-50" disabled={isSubmitting}>
                            {isSubmitting ? 'Sending...' : 'Send Email'}
                        </button>
                    </div>
                </form>
                </div>
               
            </div>
        </Modal>
    );
}

// Placeholder modal content for Bulk Upload
function BulkUploadModal({ isOpen, onClose }: ModalBasicProps): JSX.Element {
    return (
        <Modal isOpen={isOpen} onClose={onClose} classes="!max-w-2xl">
            <div className="p-6">
                <h2 className="mb-4 text-xl font-semibold">Bulk Upload Users</h2>
                <div className="mb-2">
                    <h3 className="text-sm font-medium text-gray-700">CSV File Upload</h3>
                </div>
                <div className="mb-4 cursor-pointer rounded border-2 border-dashed border-gray-300 p-6 text-center">
                    <svg className="mx-auto mb-2 h-8 w-8 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M6 2h12a2 2 0 012 2v16a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm1 2v16h10V4H7zm2 2h6v2H9V6zm0 4h6v2H9v-2zm0 4h4v2H9v-2z" />
                    </svg>
                    <p className="text-gray-500">Click to upload CSV file or drag and drop</p>
                    <p className="text-sm text-gray-400">CSV files only (Name, Email, Type, Status)</p>
                </div>
                <div className="mb-4 text-center text-gray-500">OR</div>
                <div className="mb-2">
                    <label className="block text-sm font-medium text-gray-700">Paste CSV Data</label>
                    <textarea
                        className="mb-3 w-full rounded border px-3 py-2"
                        rows={4}
                        placeholder={`Name,Email,Type,Status\nJohn Doe,<EMAIL>,Web User,Active\nJane Smith,<EMAIL>,Agent,Active`}
                    />
                    <p className="text-sm text-gray-400">Format: Name, Email, Type, Status (one user per line)</p>
                </div>
                <div className="flex justify-end gap-2">
                    <button className="rounded border px-4 py-2 text-gray-700 hover:bg-gray-100" onClick={onClose}>
                        Cancel
                    </button>
                    <button className="rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700">Upload Users</button>
                </div>
            </div>
        </Modal>
    );
}

// Placeholder modal content for Add New User
function AddUserModal({ isOpen, onClose }: ModalBasicProps): JSX.Element {
    const [selectedUserType, setSelectedUserType] = useState('Web User');
    const [fullName, setFullName] = useState('');
    const [email, setEmail] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState('');
    const userTypeOptions = dropdown2.filter((option) => option.label !== 'All User Types');

    const handleSubmit = async () => {
        if (!fullName || !email || !selectedUserType) {
            setError('Please fill in all fields');
            return;
        }
        setError('');
        setIsSubmitting(true);
        try {
            // Check if user exists
            const checkRes = await fetch(`${USERS_API}?search=${encodeURIComponent(email)}`);
            const checkData = await checkRes.json();
            if (checkData?.data?.users?.some((u: any) => u['Email']?.toLowerCase() === email.toLowerCase())) {
                setError('User already exists.');
                setIsSubmitting(false);
                return;
            }
            // Proceed to invite
            const response = await fetch(USERS_ADD_USER_API, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify({
                    fullName,
                    email,
                    userType: selectedUserType.toLowerCase().replace(/\s+/g, ''),
                }),
            });
            const result = await response.json();
            if (result.success) {
                showMessage('User invitation sent successfully', 'success');
                onClose();
                setFullName('');
                setEmail('');
                setSelectedUserType('Web User');
            } else {
                setError(result.message || 'Failed to add user');
            }
        } catch (error) {
            setError('Failed to add user');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Modal isOpen={isOpen} onClose={onClose} classes="min-h-[550px] overflow-y-auto !max-w-xl">
            <div className="p-6">
                <h2 className="mb-10 flex items-center gap-2 text-xl font-semibold">
                    <svg className="h-5 w-5 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 4a1 1 0 011 1v6h6a1 1 0 110 2h-6v6a1 1 0 11-2 0v-6H5a1 1 0 110-2h6V5a1 1 0 011-1z" />
                    </svg>
                    Add New User
                </h2>
                <div className="mb-2">
                    <label className="block text-sm font-medium text-gray-700">Full Name</label>
                    <input className="mt-1 w-full rounded border px-3 py-2" placeholder="Enter full name" value={fullName} onChange={(e) => setFullName(e.target.value)} />
                </div>
                <div className="mb-2">
                    <label className="block text-sm font-medium text-gray-700">Email Address</label>
                    <input className="mt-1 w-full rounded border px-3 py-2" placeholder="Enter email address" type="email" value={email} onChange={(e) => setEmail(e.target.value)} />
                </div>
                <div className="mb-2">
                    <label className="block text-sm font-medium text-gray-700">User Type</label>
                    <SearchDropDown classes="!h-14 w-full mt-1" dropdownOptions={userTypeOptions} initail={selectedUserType} setSelectedStatus={setSelectedUserType} />
                </div>
                {error && <div className="mb-2 text-sm text-red-600">{error}</div>}
                <div className="mt-6 flex justify-end gap-2">
                    <button className="rounded border px-4 py-2 text-gray-700 hover:bg-gray-100" onClick={onClose} disabled={isSubmitting}>
                        Cancel
                    </button>
                    <button
                        className="flex items-center gap-2 rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50"
                        onClick={handleSubmit}
                        disabled={isSubmitting}
                    >
                        {isSubmitting ? (
                            <>
                                <svg className="h-5 w-5 animate-spin text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path
                                        className="opacity-75"
                                        fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                    ></path>
                                </svg>
                                Adding User...
                            </>
                        ) : (
                            <>
                                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 4a1 1 0 011 1v6h6a1 1 0 110 2h-6v6a1 1 0 11-2 0v-6H5a1 1 0 110-2h6V5a1 1 0 011-1z" />
                                </svg>
                                Add User
                            </>
                        )}
                    </button>
                </div>
            </div>
        </Modal>
    );
}

// New UserDetailsModal component
function UserDetailsModal({ isOpen, onClose, user }: UserDetailsModalProps): JSX.Element | null {
    if (!user) return null; // Don't render if no user is provided

    return (
        <Modal isOpen={isOpen} onClose={onClose} classes="!max-w-xl">
            <div className="p-6">
                {/* Header */}
                <div className="mb-6 flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <div className="flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 text-2xl font-bold uppercase text-blue-700">
                            {user.name
                                ? user.name
                                      .split(' ')
                                      .map((n: string) => n[0])
                                      .join('')
                                : ''}
                        </div>
                        <div>
                            <h3 className="text-2xl font-semibold text-gray-800">{user.name}</h3>
                            <p className="text-base text-gray-600">{user.email}</p>
                        </div>
                    </div>
                    <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
                        <X className="h-6 w-6" />
                    </button>
                </div>

                {/* Status and Type Tags */}
                <div className="mb-6 flex items-center gap-2">
                    <span
                        className={`rounded-full px-3 py-1 text-sm font-medium ${
                            user.status === 'Activated' ? 'bg-green-100 text-green-800' : user.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                        }`}
                    >
                        {user.status}
                    </span>
                    <span className="rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800">{user.type}</span>
                </div>

                {/* Information Sections */}
                <div className="grid grid-cols-1 gap-x-8 gap-y-6 md:grid-cols-2">
                    {/* Contact Information */}
                    <div>
                        <h4 className="mb-3 text-lg font-semibold text-gray-800">Contact Information</h4>
                        <div className="flex items-center gap-2 text-gray-700">
                            <Mail className="h-5 w-5" />
                            <span>{user.email}</span>
                        </div>
                        {/* Add more contact info if available, e.g., phone */}
                    </div>

                    {/* Account Information */}
                    <div>
                        <h4 className="mb-3 text-lg font-semibold text-gray-800">Account Information</h4>
                        <div className="mb-2 flex items-center gap-2 text-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-5 w-5">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5m18 7.5v-7.5"
                                />
                            </svg>
                            <span>Joined: {formatJoinedDate(user.joinDate)}</span>
                        </div>
                        <div className="flex items-center gap-2 text-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-5 w-5">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5m18 7.5v-7.5"
                                />
                            </svg>
                            <span>Last Login: {formatJoinedDate(user.lastLogin)}</span>
                        </div>
                    </div>
                </div>

                {/* Close Button */}
                <div className="mt-8 flex justify-end">
                    <button type="button" onClick={onClose} className="rounded-md border border-gray-300 px-6 py-2 font-medium text-gray-700 hover:bg-gray-100">
                        Close
                    </button>
                </div>
            </div>
        </Modal>
    );
}

// Inline BulkActionsCard component
interface BulkActionsCardProps {
    selectedCount: number;
    actions: {
        label: string;
        action: () => void;
        icon: React.ElementType;
    }[];
    onClearSelection: () => void;
}

const BulkActionsCard: React.FC<BulkActionsCardProps> = ({ selectedCount, actions, onClearSelection }) => {
    if (selectedCount === 0) return null;

    return (
        <div className="mb-8 min-w-[300px] rounded-lg border border-blue-200 bg-blue-50 p-4 shadow-lg">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <span className="text-sm font-medium text-blue-800">
                        {selectedCount} item{selectedCount > 1 ? 's' : ''} selected
                    </span>
                    <button className="text-sm font-medium text-blue-600 hover:text-blue-800" onClick={onClearSelection}>
                        Clear Selection
                    </button>
                </div>
                <div className="flex gap-2">
                    {actions.map((action, index) => {
                        const Icon = action.icon;
                        return (
                            <button
                                key={index}
                                className="flex items-center gap-2 rounded-md border border-[#1D7EB6] bg-[#1D7EB6] px-4 py-2 text-sm font-medium text-white transition hover:bg-[#166da0]"
                                onClick={action.action}
                            >
                                <Icon className="h-4 w-4" />
                                {action.label}
                            </button>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

const Card = ({ value, label, onClick }: any) => (
    <div
        className="flex shrink grow basis-0 cursor-pointer flex-col items-center justify-center gap-2.5 rounded-[20px] border-2 border-[#e4e4e4] bg-neutral-100 p-5 shadow-[0px_4px_44px_-4px_rgba(12,12,13,0.05)] 2xl:p-10"
        onClick={onClick}
    >
        <div className=" flex flex-col items-center justify-start gap-1.5">
            <div className="text-center text-base font-medium leading-relaxed  text-[#636363]">{label}</div>
            <div className="text-center font-golosText text-5xl font-semibold text-[#993333]">{value}</div>
        </div>
    </div>
);

export default function UserManagementTable() {
    const { push } = useRouter();
    const [loader, setLoader] = useState(false);
    const [users, setUsers] = useState<any[]>([]);
    const [showDropdown, setShowDropdown] = useState(false);
    const [showActionDropdown, setShowActionDropdown] = useState<string | null>(null);
    const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>('bottom');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedType, setSelectedType] = useState('All User Types');
    const [selectedStatus, setSelectedStatus] = useState('All Status');
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
    const dropdownRef = useRef<HTMLDivElement | null>(null);
    const actionDropdownRef = useRef<HTMLDivElement>(null);
    const actionButtonRefs = useRef<Map<string, HTMLButtonElement>>(new Map());
    const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);
    const [showEmailModal, setShowEmailModal] = useState(false);
    const [emailRecipients, setEmailRecipients] = useState<Array<{ name: string; email: string }>>([]);
    const [isIndividualEmail, setIsIndividualEmail] = useState(false);
    const [showUserDetailsModal, setShowUserDetailsModal] = useState(false);
    const [selectedUserDetails, setSelectedUserDetails] = useState<any>(null);
    const [showDeactivateConfirmModal, setShowDeactivateConfirmModal] = useState(false);
    const [userToDeactivate, setUserToDeactivate] = useState<any>(null);
    const [isDeactivating, setIsDeactivating] = useState(false);
    const [showResetPasswordConfirmModal, setShowResetPasswordConfirmModal] = useState(false);
    const [userToResetPassword, setUserToResetPassword] = useState<any>(null);
    const [isResettingPassword, setIsResettingPassword] = useState(false);
    const [showAddNoteModal, setShowAddNoteModal] = useState(false);
    const [userToAddNote, setUserToAddNote] = useState<any>(null);
    const [noteContent, setNoteContent] = useState('');
    const [isAddingNote, setIsAddingNote] = useState(false);
    const [notesHistory, setNotesHistory] = useState<any[]>([]);
    const [isLoadingNotes, setIsLoadingNotes] = useState(false);

    // --- Pagination State ---
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [tableLoading, setTableLoading] = useState(false);
    const [allUsers, setAllUsers] = useState<any[]>([]);

    // 1. Table loader state
    // const [tableLoading, setTableLoading] = useState(false);

    // Add a master list of all users loaded so far
    const [allUsersMap, setAllUsersMap] = useState<{ [id: string]: any }>({});

    // Add state for activate confirmation modal
    const [showActivateConfirmModal, setShowActivateConfirmModal] = useState(false);
    const [userToActivate, setUserToActivate] = useState<any>(null);

    // 1. Add state for all users and filtered users
    // const [allUsers, setAllUsers] = useState<any[]>([]);

    // 2. Fetch all users once (or a large chunk)
    useEffect(() => {
        // Only fetch once on mount
        const fetchAllUsers = async () => {
            setTableLoading(true);
            try {
                // Fetch a large number of users (adjust limit as needed)
                const response = await fetch(`${USERS_API}?page=1&limit=10000`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                });
                const result = await response.json();
                if (result.success && result.data) {
                    const transformedUsers = result.data.users.map((user: any) => ({
                        id: user['Email'] || Math.random().toString(36).substr(2, 9),
                        userId: user['User ID'] || null,
                        name: user['Full Name'] || '',
                        email: user['Email'] || '',
                        type: user['Type'] || '',
                        status: user['Status'] || '',
                        lastLogin: user['Last Login'] ? user['Last Login'] : 'Never',
                        joinDate: user['Join Date'] || '',
                        profileImage: user['Profile Image'] || user['profileImage'] || '',
                    }));
                    setAllUsers(transformedUsers);
                    // Add/merge users into allUsersMap
                    setAllUsersMap(prev => {
                        const updated = { ...prev };
                        transformedUsers.forEach((u: any) => { updated[u.id] = u; });
                        return updated;
                    });
                    if (result.data.pagination) {
                        // setTotalPages(result.data.pagination.totalPages || 1);
                        // setTotalUsers(result.data.pagination.totalUsers || 0);
                    }
                    if (result.data.counts) {
                        // setActiveUsersCount(result.data.counts.active || 0);
                        // setPendingUsersCount(result.data.counts.pending || 0);
                        // setUnfilteredTotalUsers(result.data.counts.total || 0);
                    }
                    // If backend provides all user IDs, set them here (placeholder)
                    if (result.data.allUserIds) {
                        // setAllUserIds(result.data.allUserIds);
                    }
                } else {
                    showMessage(result.message || 'Failed to fetch users', 'error');
                }
            } catch (error) {
                showMessage('Failed to fetch users', 'error');
            } finally {
                setTableLoading(false);
            }
        };
        fetchAllUsers();
    }, []);

    // 3. useMemo for filtered users (search, status, type)
    const filteredUsers = React.useMemo(() => {
        let filtered = allUsers;
        if (selectedStatus !== 'All Status') {
            filtered = filtered.filter(u => u.status === selectedStatus);
        }
        if (selectedType !== 'All User Types') {
            // Compare type filter case-insensitively and ignore whitespace
            const selectedTypeNormalized = selectedType.replace(/\s+/g, '').toLowerCase();
            filtered = filtered.filter(u =>
                u.type && u.type.replace(/\s+/g, '').toLowerCase() === selectedTypeNormalized
            );
        }
        if (debouncedSearchTerm.trim()) {
            const term = debouncedSearchTerm.trim().toLowerCase();
            filtered = filtered.filter(u =>
                (u.name && u.name.toLowerCase().includes(term)) ||
                (u.email && u.email.toLowerCase().includes(term)) ||
                (u.type && u.type.toLowerCase().includes(term)) ||
                (u.status && u.status.toLowerCase().includes(term))
            );
        }
        return filtered;
    }, [allUsers, selectedStatus, selectedType, debouncedSearchTerm]);

    // 4. Update stats from filteredUsers
    const totalUsers = filteredUsers.length;
    const activeUsersCount = filteredUsers.filter(u => u.status === 'Activated').length;
    const pendingUsersCount = filteredUsers.filter(u => u.status === 'Pending').length;

    // 5. Pagination for filteredUsers
    const totalPages = Math.max(1, Math.ceil(filteredUsers.length / pageSize));
    const paginatedUsers = React.useMemo(() => {
        const start = (currentPage - 1) * pageSize;
        return filteredUsers.slice(start, start + pageSize);
    }, [filteredUsers, currentPage, pageSize]);

    // 6. Remove loader flicker for search/filter (only show loader on initial fetch)
    // useEffect(() => {
    //     fetchData();
    // }, [debouncedSearchTerm, selectedStatus, selectedType, currentPage, pageSize]);

    useEffect(() => {
        const timeoutId = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
        }, 300); // 300ms debounce
        return () => clearTimeout(timeoutId);
    }, [searchTerm]);

    // Sorting state
    const [sortConfig, setSortConfig] = useState<{
        key: string;
        direction: 'ascending' | 'descending' | null;
    }>({
        key: '',
        direction: null,
    });

    // Handle sorting
    const requestSort = (key: string) => {
        let direction: 'ascending' | 'descending' | null = 'ascending';

        if (sortConfig.key === key) {
            if (sortConfig.direction === 'ascending') {
                direction = 'descending';
            } else if (sortConfig.direction === 'descending') {
                direction = null;
            }
        }

        setSortConfig({ key, direction });
    };

    // Sort the data from the current page
    const sortedUsers = [...users].sort((a: any, b: any) => {
        if (sortConfig.direction === null) {
            return 0;
        }

        const { key, direction } = sortConfig;

        // Special case for 'accountName' (composite of firstName + middleName + lastName)
        const getFullName = (obj: any) => `${obj.firstName || ''} ${obj.middleName || ''} ${obj.lastName || ''}`.trim().toLowerCase();

        const aValue = key === 'accountName' ? getFullName(a) : (a[key] || '').toString().toLowerCase();
        const bValue = key === 'accountName' ? getFullName(b) : (b[key] || '').toString().toLowerCase();

        if (aValue < bValue) return direction === 'ascending' ? -1 : 1;
        if (aValue > bValue) return direction === 'ascending' ? 1 : -1;
        return 0;
    });

    // Close dropdowns when clicking outside
    useEffect(() => {
        function handleClickOutside(event: MouseEvent | globalThis.MouseEvent) {
            // Get the button element that opened this specific dropdown
            const currentActionButton = actionButtonRefs.current.get(showActionDropdown as string);

            if (
                actionDropdownRef.current &&
                !actionDropdownRef.current.contains(event.target as Node) &&
                currentActionButton && // Ensure button exists
                !currentActionButton.contains(event.target as Node) // Check if click was on the button itself
            ) {
                setShowActionDropdown(null);
            }
        }
        if (showActionDropdown !== null) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [showActionDropdown]);

    // Handle dropdown positioning
    const handleActionClick = (userId: string, event: React.MouseEvent<HTMLButtonElement>) => {
        // Store the button reference
        actionButtonRefs.current.set(userId, event.currentTarget);

        // Toggle dropdown
        if (showActionDropdown === userId) {
            setShowActionDropdown(null);
            return;
        }

        // Check position in viewport
        const buttonRect = event.currentTarget.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - buttonRect.bottom;

        // If less than 200px below (approximate dropdown height), position above
        setDropdownPosition(spaceBelow < 200 ? 'top' : 'bottom');
        setShowActionDropdown(userId);

        // The individual actions will be handled by their respective buttons in the dropdown
    };

    const handleViewDetails = (user: any) => {
        setSelectedUserDetails(user);
        setShowUserDetailsModal(true);
        setShowActionDropdown(null); // Close dropdown
    };

    const handleAddNote = (user: any) => {
        setUserToAddNote(user);
        setNoteContent(''); // Clear previous note content
        setShowAddNoteModal(true);
        setShowActionDropdown(null); // Close dropdown
        // Fetch notes for this user
        if (user && user.userId) {
            setIsLoadingNotes(true);
            fetch(USERS_GET_NOTES_API.replace(':id', user.userId), {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
            })
                .then((res) => res.json())
                .then((data) => {
                    if (Array.isArray(data)) {
                        setNotesHistory(data);
                    } else if (Array.isArray(data.data)) {
                        setNotesHistory(data.data);
                    } else {
                        setNotesHistory([]);
                    }
                })
                .catch(() => setNotesHistory([]))
                .finally(() => setIsLoadingNotes(false));
        } else {
            setNotesHistory([]);
        }
    };

    const handleUserResetPassword = (user: any) => {
        setUserToResetPassword(user);
        setShowResetPasswordConfirmModal(true);
        setShowActionDropdown(null); // Close dropdown
    };

    const confirmResetPassword = async () => {
        if (!userToResetPassword || !userToResetPassword.userId) return;

        const adminUserId = parseInt(getAndDecryptCookie('adminUserId'), 10);

        if (isNaN(adminUserId)) {
            showMessage('Admin user ID not found. Please log in again.', 'error');
            return;
        }

        setIsResettingPassword(true);

        try {
            const response = await fetch(USERS_RESET_PASSWORD_API.replace(':id', userToResetPassword.userId), {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    createdBy: adminUserId, // This will be the id from prf.profile
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to reset password.');
            }

            const result = await response.json();
            showMessage(result.message || 'Password reset successfully, credentials emailed to user!', 'success');
            fetchData(); // Re-fetch data to update UI if needed (e.g., status changes to "Reset Pending")
        } catch (error: any) {
            console.error('Error resetting password:', error);
            showMessage(error.message || 'Failed to reset password.', 'error');
        } finally {
            setIsResettingPassword(false);
            setShowResetPasswordConfirmModal(false);
            setUserToResetPassword(null);
        }
    };

    const handleDeactivate = (user: any) => {
        setUserToDeactivate(user);
        setShowDeactivateConfirmModal(true);
        setShowActionDropdown(null); // Close dropdown
    };

    const confirmDeactivateUser = async () => {
        if (!userToDeactivate) return;

        setIsDeactivating(true);
        try {
            const response = await fetch(USERS_DEACTIVATE_API.replace(':id', userToDeactivate.userId), {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to deactivate user.');
            }

            const result = await response.json();
            showMessage(result.message || 'User deactivated successfully!', 'success');
            fetchData(); // Re-fetch data to update UI
        } catch (error: any) {
            console.error('Error deactivating user:', error);
            showMessage(error.message || 'Failed to deactivate user.', 'error');
        } finally {
            setIsDeactivating(false);
            setShowDeactivateConfirmModal(false);
            setUserToDeactivate(null);
        }
    };

    const goToPage = (pageNumber: number) => setCurrentPage(pageNumber);
    const handlePrev = () => setCurrentPage((prev) => Math.max(prev - 1, 1));
    const handleNext = () => setCurrentPage((prev) => Math.min(prev + 1, totalPages));

    const paginationRange = () => {
        const range = [];
        let start = Math.max(currentPage - 2, 1);
        let end = Math.min(currentPage + 2, totalPages);

        if (end - start < 4) {
            if (currentPage < totalPages / 2) {
                end = Math.min(start + 4, totalPages);
            } else {
                start = Math.max(end - 4, 1);
            }
        }

        for (let i = start; i <= end; i++) {
            range.push(i);
        }

        return range;
    };

    const getSortIcon = (key: string) => {
        if (sortConfig.key !== key) {
            return <DatatableSortingIcon />;
        }

        if (sortConfig.direction === 'ascending') {
            return <DatatableAccendingSortingIcon />;
        }

        if (sortConfig.direction === 'descending') {
            return <DatatableDeccendingSortingIcon />;
        }

        return <DatatableSortingIcon />;
    };

    // 2. Update fetchData to set unfiltered total users and all user IDs if available
    const fetchData = async () => {
        try {
            setTableLoading(true);
            // Always fetch all users for allUsers (for filtering/pagination)
            const response = await fetch(`${USERS_API}?page=1&limit=10000`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
            });
            const result = await response.json();
            if (result.success && result.data) {
                const transformedUsers = result.data.users.map((user: any) => ({
                    id: user['Email'] || Math.random().toString(36).substr(2, 9),
                    userId: user['User ID'] || null,
                    name: user['Full Name'] || '',
                    email: user['Email'] || '',
                    type: user['Type'] || '',
                    status: user['Status'] || '',
                    lastLogin: user['Last Login'] ? user['Last Login'] : 'Never',
                    joinDate: user['Join Date'] || '',
                    profileImage: user['Profile Image'] || user['profileImage'] || '',
                }));
                setUsers(transformedUsers);
                setAllUsers(transformedUsers);
                setAllUsersMap(prev => {
                    const updated = { ...prev };
                    transformedUsers.forEach((u: any) => {
                        updated[u.id] = u;
                    });
                    return updated;
                });
            } else {
                showMessage(result.message || 'Failed to fetch users', 'error');
            }
        } catch (error) {
            console.error('Error fetching users:', error);
            showMessage('Failed to fetch users', 'error');
        } finally {
            setTableLoading(false);
        }
    };

    // 3. Select all users across all pages
    const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { checked } = e.target;
        const currentPageUserIds = paginatedUsers.map(user => user.id);
        if (checked) {
            setSelectedUserIds(prev => Array.from(new Set([...prev, ...currentPageUserIds])));
        } else {
            setSelectedUserIds(prev => prev.filter(id => !currentPageUserIds.includes(id)));
        }
    };

    // 4. Avatar logic: only 2 initials, show image if present
    function getUserInitials(name: string | null | undefined): string {
        if (!name || !name.trim()) return '';
        const parts = name.trim().split(' ');
        if (parts.length === 1) return parts[0][0].toUpperCase();
        return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
    }

    // Modal state
    const [showBulkEmail, setShowBulkEmail] = useState(false);
    const [showBulkUpload, setShowBulkUpload] = useState(false);
    const [showAddUser, setShowAddUser] = useState(false);

    // Modal open handlers
    const openBulkEmail = () => {
        const selected = getSelectedRecipientsForEmail();
        console.log('DEBUG openBulkEmail:', {
            selectedUserIds,
            allUsersMap,
            selectedRecipients: selected
        });
        if (selected.length === 0) {
            showMessage('Please select at least one user to send bulk email.', 'error');
            return;
        }
        setEmailRecipients(selected);
        setIsIndividualEmail(false);
        setShowEmailModal(true);
    };

    const closeEmailModal = () => setShowEmailModal(false);

    const openBulkUpload = () => setShowBulkUpload(true);
    const closeBulkUpload = () => setShowBulkUpload(false);
    const openAddUser = () => setShowAddUser(true);
    const closeAddUser = () => setShowAddUser(false);

    // Dropdown option click handler
    const handleDropdownOption = (cb?: () => void) => {
        setShowActionDropdown(null);
        if (cb) cb();
    };

    const handleDownloadCSV = async () => {
        try {
            const response = await fetch(USERS_DOWNLOAD_CSV_API, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error('Failed to download CSV');
            }

            // Get the blob from the response
            const blob = await response.blob();

            // Create a URL for the blob
            const url = window.URL.createObjectURL(blob);

            // Create a temporary link element
            const link = document.createElement('a');
            link.href = url;
            link.download = 'users.csv';

            // Append to body, click and remove
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up the URL
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error downloading CSV:', error);
            showMessage('Failed to download CSV file', 'error');
        }
    };

    // New selection handlers
    const handleSelectUser = (userId: string, e: React.ChangeEvent<HTMLInputElement>) => {
        const { checked } = e.target;
        if (checked) {
            setSelectedUserIds((prev) => [...prev, userId]);
        } else {
            setSelectedUserIds((prev) => prev.filter((id) => id !== userId));
        }
    };

    /*
    // Bulk action handlers
    const handleBulkResetPassword = () => {
        // Implement actual API call here
        setSelectedUserIds([]); // Clear selection after action
    };

    const handleBulkDelete = () => {
        // Implement actual API call here
        setSelectedUserIds([]); // Clear selection after action
    };
    */

    const getSelectedRecipientsForEmail = () => {
        return allUsers
            .filter(user => selectedUserIds.includes(user.email))
            .map(user => ({ name: user.name, email: user.email }));
    };

    // 7. Card click handlers
    const handleCardClick = (status: string) => {
        if (status === 'All Status') {
            setSelectedStatus('All Status');
            setActiveStatCard('all');
        } else if (status === 'Activated') {
            setSelectedStatus('Activated');
            setActiveStatCard('active');
        } else if (status === 'Pending') {
            setSelectedStatus('Pending');
            setActiveStatCard('pending');
        }
        setCurrentPage(1);
    };

    // Implement handleActivate
    const handleActivate = async (user: any) => {
        try {
            const response = await fetch(USERS_ACTIVATE_API.replace(':id', user.userId), {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
            });
            const result = await response.json();
            if (result.success) {
                showMessage(result.message || 'User activated successfully!', 'success');
                fetchData();
            } else {
                showMessage(result.message || 'Failed to activate user', 'error');
            }
        } catch (error) {
            showMessage('Failed to activate user', 'error');
        }
    };

    const confirmAddNote = async () => {
        if (!userToAddNote || !userToAddNote.userId || !noteContent.trim()) {
            showMessage('Please select a user and enter a note.', 'error');
            return;
        }
        const adminUserId = parseInt(getAndDecryptCookie('adminUserId'), 10);
        if (isNaN(adminUserId)) {
            showMessage('Admin user ID not found. Please log in again.', 'error');
            return;
        }
        setIsAddingNote(true);
        try {
            const response = await fetch(USERS_ADD_NOTE_API.replace(':id', userToAddNote.userId), {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify({
                    reason: noteContent,
                    createdBy: adminUserId,
                    profileId: userToAddNote.userId,
                }),
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to add note.');
            }
            const result = await response.json();
            showMessage(result.message || 'Note added successfully!', 'success');
            fetchData();
        } catch (error: any) {
            showMessage(error.message || 'Failed to add note.', 'error');
        } finally {
            setIsAddingNote(false);
            setShowAddNoteModal(false);
            setUserToAddNote(null);
            setNoteContent('');
        }
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1);
    };

    // Update Activate button click to show confirmation modal
    const handleActivateClick = (user: any) => {
        setUserToActivate(user);
        setShowActivateConfirmModal(true);
        setShowActionDropdown(null); // Close dropdown
    };

    // Update handleActivate to use userToActivate
    const confirmActivateUser = async () => {
        if (!userToActivate) return;
        await handleActivate(userToActivate);
        setShowActivateConfirmModal(false);
        setUserToActivate(null);
    };

    const PaginationControls = () => {
        if (totalPages <= 1) return null;
        return (
            <div className="flex justify-end items-center gap-2 mt-4">
                <button
                    className="px-3 py-1 border rounded disabled:opacity-50"
                    onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                    disabled={currentPage === 1}
                >
                    Prev
                </button>
                {Array.from({ length: totalPages }, (_, i) => (
                    <button
                        key={i}
                        className={`px-3 py-1 border rounded ${currentPage === i + 1 ? 'bg-blue-600 text-white' : ''}`}
                        onClick={() => setCurrentPage(i + 1)}
                    >
                        {i + 1}
                    </button>
                ))}
                <button
                    className="px-3 py-1 border rounded disabled:opacity-50"
                    onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                    disabled={currentPage === totalPages}
                >
                    Next
                </button>
            </div>
        );
    };

    // Add a state to track which stat card is active for conditional rendering
    const [activeStatCard, setActiveStatCard] = useState<'all' | 'active' | 'pending'>('all');

    return (
        <>
            {/* User Management Section */}
            <div className="mb-8 rounded-lg bg-white pt-6">
                <div className="flex w-full items-center justify-between">
                    <h2 className="font-inter text-xl font-semibold text-[#2d2d2e]">User Management</h2>
                    <div className="flex items-center gap-2">
                        <button
                            className="flex items-center gap-2 rounded-md border border-[#1D7EB6] bg-[#1D7EB6] px-4 py-3 text-sm font-medium text-white transition hover:bg-[#166da0]"
                            onClick={handleDownloadCSV}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            Download CSV
                        </button>
                        <button className="flex items-center gap-2 rounded-md border border-[#1D7EB6] bg-[#1D7EB6] px-4 py-3 text-sm font-medium text-white transition hover:bg-[#166da0]" onClick={openAddUser}>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                            </svg>
                            Add New User
                        </button>
                    </div>
                </div>
                <div className="flex items-center justify-between">
                    <h4 className="font-inter text-[#2d2d2e]">Manage and monitor all users across your platform </h4>
                </div>
            </div>

            {/* Statistics Cards (restored to original position above table) */}
            <div className="mb-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                {/* Total Users card is always shown and clickable */}
                <Card
                    label="Total Users"
                    value={totalUsers}
                    onClick={() => handleCardClick('All Status')}
                    className={selectedStatus === 'All Status' ? 'border-blue-500 bg-white cursor-pointer' : 'border-[#e4e4e4] bg-neutral-100 cursor-pointer'}
                />
                {/* Show only relevant cards based on activeStatCard */}
                {activeStatCard === 'all' && (
                    <>
                        <Card
                            label="Active Users"
                            value={activeUsersCount}
                            onClick={() => handleCardClick('Activated')}
                            className={selectedStatus === 'Activated' ? 'border-blue-500 bg-white cursor-pointer' : 'border-[#e4e4e4] bg-neutral-100 cursor-pointer'}
                        />
                        <Card
                            label="Pending Approval"
                            value={pendingUsersCount}
                            onClick={() => handleCardClick('Pending')}
                            className={selectedStatus === 'Pending' ? 'border-blue-500 bg-white cursor-pointer' : 'border-[#e4e4e4] bg-neutral-100 cursor-pointer'}
                        />
                        <Card label="Super Admins" value={1} />
                    </>
                )}
                {activeStatCard === 'active' && (
                    <Card
                        label="Active Users"
                        value={activeUsersCount}
                        onClick={() => handleCardClick('Activated')}
                        className={selectedStatus === 'Activated' ? 'border-blue-500 bg-white cursor-pointer' : 'border-[#e4e4e4] bg-neutral-100 cursor-pointer'}
                    />
                )}
                {activeStatCard === 'pending' && (
                    <Card
                        label="Pending Approval"
                        value={pendingUsersCount}
                        onClick={() => handleCardClick('Pending')}
                        className={selectedStatus === 'Pending' ? 'border-blue-500 bg-white cursor-pointer' : 'border-[#e4e4e4] bg-neutral-100 cursor-pointer'}
                    />
                )}
            </div>

            {selectedUserIds.length > 0 && (
                <BulkActionsCard
                    selectedCount={selectedUserIds.length}
                    onClearSelection={() => setSelectedUserIds([])}
                    actions={[
                        {
                            label: `Email Selected (${selectedUserIds.length})`,
                            action: openBulkEmail, // Use the openBulkEmail function
                            icon: Mail,
                        },
                    ]}
                />
            )}

            {/* Filters */}
            <div className="mb-8 rounded-lg bg-white p-6">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                    <div className="col-span-2">
                        <SearchInput onChange={handleSearchChange} value={searchTerm} placeholder="Search users by name or email..." />
                    </div>
                    <div className="col-span-1">
                        <SearchDropDown classes="!h-14 w-full" dropdownOptions={dropdown2} initail={selectedType} setSelectedStatus={setSelectedType} />
                    </div>
                    <div className="col-span-1">
                        <SearchDropDown classes="!h-14 w-full" dropdownOptions={dropdown} initail={selectedStatus} setSelectedStatus={setSelectedStatus} />
                    </div>
                </div>
            </div>

            {/* User Table */}
            <div className="relative rounded-lg bg-white">
                <table className="w-full text-left">
                    <thead className="bg-[#f8f9fa]">
                        <tr>
                            <th className="w-[5%] px-6 py-4 font-semibold text-[#636363]">
                                <input
                                    type="checkbox"
                                    className="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
                                    checked={paginatedUsers.length > 0 && paginatedUsers.every(user => selectedUserIds.includes(user.id))}
                                    onChange={handleSelectAll}
                                />
                            </th>
                            <th className="px-6 py-4 font-semibold text-[#636363]">User</th>
                            <th className="px-6 py-4 font-semibold text-[#636363]">Type</th>
                            <th className="px-6 py-4 font-semibold text-[#636363]">Status</th>
                            <th className="px-6 py-4 font-semibold text-[#636363]">Last Login</th>
                            <th className="px-6 py-4 font-semibold text-[#636363]">Join Date</th>
                            <th className="px-6 py-4 font-semibold text-[#636363]">Actions</th>
                        </tr>
                    </thead>
                    <tbody className={tableLoading ? 'opacity-50 pointer-events-none' : ''}>
                        {paginatedUsers.map((user: any, idx: number) => (
                            <tr key={user.id} className="border-b hover:bg-gray-50">
                                <td className="w-[5%] px-6 py-4">
                                    <input
                                        type="checkbox"
                                        className="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
                                        checked={selectedUserIds.includes(user.id)}
                                        onChange={(e) => handleSelectUser(user.id, e)}
                                    />
                                </td>
                                <td className="flex items-center gap-3 px-6 py-4">
                                    {user.profileImage ? (
                                        <img src={user.profileImage} alt={user.name} className="h-10 w-10 rounded-full border border-blue-200 object-cover" />
                                    ) : (
                                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 font-bold text-blue-700">{getUserInitials(user.name)}</div>
                                    )}
                                    <div>
                                        <div className="font-semibold text-[#2d2d2e]">{user.name}</div>
                                        <div className="text-xs text-gray-500">{user.email}</div>
                                    </div>
                                </td>
                                <td className="px-6 py-4">{user.type}</td>
                                <td className="px-6 py-4">{user.status}</td>
                                <td className="px-6 py-4 text-sm text-gray-700">{formatJoinedDate(user.lastLogin)}</td>
                                <td className="px-6 py-4 text-sm text-gray-700">{formatJoinedDate(user.joinDate)}</td>
                                <td className={`relative overflow-visible px-6 py-4 text-right ${showActionDropdown === user.id ? 'pointer-events-none' : ''}`}>
                                    <button
                                        className="rounded p-2 hover:bg-gray-100"
                                        title="Contact User"
                                        onClick={() => {
                                            setEmailRecipients([{ name: user.name, email: user.email }]);
                                            setIsIndividualEmail(true);
                                            setShowEmailModal(true);
                                        }}
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                            />
                                        </svg>
                                    </button>
                                    <button className="rounded p-2 hover:bg-gray-100" title="More Actions" onClick={(event) => handleActionClick(user.id, event)}>
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <circle cx="12" cy="12" r="1.5" />
                                            <circle cx="19.5" cy="12" r="1.5" />
                                            <circle cx="4.5" cy="12" r="1.5" />
                                        </svg>
                                    </button>
                                    {showActionDropdown === user.id && (
                                        <div
                                            ref={actionDropdownRef}
                                            className={`pointer-events-auto absolute right-0 z-[9999] max-h-60 w-48 overflow-y-auto rounded-md border-2 border-white bg-white !bg-opacity-100 shadow-2xl ring-1 ring-black ring-opacity-5 ${
                                                dropdownPosition === 'top' ? 'bottom-full mb-2' : 'top-10 mt-2'
                                            }`}
                                        >
                                            <div className="py-1">
                                                <button
                                                    className="block flex w-full items-center gap-2 px-4 py-2 text-left text-sm text-[#2d2d2e] hover:bg-gray-100"
                                                    onClick={() => handleViewDetails(user)}
                                                >
                                                    <Eye className="h-4 w-4" /> View Details
                                                </button>
                                                <button
                                                    className="block flex w-full items-center gap-2 px-4 py-2 text-left text-sm text-[#2d2d2e] hover:bg-gray-100"
                                                    onClick={() => handleAddNote(user)}
                                                >
                                                    <NotebookPen className="h-4 w-4" /> Add Note
                                                </button>
                                                <button
                                                    className={`w-full px-4 py-2 text-left text-sm ${
                                                        user.status === 'Deactivated' ? 'cursor-not-allowed text-gray-400' : 'text-[#2d2d2e] hover:bg-gray-100'
                                                    } block flex items-center gap-2`}
                                                    onClick={() => user.status !== 'Deactivated' && handleUserResetPassword(user)}
                                                    disabled={user.status === 'Deactivated'}
                                                >
                                                    Reset Password
                                                </button>
                                                {user.status === 'Deactivated' ? (
                                                    <button
                                                        className="block flex w-full items-center gap-2 px-4 py-2 text-left text-sm text-green-600 hover:bg-gray-100"
                                                        onClick={() => handleActivateClick(user)}
                                                    >
                                                        Activate
                                                    </button>
                                                ) : (
                                                    <button
                                                        className="block flex w-full items-center gap-2 px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100"
                                                        onClick={() => handleDeactivate(user)}
                                                    >
                                                        Deactivate
                                                    </button>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
                {tableLoading && (
                    <div className="absolute inset-0 z-10 flex items-center justify-center bg-white bg-opacity-60">
                        <Loading />
                    </div>
                )}
            </div>
            <PaginationControls />
            {/* Modals */}
            <BulkEmailModal
                key={emailRecipients.map(r => r.email).join(',') + isIndividualEmail}
                isOpen={showEmailModal}
                onClose={closeEmailModal}
                recipients={emailRecipients}
                isIndividual={isIndividualEmail}
            />
            <BulkUploadModal isOpen={showBulkUpload} onClose={closeBulkUpload} />
            <AddUserModal isOpen={showAddUser} onClose={closeAddUser} />
            {/* User Details Modal */}
            <UserDetailsModal isOpen={showUserDetailsModal} onClose={() => setShowUserDetailsModal(false)} user={selectedUserDetails} />
            {/* Deactivate Confirmation Modal */}
            <ConfirmationModal
                isOpen={showDeactivateConfirmModal}
                onClose={() => setShowDeactivateConfirmModal(false)}
                message={`Are you sure you want to deactivate ${userToDeactivate?.name}?`}
                onConfirm={confirmDeactivateUser}
                isLoading={isDeactivating}
            />
            {/* Reset Password Confirmation Modal */}
            <ConfirmationModal
                isOpen={showResetPasswordConfirmModal}
                onClose={() => setShowResetPasswordConfirmModal(false)}
                message={`Are you sure you want to reset the password for ${userToResetPassword?.name}? New credentials will be emailed to them.`}
                onConfirm={confirmResetPassword}
                isLoading={isResettingPassword}
            />
            {/* Add Note Modal */}
            {showAddNoteModal && userToAddNote && (
                <div className="fixed inset-0 z-50 flex h-full w-full items-center justify-center overflow-y-auto bg-gray-600 bg-opacity-50">
                    <div className="relative w-full max-w-xl rounded-md border bg-white p-5 shadow-lg">
                        <h2 className="mb-2 text-2xl font-bold">Notes</h2>
                        <p className="mb-4 text-base">
                            Add a note to <span className="font-semibold">{userToAddNote.name}</span>
                        </p>
                        <div className="mb-6">
                            <h3 className="mb-2 text-lg font-semibold">History</h3>
                            {isLoadingNotes ? (
                                <div className="text-sm text-gray-500">Loading notes...</div>
                            ) : notesHistory.length === 0 ? (
                                <div className="text-sm text-gray-400">No notes found.</div>
                            ) : (
                                <div className="max-h-56 space-y-3 overflow-y-auto">
                                    {notesHistory.map((note, idx) => {
                                        const dateObj = note.created_at ? new Date(note.created_at) : null;
                                        let formattedDateTime = '';
                                        if (dateObj && !isNaN(dateObj.getTime())) {
                                            const dateStr = dateObj.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }).replace(/ /g, '-');
                                            let timeStr = dateObj.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true });
                                            timeStr = timeStr.replace('AM', 'am').replace('PM', 'pm');
                                            formattedDateTime = `${dateStr}, ${timeStr}`;
                                        }
                                        return (
                                            <div key={idx} className="rounded border bg-gray-50 p-3">
                                                <div className="mb-1 flex items-center justify-between">
                                                    <span className="font-semibold text-gray-800">{note.createdByName || 'Unknown'}</span>
                                                    <span className="text-xs text-gray-500">{formattedDateTime}</span>
                                                </div>
                                                <div className="mb-1 text-xs italic text-gray-600">{note.reason}</div>
                                            </div>
                                        );
                                    })}
                                </div>
                            )}
                        </div>
                        <div className="mb-4">
                            <label className="mb-1 block text-sm font-medium text-gray-700">Note</label>
                            <textarea
                                className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-300 focus:outline-none focus:ring"
                                rows={5}
                                placeholder="Add Note*"
                                value={noteContent}
                                onChange={(e) => setNoteContent(e.target.value)}
                            ></textarea>
                        </div>
                        <div className="mt-4 flex justify-end gap-3">
                            <button className="rounded-md bg-gray-200 px-4 py-2 text-gray-800 hover:bg-gray-300" onClick={() => setShowAddNoteModal(false)} disabled={isAddingNote}>
                                Cancel
                            </button>
                            <button className="rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700 disabled:opacity-50" onClick={confirmAddNote} disabled={isAddingNote}>
                                {isAddingNote ? 'Adding...' : 'Submit'}
                            </button>
                        </div>
                    </div>
                </div>
            )}
            {/* Activate Confirmation Modal */}
            <ConfirmationModal
                isOpen={showActivateConfirmModal}
                onClose={() => setShowActivateConfirmModal(false)}
                message={`Are you sure you want to activate ${userToActivate?.name}? This action will reactivate the user.`}
                onConfirm={confirmActivateUser}
            />
        </>
    );
}
