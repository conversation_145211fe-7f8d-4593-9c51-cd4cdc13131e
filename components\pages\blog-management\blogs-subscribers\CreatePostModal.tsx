'use client';

import type React from 'react';
import { useEffect, useState } from 'react';
import SearchDropDown from '@/components/reusable/SearchDropDown';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import uploadFile from '@/components/reusable/UploadFile';
import API_ENDPOINTS from '@/app/lib/apiRoutes';

interface CreatePostModalProps {
    onClose: () => void;
    onSubmit: (postData: any) => void;
    post?: any;
    mode?: 'create' | 'edit';
}

export default function CreatePostModal({
    onClose,
    onSubmit,
    post,
    mode = 'create',
}: CreatePostModalProps) {
    const [formData, setFormData] = useState<any>({
        title:   '',
        author:   '',
        excerpt:   '',
        content:   '',
        tags:   '',
        featuredImage:   '',
        category:   'Select category',
    });

    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const [status, setStatus] = useState( mode === 'create' ? 22 : 23); // 23 = Draft

    const categories = [
        'Select category',
        'Market Trends',
        'Agent Tips',
        'Investment',
        'Education',
        'Technology',
    ];

    const statusOptions = [
        { label: 'Draft', value: 23 },
        { label: 'Published', value: 22 },
        { label: 'Archived', value: 5 },
    ];

    const handleInputChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
    ) => {
        const { name, value } = e.target;
        setFormData((prev :any)=> ({
            ...prev,
            [name]: value,
        }));

        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: '',
            }));
        }
    };

    const validateForm = () => {
        const newErrors: { [key: string]: string } = {};
        if (!formData.title.trim()) newErrors.title = 'Please fill out this field.';
        if (!formData.author.trim()) newErrors.author = 'Please fill out this field.';
        if (!formData.category || formData.category === 'Select category')
            newErrors.category = 'Please select a category.';
        if (!formData.excerpt.trim()) newErrors.excerpt = 'Please fill out this field.';
        if (!formData.content.trim()) newErrors.content = 'Please fill out this field.';
        // if (!formData.featuredImage) newErrors.featuredImage = 'Please upload a featured image.';
        // if (!formData.tags.trim()) newErrors.tags = 'Please fill out this field.';
        if (   status === null) newErrors.status = 'Please select a status.';

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async () => {
        if (!validateForm()) return;

        try {
            let imageUrl = ""; 
            try {
            if (formData.featuredImage instanceof File) {
                imageUrl = await uploadFile(formData.featuredImage, 'featuredBlogImage');
            }
            } catch (error) {
                console.error('Error uploading image:', error); 
                
            }
           
            const payload = {
                title: formData.title,
                author: formData.author,
                excerpt: formData.excerpt,
                content: formData.content,
                category: formData.category,
                featuredImage: imageUrl,
                statusId: status,
                tags: formData.tags.split(',').map((tag : string)=> tag.trim()),
            };

            const response = await fetch(API_ENDPOINTS.BLOG_CREATE, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                          redirect: 'follow',
                credentials: 'include',
                },
                body: JSON.stringify(payload),
            });

            if (!response.ok) throw new Error('Failed to create blog');

            const result = await response.json();
            console.log('Blog created:', result);
            onSubmit(result);
            onClose();
        } catch (error) {
            console.error('Error creating blog:', error);
            alert('There was an error creating the blog. Please try again.');
        }
    };

useEffect(() => {
   
  const fetchBlogPosts = async () => {
    try {
      const response = await fetch(API_ENDPOINTS.BLOG_GET_ONE+post.id, {
        method: 'GET',
        credentials: 'include', // must be outside headers
        headers: {
          'Content-Type': 'application/json',
        },
      });
 
      const result = await response.json();
      if(result.success) {
 
          const data =  result.data
            setFormData({
                title: data.title,
                author: data.author,
                excerpt: data.excerpt,
                content: data.content,
                category: data.category,
                featuredImage: data.featuredImage,
                status: data.statusId === 22 ? 'Published' : data.statusId === 23 ? 'Draft' : 'Archived',  
                tags: data.tags,
            });
            setStatus(data.statusId);
 
           
     console.log('Blogs fetched failed:', result);
        //   setBlogPosts(data);
        }else{
          console.log('Blogs fetched failed:', result);

      }
    } catch (error) {
      console.error('Error fetching blogs:', error);
    }
  };

  fetchBlogPosts();
}, [post]);

    return (
        <div className="w-full max-w-4xl mx-auto bg-white rounded-lg overflow-hidden max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200" onClick={e => e.stopPropagation()}>
                <div className="flex justify-between items-center">
                    <h2 className="text-lg font-semibold text-[#2d2d2e] font-inter">
                        {mode === 'edit' ? 'Edit Blog Post' : 'Create New Blog Post'}
                    </h2>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600 text-xl w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100"
                    >
                        ×
                    </button>
                </div>
            </div>

            <div className="px-6 py-4 space-y-4" onClick={e => e.stopPropagation()}>
                {/* Title */}
                <div>
                    <label className="block text-sm font-medium mb-2">Title</label>
                    <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        placeholder='Enter post title'
                        className={`w-full px-3 py-2 border rounded-md text-sm ${
                            errors.title ? 'border-red-500' : 'border-gray-300'
                        }`}
                    />
                    {errors.title && <p className="text-xs text-red-600">{errors.title}</p>}
                </div>

                {/* Author */}
                <div>
                    <label className="block text-sm font-medium mb-2">Author</label>
                    <input
                        type="text"
                        name="author"
                        value={formData.author}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 border rounded-md text-sm ${
                            errors.author ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Enter author's name"
                    />
                    {errors.author && <p className="text-xs text-red-600">{errors.author}</p>}
                </div>

                {/* Category & Status */}
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <label className="block text-sm font-medium mb-2">Category</label>
                        <SearchDropDown
                            classes="!h-14 w-full !z-[9999]"
                            dropdownOptions={categories.slice(1).map(cat => ({ label: cat }))}
                            initail={formData.category}
                            setSelectedStatus={(value: string) => {
                                setFormData((prev :any) => ({ ...prev, category: value }));
                                if (errors.category) {
                                    setErrors(prev => ({ ...prev, category: '' }));
                                }
                            }}
                            placeholder="Select category"
                        />
                        {errors.category && <p className="text-xs text-red-600">{errors.category}</p>}
                    </div>

                    <div>
                        <label className="block text-sm font-medium mb-2">Status</label>
                        <SearchDropDown
                            classes="!h-14 w-full !z-[9999]"
                            dropdownOptions={statusOptions}
                            initail={
                                statusOptions.find(option => option.value === status)?.label ||
                                'Select status'
                            }
                            setSelectedStatus={(value: string) => {
                                const found = statusOptions.find(opt => opt.label === value);
                                if (found) setStatus(found.value);
                            }}
                        />
                    </div>
                     {errors.status && <p className="text-xs text-red-600">{errors.status}</p>}
                </div>

                {/* Excerpt */}
                <div>
                    <label className="block text-sm font-medium mb-2">Excerpt</label>
                    <textarea
                        name="excerpt"
                        value={formData.excerpt}
                        onChange={handleInputChange}
                        rows={3}
                        className={`w-full px-3 py-2 border rounded-md text-sm resize-none ${
                            errors.excerpt ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Enter a brief excerpt of the post"
                    />
                    {errors.excerpt && <p className="text-xs text-red-600">{errors.excerpt}</p>}
                </div>

                {/* Content */}
                <div>
                    <label className="block text-sm font-medium mb-2">Content</label>
                    <ReactQuill
                        theme="snow"
                        value={formData.content}
                        onChange={value => setFormData((prev :any)=> ({ ...prev, content: value }))}

                    />
                    {errors.content && <p className="text-xs text-red-600">{errors.content}</p>}
                </div>

                {/* Tags */}
                <div>
                    <label className="block text-sm font-medium mb-2">Tags</label>
                    <input
                        type="text"
                        name="tags"
                        value={formData.tags}
                        onChange={handleInputChange}
                        placeholder="tag1, tag2, tag3"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    />
                </div>

                {/* Featured Image */}
                <div>
                    <label className="block text-sm font-medium mb-2">Featured Image</label>
                    <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                                setFormData((prev :any)=> ({ ...prev, featuredImage: file }));
                            }
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    />
                    {formData.featuredImage && typeof formData.featuredImage !== 'string' && (
                        <img
                            src={URL.createObjectURL(formData.featuredImage)}
                            alt="Preview"
                            className="mt-2 h-32 object-cover rounded"
                        />
                    )}
                </div>
            </div>

            {/* Footer */}
            <div className="px-6 py-4 border-t border-gray-200 flex justify-end gap-3">
                <button
                    onClick={onClose}
                    className="px-4 py-2 text-sm text-[#636363] bg-gray-100 border rounded"
                >
                    Cancel
                </button>
                <button
                    onClick={handleSubmit}
                    className="px-4 py-2 text-sm text-white bg-[#1D7EB6] rounded hover:bg-[#166da0]"
                >
                    {mode === 'edit' ? 'Update Post' : 'Create Post'}
                </button>
            </div>
        </div>
    );
}
