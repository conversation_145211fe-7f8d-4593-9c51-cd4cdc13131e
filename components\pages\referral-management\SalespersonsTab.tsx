'use client';

import API_ENDPOINTS from '@/app/lib/apiRoutes';
import SearchDropDown from '@/components/reusable/SearchDropDown';
import React, { useEffect, useState, useRef } from 'react';
import AddSalespersonPopup, { SalespersonFormData } from './AddSalespersonPopup';
import SalespersonDetailsModal from './SalespersonDetailsModal';
import { showMessage } from '@/app/lib/Alert';

interface Salesperson {
    id: number;
    full_name: string;
    email: string;
    phone: string;
    statusId: number;
    referral_id: string;
    commission_rate: number;
    referrals?: number;
    status_name: string;
    created_at: string;
    totalCommission?: { total: string; paid: string };
    pending?: string;
    notes?: string;
}

const itemsPerPageOptions = [10, 20, 30, 50, 100];

interface AddSalespersonPopupProps {
    isAddSalespersonOpen: boolean;
    setIsAddSalespersonOpen: (val: boolean) => void;
}

const SalespersonsTab = ({ isAddSalespersonOpen, setIsAddSalespersonOpen }: AddSalespersonPopupProps) => {
    const [salespersonsData, setSalespersonsData] = useState<Salesperson[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(itemsPerPageOptions[0]);
    const [totalCount, setTotalCount] = useState(0);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [monthFilter, setMonthFilter] = useState('');
    const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
    const dropdownRef = useRef(null);
    const [notification, setNotification] = useState<{ message: string; subMessage: string } | null>(null);
    const [selectedRecord, setSelectedRecord] = useState<Salesperson | null>(null);
    const [currentMode, setCurrentMode] = useState('create');
    const [isViewSalespersonOpen, setIsViewSalespersonOpen] = useState(false);

    const totalPages = Math.ceil(totalCount / itemsPerPage);
    const indexOfLastItem = Math.min(currentPage * itemsPerPage, totalCount);
    const indexOfFirstItem = (currentPage - 1) * itemsPerPage;
    const currentItems = salespersonsData?.slice(indexOfFirstItem, indexOfLastItem);

    useEffect(() => {
        if (notification) {
            const timer = setTimeout(() => {
                setNotification(null);
            }, 30000); // 30,000 ms = 30 seconds

            return () => clearTimeout(timer); // Cleanup on unmount or change
        }
    }, [notification]);

    const fetchSalespersons = async () => {
        const params = new URLSearchParams({
            page: currentPage.toString(),
            pageSize: itemsPerPage.toString(),
        });

        if (searchTerm) params.append('search', searchTerm);

        // Apply status as a separate param for backend mapping
        if (statusFilter.trim() !== '' && statusFilter.toLowerCase() !== 'all status') {
            params.append('status', statusFilter);
        }

        // Apply created_at month filter only
        if (monthFilter.trim() !== '' && monthFilter.toLowerCase() !== 'all months') {
            params.append('filterColumn', 'created_at');
            params.append('filterValue', monthFilter);
        }

        try {
            const res = await fetch(`${API_ENDPOINTS.SALESPERSONS}?${params.toString()}`, {
                credentials: 'include',
            });
            const result = await res.json();

            if (result.success) {
                console.log(result.data);
                const { records, pagination } = result.data;
                setTotalCount(pagination?.total || 0);
                setCurrentPage(pagination?.page || 1);
                setItemsPerPage(pagination?.pageSize || itemsPerPageOptions[0]);
                setSalespersonsData(records || []);
            }
        } catch (err) {
            console.error('Failed to fetch salespersons:', err);
        }
    };

    useEffect(() => {
        fetchSalespersons();
    }, [searchTerm, statusFilter, monthFilter, currentPage, itemsPerPage]);

    const exportToCSV = () => {
        const headers = ['Name', 'Email', 'Phone', 'Referral ID', 'Commission Rate', 'Status'];
        const rows = salespersonsData.map((sp) => [sp.full_name, sp.email, sp.phone, sp.referral_id, sp.commission_rate, sp.status_name]);
        const csvContent = [headers, ...rows].map((row) => row.join(',')).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `salespersons_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
        URL.revokeObjectURL(url);
    };

    const handlePageChange = (page: number) => setCurrentPage(page);
    const handleItemsPerPageChange = (value: number) => {
        setItemsPerPage(value);
        setCurrentPage(1);
    };

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text);
        alert('Copied!');
    };

    const handleActionClick = (personId: string, event: React.MouseEvent) => {
        event.stopPropagation();
        setActiveDropdown(activeDropdown === personId ? null : personId);
    };

    const handleViewDetails = (person: Salesperson) => {
        setSelectedRecord(person);
        setActiveDropdown(null);
        setIsViewSalespersonOpen(true);
    };

    const handleEditSalesperson = (person: Salesperson) => {
        console.log(person);
        setSelectedRecord(person);
        setActiveDropdown(null);
        setCurrentMode('edit');
        setIsAddSalespersonOpen(true);
    };

    const handleDeactivate = async (person: Salesperson) => {
        try {
            const newStatus = person.status_name === 'Active' ? 'Inactive' : 'Active';

            const formData = new FormData();
            formData.append('status', newStatus);
            const res = await fetch(API_ENDPOINTS.SALESPERSON_STATUS(person.id), {
                method: 'PATCH',
                credentials: 'include',
                body: formData,
            });

            const result = await res.json();

            if (result.success) {
                setNotification({
                    message: 'Status Updated',
                    subMessage: `Salesperson status changed to ${newStatus}`,
                });
                fetchSalespersons(); // Refresh the list
            } else {
                console.error(result.message);
                showMessage(result.message || 'Failed to update status', 'error');
            }
        } catch (err) {
            console.error('Error updating status:', err);
            showMessage('An error occurred while updating status', 'error');
        } finally {
            setActiveDropdown(null);
        }
    };

    // Define dropdown options
    const statusDropdownOptions = [{ label: 'All Status' }, { label: 'Pending' }, { label: 'Active' }, { label: 'Inactive' }];

    const monthDropdownOptions = [
        { label: 'All Months' },
        { label: 'January' },
        { label: 'February' },
        { label: 'March' },
        { label: 'April' },
        { label: 'May' },
        { label: 'June' },
        { label: 'July' },
        { label: 'August' },
        { label: 'September' },
        { label: 'October' },
        { label: 'November' },
        { label: 'December' },
    ];

    const toFormData = (person: Salesperson): SalespersonFormData => ({
        id: person.id,
        fullName: person.full_name,
        email: person.email,
        phone: person.phone,
        referralId: person.referral_id,
        commissionRate: person.commission_rate,
        status: person.status_name as 'Active' | 'Inactive',
        notes: person.notes || '',
    });

    return (
        <>
            <div className="space-y-6">
                {/* Header with Export */}
                <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-[#2d2d2e]">Salespersons</h2>
                    <button onClick={exportToCSV} className="flex items-center gap-2 rounded-md border border-[#e4e4e4] px-4 py-2 text-sm font-medium text-[#636363] hover:bg-gray-50">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Export ({salespersonsData.length})
                    </button>
                </div>

                {/* Update the Filters Section */}
                <div className="rounded-lg bg-white p-6">
                    <div className="mb-4 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#636363]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                            />
                        </svg>
                        <span className="ml-2 font-medium text-[#2d2d2e]">Filters</span>
                    </div>
                    <div className="flex w-full flex-col flex-wrap items-center gap-5 md:flex-row">
                        <div className="w-full min-w-[287px] flex-1 md:max-w-[287px]">
                            <div className="relative">
                                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path
                                            fillRule="evenodd"
                                            d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                            clipRule="evenodd"
                                        />
                                    </svg>
                                </div>
                                <input
                                    type="text"
                                    placeholder="Search salespersons..."
                                    className="h-14 w-full rounded-lg border border-[#e4e4e4] pl-10 pr-4 text-sm focus:border-[#1D7EB6] focus:outline-none"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>
                        </div>
                        <div className="w-auto min-w-[287px] flex-1 md:max-w-[287px]">
                            <SearchDropDown classes="!h-14 w-full" dropdownOptions={statusDropdownOptions} initail={statusFilter} setSelectedStatus={setStatusFilter} status={true} />
                        </div>
                        <div className="w-auto min-w-[287px] flex-1 md:max-w-[287px]">
                            <SearchDropDown classes="!h-14 w-full" dropdownOptions={monthDropdownOptions} initail={monthFilter} setSelectedStatus={setMonthFilter} />
                        </div>
                    </div>
                </div>

                {/* Table */}
                <div className="rounded-lg bg-white">
                    <div className="">
                        <table className="w-full">
                            <thead className="bg-gray-50 text-left">
                                <tr>
                                    <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Salesperson</th>
                                    <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Referral ID</th>
                                    <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Commission Rate</th>
                                    <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Referrals</th>
                                    <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Total Commission</th>
                                    <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Pending</th>
                                    <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Status</th>
                                    <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Actions</th>
                                </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-100 overflow-x-auto">
                                {currentItems.length === 0 ? (
                                    <tr>
                                        <td colSpan={8} className="px-6 py-6 text-center text-sm text-gray-500">
                                            No salespersons found.
                                        </td>
                                    </tr>
                                ) : (
                                    currentItems.map((person) => (
                                        <tr key={person.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4">
                                                <div>
                                                    <div className="font-medium text-[#2d2d2e]">{person.full_name || '—'}</div>
                                                    <div className="text-sm text-[#636363]">{person.email || '—'}</div>
                                                    <div className="text-sm text-[#636363]">{person.phone || '—'}</div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="flex items-center gap-2">
                                                    <span className="font-medium text-[#2d2d2e]">{person.referral_id || '—'}</span>
                                                    {person.referral_id && (
                                                        <button onClick={() => copyToClipboard(person.referral_id)} className="text-[#1D7EB6] hover:text-[#166da0]">
                                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path
                                                                    strokeLinecap="round"
                                                                    strokeLinejoin="round"
                                                                    strokeWidth={2}
                                                                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                                                                />
                                                            </svg>
                                                        </button>
                                                    )}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 font-medium text-[#2d2d2e]">{person.commission_rate ?? '—'}</td>
                                            <td className="px-6 py-4 font-medium text-[#2d2d2e]">{person.referrals ?? '—'}</td>
                                            <td className="px-6 py-4">
                                                <div className="font-medium text-[#2d2d2e]">{person.totalCommission?.total || '—'}</div>
                                                <div className="text-sm text-[#636363]">{person.totalCommission?.paid ? `${person.totalCommission.paid} paid` : '—'}</div>
                                            </td>
                                            <td className="px-6 py-4 font-medium text-orange-500">{person.pending || '—'}</td>
                                            <td className="px-6 py-4">
                                                <span
                                                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium
                                                ${
                                                    person.status_name.toLowerCase() == 'active'
                                                        ? 'bg-green-100 text-green-800'
                                                        : person.status_name.toLowerCase() == 'inactive'
                                                        ? 'bg-red-100 text-red-800'
                                                        : 'bg-yellow-100 text-yellow-800'
                                                }`}
                                                >
                                                    {person.status_name || '—'}
                                                </span>
                                            </td>
                                            <td className="relative px-6 py-4">
                                                <button onClick={(e) => handleActionClick?.(person.referral_id, e)} className="text-[#636363] hover:text-[#2d2d2e]">
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                        <circle cx="6" cy="10" r="2" />
                                                        <circle cx="10" cy="10" r="2" />
                                                        <circle cx="14" cy="10" r="2" />
                                                    </svg>
                                                </button>
                                                {activeDropdown === person.referral_id && (
                                                    <div ref={dropdownRef} className="absolute right-0 z-10 mt-2 w-48 rounded-md border border-gray-100 bg-white py-1 shadow-lg">
                                                        <button onClick={() => handleViewDetails?.(person)} className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                                            View Details
                                                        </button>
                                                        <button onClick={() => handleEditSalesperson?.(person)} className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                                            Edit Salesperson
                                                        </button>
                                                        <button onClick={() => handleDeactivate?.(person)} className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-50">
                                                            {person.status_name === 'Active' ? 'Deactivate' : 'Activate'}
                                                        </button>
                                                    </div>
                                                )}
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>

                    {/* Pagination Controls */}
                    <div className="flex items-center justify-between border-t border-gray-200 px-6 py-3">
                        <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-700">Show</span>
                            <select value={itemsPerPage} onChange={(e) => handleItemsPerPageChange(Number(e.target.value))} className="rounded border border-gray-300 px-2 py-1 text-sm">
                                {itemsPerPageOptions.map((option) => (
                                    <option key={option} value={option}>
                                        {option}
                                    </option>
                                ))}
                            </select>
                            <span className="text-sm text-gray-700">entries</span>
                        </div>

                        <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-700">
                                Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, salespersonsData.length)} of {salespersonsData.length} entries
                            </span>
                            <div className="flex gap-1">
                                <button
                                    onClick={() => handlePageChange(currentPage - 1)}
                                    disabled={currentPage === 1}
                                    className={`rounded border px-3 py-1 text-sm ${
                                        currentPage === 1 ? 'cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400' : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                                    }`}
                                >
                                    Previous
                                </button>
                                {Array.from({ length: totalPages }, (_, i) => i + 1).map((number) => (
                                    <button
                                        key={number}
                                        onClick={() => handlePageChange(number)}
                                        className={`rounded px-3 py-1 text-sm ${currentPage === number ? 'bg-[#1D7EB6] text-white' : 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50'}`}
                                    >
                                        {number}
                                    </button>
                                ))}
                                <button
                                    onClick={() => handlePageChange(currentPage + 1)}
                                    disabled={currentPage === totalPages}
                                    className={`rounded border px-3 py-1 text-sm ${
                                        currentPage === totalPages ? 'cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400' : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                                    }`}
                                >
                                    Next
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Add the popup component */}
            <AddSalespersonPopup
                isOpen={isAddSalespersonOpen}
                onClose={() => setIsAddSalespersonOpen(false)}
                onSubmit={fetchSalespersons}
                mode={currentMode}
                initialData={selectedRecord ? toFormData(selectedRecord) : undefined}
                setNotification={setNotification}
            />

            <SalespersonDetailsModal
                isOpen={!!isViewSalespersonOpen}
                onClose={() => {
                    setSelectedRecord(null);
                    setIsViewSalespersonOpen(false);
                }}
                person={selectedRecord}
            />

            {/* Notification Toast */}
            {notification && (
                <div className="fixed bottom-4 right-4 z-50 w-80 rounded-lg bg-white p-4 shadow-lg">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="font-medium text-gray-900">{notification.message}</h3>
                            <p className="mt-1 text-sm text-gray-500">{notification.subMessage}</p>
                        </div>
                        <button onClick={() => setNotification(null)} className="text-gray-400 hover:text-gray-500">
                            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path
                                    fillRule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clipRule="evenodd"
                                />
                            </svg>
                        </button>
                    </div>
                </div>
            )}
        </>
    );
};

export default SalespersonsTab;
