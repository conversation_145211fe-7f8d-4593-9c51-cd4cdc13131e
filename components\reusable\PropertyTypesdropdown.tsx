'use client';

import { useState } from 'react';

const propertyTypes: Record<'Residential' | 'Commercial', string[]> = {
  Residential: [
    'Apartment',
    'Villa',
    'Townhouse',
    'Penthouse',
    'Villa Compound',
    'Hotel Apartment',
    'Residential Plot',
    'Residential Floor',
    'Residential Building',
  ],
  Commercial: ['Office', 'Retail', 'Warehouse', 'Showroom', 'Commercial Building'],
};

export default function PropertyDropdown() {
  const [selectedCategory, setSelectedCategory] = useState<'Residential' | 'Commercial'>('Residential');
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const handleSelectType = (type: string) => {
    setSelectedType(type);
    setIsDropdownOpen(false);
  };

  return (
    <div className="relative font-inter">
      {/* Dropdown Button */}
      <button
        className="inline- relative  w-full text-grayText flex h-full max-h-14 cursor-pointer items-center justify-between gap-2 rounded-md border border-[#E4E4E4] bg-white p-2 py-4 font-inter"
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
      >
        {selectedType ? selectedType : 'Select Property Type'}
        <svg
                    className={`h-4 w-4 transform text-gray-600 transition-transform  'rotate-180' : 'rotate-0'}`}
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
      </button>

      {/* Dropdown Content */}
      {isDropdownOpen && (
        <div className="absolute w-full mt-1 bg-white border shadow-lg rounded-lg z-[99] p-3">
          {/* Category Tabs */}
          <div className="flex border border-gray">
            <button
              className={`w-1/2 px-2 py-3 text-center ${
                selectedCategory === 'Residential' ? 'bg-[#8b3a3a] text-white' : ''
              }`}
              onClick={() => setSelectedCategory('Residential')}
            >
              Residential
            </button>
            <button
              className={`w-1/2 px-2 py-3 text-center ${
                selectedCategory === 'Commercial' ? 'bg-[#8b3a3a] text-white' : ''
              }`}
              onClick={() => setSelectedCategory('Commercial')}
            >
              Commercial
            </button>
          </div>

          {/* Property Type Options */}
          <ul className="grid grid-cols-2 gap-2 p-2">
            {propertyTypes[selectedCategory].map((type) => (
              <li
                key={type}
                className="p-2 text-center hover:bg-[#8b3a3a] hover:text-[#fff] cursor-pointer"
                onClick={() => handleSelectType(type)}
              >
                {type}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
