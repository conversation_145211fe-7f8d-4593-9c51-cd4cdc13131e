import Image from 'next/image';
import React from 'react';
import images from '@/public/assets/images/main/Preview-image.png';
const DetailsImagesPreview2 = () => {
    const imagesArray = [images, images, images ,images, images, images]
    return (
        <div>
            <div className="grid grid-cols-5 gap-5  px-4 ">

                <div className='w-full md:col-span-3 col-span-5 h-full rounded-lg'>
                    <Image src={imagesArray[0]} alt="Picture of the author" className='h-full w-full object-cover rounded-lg' width={1000} height={1000} />
                </div>

                <div className="md:col-span-2 col-span-5 grid    grid-cols-2   gap-5  ">
                    {imagesArray.map((image, index) => (
                        <div key={index} className="h-[186px]">
                            <Image
                                src={image}
                                alt="Picture of the author"
                                className="h-full w-full object-cover rounded-lg"
                                width={1000}
                                height={1000}
                            />
                        </div>
                    ))}
                </div>


            </div>
        </div>
    );
};

export default DetailsImagesPreview2;
