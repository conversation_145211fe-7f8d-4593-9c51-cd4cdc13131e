import React, { useEffect, useState } from 'react';
import { FeatureConstants } from '@/app/constants/subscriptionEnums';

interface FeatureModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: FeatureFormData) => void;
  mode: 'add' | 'edit';
  initialValues?: FeatureFormData;
}

export interface FeatureFormData {
  featureId?: number;
  name: string;
  type: string;
  displayOrder: number;
  constants: string;
  // active: boolean;
}

const typeOptions = [
  { value: 'TEXT', label: 'Text' },
  { value: 'NUMERIC', label: 'Numeric' },
  { value: 'ENUM', label: 'Enum' },
  { value: 'BOOLEAN', label: 'Boolean' },
];

const constantsOptions = [
  { value: '', label: 'None' },
  ...Object.values(FeatureConstants).map(constant => ({ value: constant, label: constant.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) }))
];

function normalizeType(type: string | undefined): string {
  if (!type) return 'TEXT';
  const upper = type.toUpperCase();
  if ([
    'TEXT', 'NUMERIC', 'ENUM', 'BOOLEAN'
  ].includes(upper)) return upper;
  // legacy or fallback
  if (upper === 'STRING') return 'TEXT';
  if (upper === 'NUMBER') return 'NUMERIC';
  return 'TEXT';
}

const FeatureModal: React.FC<FeatureModalProps> = ({ isOpen, onClose, onSave, mode, initialValues }) => {
  const [form, setForm] = useState<FeatureFormData>({
    name: '',
    type: 'TEXT',
    displayOrder: 1,
    constants: '',
    // active: true,
  });

  useEffect(() => {
    if (isOpen) {
      setForm(
        initialValues
          ? {
              ...initialValues,
              type: normalizeType((initialValues as any).type || (initialValues as any).featureType),
            }
          : {
              name: '',
              type: 'TEXT',
              displayOrder: 1,
              constants: '',
            }
      );
    }
  }, [isOpen, initialValues]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-30">
      <div className="bg-white rounded-xl shadow-lg p-8 w-full max-w-lg relative">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-[#2d2d2e]">
            {mode === 'edit' ? 'Edit Feature' : 'Add New Feature'}
          </h2>
          <button onClick={onClose} className="text-2xl text-[#993333] absolute right-6 top-8">&times;</button>
        </div>
        {/* Form */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-[#2d2d2e] mb-1">Feature Name</label>
          <input
            type="text"
            className="border rounded px-3 py-2 w-full"
            placeholder="Feature Name"
            value={form.name}
            onChange={e => setForm({ ...form, name: e.target.value })}
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-[#2d2d2e] mb-1">Display Order</label>
          <input
            type="number"
            className="border rounded px-3 py-2 w-full"
            placeholder="Display Order"
            min={1}
            value={form.displayOrder}
            onChange={e => setForm({ ...form, displayOrder: Number(e.target.value) })}
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-[#2d2d2e] mb-1">Type</label>
          <select
            className="border rounded px-3 py-2 w-full"
            value={form.type}
            onChange={e => setForm({ ...form, type: e.target.value })}
          >
            {typeOptions.map(opt => (
              <option key={opt.value} value={opt.value}>{opt.label}</option>
            ))}
          </select>
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-[#2d2d2e] mb-1">Constants</label>
          <select
            className="border rounded px-3 py-2 w-full"
            value={form.constants}
            onChange={e => setForm({ ...form, constants: e.target.value })}
          >
            {constantsOptions.map(opt => (
              <option key={opt.value} value={opt.value}>{opt.label}</option>
            ))}
          </select>
        </div>
        {/* <div className="flex items-center mb-6">
          <label className="relative inline-flex items-center cursor-pointer select-none">
            <input
              type="checkbox"
              checked={form.active}
              onChange={e => setForm({ ...form, active: e.target.checked })}
              className="sr-only peer"
            />
            <div className={`w-10 h-6 rounded-full transition-colors duration-200 relative ${form.active ? 'bg-[#2d2d2e]' : 'bg-[#9f9fa7]'}`}> 
              <div className={`absolute top-0.5 left-0.5 w-5 h-5 rounded-full bg-white shadow-md transition-transform duration-200 ${form.active ? 'translate-x-4' : ''}`}></div>
            </div>
            <span className="ml-3 text-base font-medium">{form.active ? 'Active' : 'Inactive'}</span>
          </label>
        </div> */}
        {/* Action Buttons */}
        <div className="flex justify-end gap-3 mt-2">
          <button
            onClick={onClose}
            className="min-w-[120px] bg-[#993333] hover:bg-[#711d1d] text-white px-4 py-2 rounded font-medium"
          >
            Cancel
          </button>
          <button
            onClick={() => onSave(form)}
            className="min-w-[120px] bg-[#993333] hover:bg-[#711d1d] text-white px-4 py-2 rounded font-medium"
          >
            {mode === 'edit' ? 'Save Changes' : 'Add Feature'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FeatureModal; 