import { showMessage } from '@/app/lib/Alert';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import React, { useState, useRef } from 'react';

interface BulkUploadPopupProps {
    isOpen: boolean;
    onClose: () => void;
    refreshLeads?: () => void;
}

const BulkUploadPopup: React.FC<BulkUploadPopupProps> = ({ isOpen, onClose, refreshLeads }) => {
    const [csvData, setCsvData] = useState<string>('');
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [isDragging, setIsDragging] = useState(false);
    const [uploading, setUploading] = useState(false);

    if (!isOpen) return null;

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);
        const file = e.dataTransfer.files[0];
        if (file && file.name.endsWith('.csv')) {
            readFile(file);
        }
    };

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            readFile(file);
        }
    };

    const readFile = (file: File) => {
        const reader = new FileReader();
        reader.onload = (e) => {
            const text = e.target?.result as string;
            setCsvData(text);
        };
        reader.readAsText(file);
    };

    const handleUpload = async () => {
        if (!csvData.trim()) return;

        try {
            setUploading(true);
            const formData = new FormData();

            formData.append('csvText', csvData);

            const res = await fetch(API_ENDPOINTS.LEAD_BULK, {
                method: 'POST',
                credentials: 'include',
                body: formData,
            });

            const payload = await res.json().catch(() => ({})); // handle 204, etc.

            if (!res.ok) {
                throw new Error(payload.message || 'Bulk upload failed');
            }

            showMessage(`Imported ${payload.data?.created ?? 0} lead${(payload.data?.created ?? 0) !== 1 ? 's' : ''} • ${payload.data?.failed ?? 0} failed`, 'success');

            refreshLeads?.(); // refresh list in parent
            setCsvData(''); // clear input
            onClose(); // close modal
        } catch (err: any) {
            showMessage(err.message ?? 'Request failed', 'error');
        } finally {
            setUploading(false);
        }
    };

    const downloadTemplate = () => {
        const template =
            'Name,Email,Phone,LicenseNumber,Company,Type,Source,Status\n' +
            'John Agent,<EMAIL>,+**********,A12345,John Real Estate,agent,Website Form,New\n' +
            'Skyline Realty,<EMAIL>,+**********,,Skyline Realty,agency,Cold Outreach,New';
        const blob = new Blob([template], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'leads_template.csv';
        a.click();
        window.URL.revokeObjectURL(url);
    };

    return (
        <div className= "">
   <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 font-inter">
            <div className="relative max-h-[90vh] w-full max-w-xl overflow-y-auto rounded-lg bg-white p-6">
                {uploading && (
                    <div className="absolute inset-0 flex items-center justify-center rounded-lg bg-white/70">
                        <svg className="h-10 w-10 animate-spin text-[#1D7EB6]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 4v1m6.364 1.636l-.707.707M20 12h-1m-1.636 6.364l-.707-.707M12 20v-1m-6.364-1.636l.707-.707M4 12h1m1.636-6.364l.707.707"
                            />
                        </svg>
                    </div>
                )}
                {/* Header */}
                <div className="mb-6 flex items-center justify-between">
                    <h2 className="flex items-center text-xl font-semibold text-gray-900">
                        <svg className="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                        Bulk Upload Leads
                    </h2>
                    <button onClick={downloadTemplate} className="flex items-center text-[#1D7EB6] hover:text-[#1D7EB6]/80">
                        <svg className="mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Download Template
                    </button>
                </div>

                {/* CSV File Upload Section */}
                <div className="mb-6">
                    <p className="mb-2 text-sm text-gray-600">CSV File Upload</p>
                    <div
                        className={`rounded-lg border-2 border-dashed p-8 text-center ${isDragging ? 'border-[#1D7EB6] bg-[#1D7EB6]/5' : 'border-gray-300'}`}
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={handleDrop}
                    >
                        <div className="flex flex-col items-center">
                            <svg className="mb-4 h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                            </svg>
                            <p className="mb-2 text-sm text-gray-600">Click to upload CSV file or drag and drop</p>
                            <p className="text-xs text-gray-500">CSV files only (Name, Email, Phone, Company, Type, Source, Status)</p>
                        </div>
                        <input type="file" ref={fileInputRef} onChange={handleFileSelect} accept=".csv" className="hidden" />
                        <button onClick={() => fileInputRef.current?.click()} className="mt-4 rounded-md border border-[#1D7EB6] px-4 py-2 text-sm font-medium text-[#1D7EB6] hover:bg-[#1D7EB6]/5">
                            Select File
                        </button>
                    </div>
                </div>

                {/* OR Divider */}
                <div className="relative my-8">
                    <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t border-gray-200"></div>
                    </div>
                    <div className="relative flex justify-center">
                        <span className="bg-white px-4 text-sm text-gray-500">OR</span>
                    </div>
                </div>

                {/* Paste CSV Data Section */}
                <div className="mb-6">
                    <label className="mb-2 block text-sm text-gray-600">Paste CSV Data</label>
                    <textarea
                        value={csvData}
                        onChange={(e) => setCsvData(e.target.value)}
                        placeholder="Name,Email,Phone,Company,Type,Source,Status&#13;&#10;John Agent,<EMAIL>,+15551234567,John Real Estate,Agent,Website Form,New"
                        className="h-32 w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                    />
                    <p className="mt-1 text-xs text-gray-500">Format: Name, Email, Phone, Company, Type, Source, Status (one lead per line)</p>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end gap-3">
                    <button
                        onClick={() => {
                            setCsvData('');
                            onClose?.();
                        }}
                        disabled={uploading}
                        className="rounded-md border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={handleUpload}
                        className={`flex items-center rounded-md px-4 py-2 text-sm font-medium text-white ${uploading ? 'cursor-not-allowed bg-gray-400' : 'bg-[#1D7EB6] hover:bg-[#1D7EB6]/90'}`}
                        disabled={uploading}
                    >
                        {uploading ? (
                            <span className="animate-pulse">Uploading…</span>
                        ) : (
                            <>
                                <svg className="mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                </svg>
                                Upload Leads
                            </>
                        )}
                    </button>
                </div>
            </div>
        </div>
        </div>
     
    );
};

export default BulkUploadPopup;
