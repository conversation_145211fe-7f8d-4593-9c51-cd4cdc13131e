import React from 'react';
import Image from 'next/image';
import LoginImage from '@/../public/LogoLogin.png';

const ImageComponent = () => {
    return (
        <div className="relative w-full min-h-screen">
            <Image className="w-full h-full object-cover" src={LoginImage} alt="Login Image" width={800} height={800} />

            <div className="absolute left-1/2  bottom-2 w-full px-32 -translate-x-1/2 -translate-y-1/2 transform">
                <h2 className="font-golosText mb-0 rounded-3xl bg-white px-8 py-5 text-center text-[22px] font-black leading-[120%] text-[#1D7EB6] lg:text-[38px] lg:leading-[110%]">
                    Verified. Real. Trusted.
                </h2>
            </div>
        </div>
    );
};

export default ImageComponent;
