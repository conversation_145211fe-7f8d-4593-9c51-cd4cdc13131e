import React, { useState } from 'react';

function Search() {
    const [searchQuery, setSearchQuery] = useState('');

    const options = ['Palm Jumeirah', 'Jumeirah Beach Residence', 'Marina Diamonds'];

    const [selected, setSelected] = useState(options);
    const filtered = options.filter((option: any) => option.toLowerCase().includes(searchQuery.toLowerCase()) && !selected.includes(option));

    const handleSelect = (option: string) => {
        setSelected((prev: any) => [...prev, option]);
        setSearchQuery('');
    };

    const handleRemove = (option: string) => {
        setSelected(selected.filter((loc: any) => loc !== option));
    };

    return (
        <div className="w-full">
            <h1 className="mb-4 text-lg font-semibold text-gray-900"> </h1>

            <div className="relative mb-4">
                <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="border-borderColor w-full rounded-lg border py-2 pl-10 pr-4 text-gray-600 placeholder:text-gray-400 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {searchQuery && filtered.length > 0 && (
                    <ul className="absolute z-10 mt-1 w-full rounded-lg border border-gray-200 bg-white shadow-lg">
                        {filtered.map((option: any) => (
                            <li key={option} onClick={() => handleSelect(option)} className="cursor-pointer px-4 py-2 hover:bg-gray-100">
                                {option}
                            </li>
                        ))}
                    </ul>
                )}
            </div>

            <div className="flex flex-wrap gap-2">
                {selected.map((option: any) => (
                    <div key={option} className="text-red_primary flex items-center gap-2 rounded-full border   bg-slate-100 px-2 py-0.5 text-sm">
                        <span>{option}</span>
                        <button onClick={() => handleRemove(option)} className="rounded-full p-0.5 transition-colors">
                            <span className="sr-only">Remove {option}</span>
                        </button>
                    </div>
                ))}
            </div>
        </div>
    );
}
