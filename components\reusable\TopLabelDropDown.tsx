import { useState, useRef, useEffect, FC } from "react";
import { DownIcon } from "../icon/Icon";
 

const TopLabelDropDown = ({ items, title, initialValue }: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);

  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => setIsOpen((prev) => !prev);

  const handleSelect = (item: string) => {
    setSelectedItem(item);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div
    ref={dropdownRef}
    tabIndex={0}   
    className="dropdown w-full relative cursor-pointer rounded-lg border border-blue-500  ring-1  ring-blue-500  font-inter px-2 py-2"
  >
    <div
      className="flex justify-between items-center rounded-lg"
      onClick={toggleDropdown}
    >
      <div className="px-1 relative">
        <p className="text-xs">{title}</p>
        <button className="dropdown-button text-sm">
          {selectedItem || initialValue}
        </button>
      </div>
      <div className="pr-2">
        <DownIcon />
      </div>
    </div>
  
    {isOpen && (
      <ul className="dropdown-menu absolute z-10 border top-14 rounded-lg border-[#E4E4E4] left-0 bg-slate-50 shadow-lg min-w-56 py-1 mt-1 w-full">
        {items.map((item: any, index: any) => (
          <li 
            key={index}
            className="dropdown-item py-2   px-3 hover:bg-gray-200 hover:text-[#1D7EB6] cursor-pointer"
            onClick={() => handleSelect(item)}
          >
            {item}
          </li>
        ))}
      </ul>
    )}
  </div>
  
  );
};

export default TopLabelDropDown;
 