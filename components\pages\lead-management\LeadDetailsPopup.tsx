import React from 'react';

interface LeadDetailsPopupProps {
    isOpen: boolean;
    onClose: () => void;
    name: string;
    company: string;
    type: string;
    email: string;
    phone: string;
    addedDate: string;
    lastContact?: string;
    source: string;
    status: string;
}

const LeadDetailsPopup: React.FC<LeadDetailsPopupProps> = ({ isOpen, onClose, name, company, type, email, phone, addedDate, lastContact, source, status }) => {
    if (!isOpen) return null;

    // Get initials from company name
    const initials = name
        .split(' ')
        .map((word) => word[0])
        .join('')
        .toUpperCase()
        .slice(0, 3);

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 font-inter" >
            <div className="relative w-full max-w-md rounded-lg bg-white">
                {/* Close button */}
                <button onClick={onClose} className="absolute right-3 top-3 text-gray-400 hover:text-gray-600">
                    ×
                </button>

                {/* Header */}
                <div className="p-6">
                    <h2 className="mb-6 text-lg font-medium">Lead Details</h2>

                    {/* Company Info */}
                    <div className="mb-4 flex items-center gap-4">
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-[#1D7EB6] text-lg font-medium text-white">{initials}</div>
                        <div>
                            <h3 className="text-base font-medium">{name}</h3>
                            <p className="text-sm text-gray-600">{company}</p>
                        </div>
                    </div>

                    {/* Badges */}
                    <div className="mb-6 flex gap-2">
                        <span className="rounded-full bg-purple-100 px-3 py-1 text-sm text-purple-800">{type}</span>
                        <span className="rounded-full bg-yellow-100 px-3 py-1 text-sm text-yellow-800">{status}</span>
                    </div>

                    {/* Lead Information */}
                    <div className="space-y-4">
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                />
                            </svg>
                            <span>{email}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                                />
                            </svg>
                            <span>{phone}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <span>Added: {addedDate}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Last contact: {lastContact}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2"
                                />
                            </svg>
                            <span>Source: {source}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default LeadDetailsPopup;
