import { FC } from 'react';

interface IconEyeProps {
    className?: string;
}

const IconEye: FC<IconEyeProps> = ({ className }) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
        <mask id="mask0_21130_1474"   maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
          <rect x="0.25" width="24" height="24" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_21130_1474)">
          <path d="M12.2523 15.5769C13.3854 15.5769 14.3477 15.1803 15.1394 14.3871C15.9311 13.5939 16.3269 12.6308 16.3269 11.4977C16.3269 10.3646 15.9303 9.40224 15.1372 8.61058C14.344 7.81891 13.3808 7.42308 12.2477 7.42308C11.1146 7.42308 10.1523 7.81966 9.3606 8.61282C8.56893 9.40601 8.1731 10.3692 8.1731 11.5023C8.1731 12.6353 8.56968 13.5977 9.36285 14.3894C10.156 15.181 11.1192 15.5769 12.2523 15.5769ZM12.25 14.2C11.5 14.2 10.8625 13.9375 10.3375 13.4125C9.8125 12.8875 9.55 12.25 9.55 11.5C9.55 10.75 9.8125 10.1125 10.3375 9.58748C10.8625 9.06248 11.5 8.79998 12.25 8.79998C13 8.79998 13.6375 9.06248 14.1625 9.58748C14.6875 10.1125 14.95 10.75 14.95 11.5C14.95 12.25 14.6875 12.8875 14.1625 13.4125C13.6375 13.9375 13 14.2 12.25 14.2ZM12.25 18.5C10.0616 18.5 8.05547 17.9243 6.23175 16.773C4.40805 15.6217 3.01735 14.0653 2.05965 12.1038C2.01607 12.0269 1.98594 11.9353 1.96927 11.8292C1.95261 11.723 1.94427 11.6132 1.94427 11.5C1.94427 11.3867 1.95261 11.277 1.96927 11.1708C1.98594 11.0646 2.01607 10.9731 2.05965 10.8961C3.01735 8.93461 4.40805 7.37821 6.23175 6.22693C8.05547 5.07564 10.0616 4.5 12.25 4.5C14.4384 4.5 16.4445 5.07564 18.2682 6.22693C20.0919 7.37821 21.4827 8.93461 22.4404 10.8961C22.4839 10.9731 22.5141 11.0646 22.5307 11.1708C22.5474 11.277 22.5557 11.3867 22.5557 11.5C22.5557 11.6132 22.5474 11.723 22.5307 11.8292C22.5141 11.9353 22.4839 12.0269 22.4404 12.1038C21.4827 14.0653 20.0919 15.6217 18.2682 16.773C16.4445 17.9243 14.4384 18.5 12.25 18.5ZM12.25 17C14.1333 17 15.8625 16.5041 17.4375 15.5125C19.0125 14.5208 20.2167 13.1833 21.05 11.5C20.2167 9.81664 19.0125 8.47914 17.4375 7.48748C15.8625 6.49581 14.1333 5.99998 12.25 5.99998C10.3667 5.99998 8.6375 6.49581 7.0625 7.48748C5.4875 8.47914 4.28333 9.81664 3.45 11.5C4.28333 13.1833 5.4875 14.5208 7.0625 15.5125C8.6375 16.5041 10.3667 17 12.25 17Z" fill="#1D7EB6"/>
        </g>
      </svg>
    );
};

export default IconEye;
