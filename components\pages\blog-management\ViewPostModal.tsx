'use client';

import React, { useEffect, useState } from 'react';
import Modal from '@/components/reusable/modals/modal';
import DeletePostModal from './DeletePostModal';
import API_ENDPOINTS from '@/app/lib/apiRoutes';

interface ViewPostModalProps {
    post: any;
    onClose: () => void;
    onDelete?: (id: number) => void;
}

const getStatusBadgeColor = (status: string) => {
    switch (status) {
        case 'Published':
            return 'bg-green-100 text-green-800';
        case 'Draft':
            return 'bg-yellow-100 text-yellow-800';
        case 'Archived':
            return 'bg-gray-100 text-gray-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

export default function ViewPostModal({ post, onClose, onDelete }: ViewPostModalProps) {
    const [warningModal, setWarningModal] = useState(false);
    const [formData, setFormData] = useState({
        title: '',
        author: '',
        excerpt: '',
        content: '',
        category: '',
        featuredImage: '',
        status: '',
        tags: '',
        updated: '',
        created: '',
        published: '',
        views: 0,
        likes: 0,
    });
    const [loading, setLoading] = useState(true);

    const handleDeleteClick = () => {
        setWarningModal(true);
    };

    const handleDeleteConfirm = () => {
        setWarningModal(false);
        if (onDelete) {
            onDelete(post.id);
        }
        onClose();
    };

    useEffect(() => {
        const fetchBlogPost = async () => {
            try {
                setLoading(true);
                const response = await fetch(API_ENDPOINTS.BLOG_GET_ONE + post.slug, {
                    method: 'GET',
                    credentials: 'include',
                     
                });

                const result = await response.json();
                if (result.success) {
                    const data = result.data;
                    setFormData({
                        title: data.title,
                        author: data.author,
                        excerpt: data.excerpt,
                        content: data.content,
                        category: data.category,
                        featuredImage: data.featuredImage,
                        status: data.statusId === 22 ? 'Published' : data.statusId === 23 ? 'Draft' : 'Archived',
                        tags: data.tags,
                        updated: new Date(data.updated_at).toLocaleDateString(),
                        created: new Date(data.created_at).toLocaleDateString(),
                        published: data.publishDate ? new Date(data.publishDate).toLocaleDateString() : '',
                        views: data.views || 0,
                        likes: data.likes || 0,
                    });
                } else {
                    console.log('Failed to fetch blog:', result.message);
                }
            } catch (error) {
                console.error('Error fetching blog:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchBlogPost();
    }, [post]);

    if (loading) {
        return <div className="p-4 text-center text-gray-500">Loading post details...</div>;
    }

    const handleDelete = () => {
        setWarningModal(true);
    };

    return (
        <>
            <Modal isOpen={warningModal} onClose={() => setWarningModal(false)}>
                <DeletePostModal
                    post={post}
                    onClose={() => setWarningModal(false)}
                    onConfirm={handleDeleteConfirm}
                />
            </Modal>

            <div className="max-w-6xl mx-auto bg-white rounded-lg overflow-hidden max-h-[90vh] overflow-y-auto p-4">
                <div className="px-6 py-4 border-b border-gray-200">
                    <div className="flex justify-between items-start mb-4">
                        <h2 className="text-lg font-semibold text-[#2d2d2e] pr-4 font-inter">{formData.title}</h2>
                        <div className="flex items-center gap-2">
                            {onDelete && (
                                <button
                                    onClick={handleDeleteClick}
                                    className="flex items-center gap-1 px-3 py-1 text-sm text-red-600 border border-red-300 rounded hover:bg-red-50 transition-colors font-inter"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 002 2h8a2 2 0 002-2V7H6z" />
                                    </svg>
                                    Delete
                                </button>
                            )}
                            <button
                                onClick={onClose}
                                className="text-gray-400 hover:text-gray-600 text-xl w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100"
                            >
                                ×
                            </button>
                        </div>
                    </div>

                    <div className="flex items-center gap-4 text-sm text-[#636363] mb-4 font-inter">
                        <div className="flex items-center gap-1">
                            👤 {formData.author}
                        </div>
                        <div className="flex items-center gap-1">
                            🕒 {formData.updated}
                        </div>
                        <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold font-inter ${getStatusBadgeColor(formData.status)}`}>
                            {formData.status}
                        </span>
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-semibold font-inter">
                            {formData.category}
                        </span>
                    </div>

                    <div className="flex items-center gap-6 text-sm text-[#636363] font-inter">
                        <div className="flex items-center gap-1">👁️ {formData.views.toLocaleString()} views</div>
                        <div className="flex items-center gap-1">❤️ {formData.likes} likes</div>
                        <div className="flex items-center gap-1">💬 12 comments</div>
                    </div>
                </div>

                <div className="px-6 py-4">
                    <img
                        src={formData.featuredImage}
                        alt={formData.title}
                        className="w-full h-48 object-cover rounded-lg"
                    />
                </div>

                <div className="px-6 py-4">
                    <h3 className="text-sm font-semibold text-[#2d2d2e] mb-3 font-inter">Excerpt</h3>
                    <div className="bg-blue-50 p-4 rounded-lg">
                        <p className="text-sm text-blue-700 leading-relaxed font-inter">
                            {formData.excerpt}
                        </p>
                    </div>
                </div>

               <div className="px-6 py-4">
                    <h3 className="text-sm font-semibold text-[#2d2d2e] mb-3 font-inter">Content</h3>
                    <div
                        className="text-sm text-[#636363] leading-relaxed font-inter"
                        dangerouslySetInnerHTML={{ __html: formData.content }}
                    />
                    </div>

                <div className="px-6 py-4">
                    <h3 className="text-sm font-semibold text-[#2d2d2e] mb-3 font-inter">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                        {formData.tags ? formData.tags.split(',').map((tag, index) => (
                            <span key={index} className="border border-blue-300 text-blue-600 px-3 py-1 rounded text-xs font-inter">
                                #{tag.trim()}
                            </span>
                        )) : <span className="text-gray-500">No tags</span>}
                    </div>
                </div>

                <div className="px-6 py-4 border-t border-gray-200">
                    <h3 className="text-sm font-semibold text-[#2d2d2e] mb-4 font-inter">Publishing Information</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm font-inter">
                        <div>
                            <span className="text-[#636363]">Created:</span>
                            <div className="text-[#2d2d2e] font-medium">{formData.created}</div>
                        </div>
                        <div>
                            <span className="text-[#636363]">Updated:</span>
                            <div className="text-[#2d2d2e] font-medium">{formData.updated}</div>
                        </div>
                        <div className="col-span-2">
                            <span className="text-[#636363]">Published:</span>
                            <div className="text-[#2d2d2e] font-medium">{formData.published || 'Not published'}</div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
