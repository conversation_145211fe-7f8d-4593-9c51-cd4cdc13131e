import { FC } from 'react';

interface IconPaperclipProps {
    className?: string;
}

const IconPaper: FC<IconPaperclipProps> = ({ className }) => {
    return (
        <svg width="38" height="38" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
            <path d="M1.18749 5.9375V19C1.18749 19.9448 1.56283 20.851 2.23093 21.5191C2.89902 22.1872 3.80516 22.5625 4.74999 22.5625H10.6875C11.0024 22.5625 11.3045 22.4374 11.5272 22.2147C11.7499 21.992 11.875 21.6899 11.875 21.375V7.125C11.8767 5.9682 11.5405 4.83609 10.9076 3.86777C10.2747 2.89945 9.37272 2.13709 8.31249 1.67437H8.25312C7.52115 1.3602 6.73401 1.1947 5.93749 1.1875C4.67772 1.1875 3.46954 1.68795 2.57874 2.57874C1.68794 3.46954 1.18749 4.67772 1.18749 5.9375Z" fill="#C19A00" />
            <path d="M5.9374 1.1875C7.1851 1.55698 8.29843 2.2813 9.14179 3.27226C9.98516 4.26321 10.5222 5.47803 10.6874 6.76875V7.125V32.9412C10.6649 33.4619 10.7726 33.98 11.0006 34.4486C11.2287 34.9171 11.57 35.3215 11.9937 35.625C12.5154 35.9576 13.1283 36.1182 13.7461 36.0841C14.3639 36.05 14.9554 35.823 15.4374 35.435C15.7445 35.1158 16.1136 34.8627 16.522 34.6912C16.9304 34.5197 17.3695 34.4333 17.8124 34.4375C18.2277 34.4338 18.6395 34.5139 19.0231 34.6731C19.4067 34.8322 19.7542 35.0671 20.0449 35.3637C21.0591 36.2942 22.3854 36.8104 23.7618 36.8104C25.1381 36.8104 26.4644 36.2942 27.4787 35.3637C27.7645 35.0673 28.1078 34.8322 28.4876 34.673C28.8673 34.5137 29.2756 34.4336 29.6874 34.4375C30.0968 34.4352 30.5027 34.5137 30.8817 34.6686C31.2608 34.8235 31.6055 35.0516 31.8962 35.34L32.003 35.435C32.4368 35.7833 32.9604 36.0016 33.5131 36.0646C34.0658 36.1277 34.6251 36.0329 35.1261 35.7912C35.8286 35.452 36.37 34.8508 36.6343 34.1169C36.7549 33.7942 36.8153 33.452 36.8124 33.1075V8.3125C36.8124 6.42283 36.0617 4.61056 34.7255 3.27436C33.3893 1.93817 31.5771 1.1875 29.6874 1.1875H5.9374Z" fill="#FFCC00" />
            <path d="M15.6611 14.2503C15.6611 14.644 15.8175 15.0215 16.0959 15.2999C16.3743 15.5783 16.7518 15.7347 17.1455 15.7347H30.208C30.5504 15.7379 30.883 15.6208 31.1478 15.4037C31.4126 15.1867 31.5928 14.8835 31.6568 14.5472C31.6691 14.4605 31.6691 14.3726 31.6568 14.2859C31.6615 14.091 31.6278 13.897 31.5575 13.7151C31.4873 13.5332 31.3819 13.3669 31.2473 13.2257C31.1128 13.0846 30.9517 12.9713 30.7734 12.8924C30.5951 12.8135 30.403 12.7705 30.208 12.7659H17.1455C17.0589 12.7536 16.9709 12.7536 16.8843 12.7659C16.5352 12.8233 16.2188 13.0054 15.9939 13.2784C15.7689 13.5514 15.6507 13.8967 15.6611 14.2503Z" fill="#C19A00" />
            <path d="M16.0576 21.3753C16.0576 21.769 16.214 22.1465 16.4924 22.4249C16.7708 22.7033 17.1483 22.8597 17.542 22.8597H27.042C27.3844 22.8629 27.717 22.7458 27.9818 22.5287C28.2466 22.3117 28.4267 22.0085 28.4907 21.6722C28.5031 21.5855 28.5031 21.4976 28.4907 21.4109C28.4955 21.216 28.4618 21.022 28.3915 20.8401C28.3212 20.6582 28.2158 20.4919 28.0813 20.3507C27.9468 20.2096 27.7857 20.0963 27.6074 20.0174C27.4291 19.9385 27.2369 19.8955 27.042 19.8909H17.542C17.4553 19.8786 17.3674 19.8786 17.2807 19.8909C16.9317 19.9483 16.6153 20.1304 16.3904 20.4034C16.1654 20.6764 16.0472 21.0217 16.0576 21.3753Z" fill="#C19A00" />
        </svg>
    );
};

export default IconPaper;


