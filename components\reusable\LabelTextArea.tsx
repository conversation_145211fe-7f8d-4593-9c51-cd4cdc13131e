interface TextAreaProps {
  label: string;
  id: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

const LabelTextArea: React.FC<TextAreaProps> = ({ label, id, value, onChange }) => {
  return (
    <div className="relative w-full">
      <textarea
        id={id}
        value={value}
        onChange={onChange}
        className="peer w-full border border-gray-300 rounded-lg p-3 pt-5 text-gray-900 focus:outline-none text-sm min-h-[130px] resize-none"
      />
      <label
        htmlFor={id}
        className={`absolute left-3 px-1 bg-white text-grayMain font-normal transition-all duration-300 peer-focus:-top-2 peer-focus:text-xs ${
          value ? "-top-2 text-xs" : "top-4 text-sm"
        }`}
      >
        {label}
      </label>
    </div>
  );
};

export default LabelTextArea;