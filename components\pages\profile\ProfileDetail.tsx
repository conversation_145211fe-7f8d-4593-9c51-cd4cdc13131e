'use client';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import React, { useEffect, useState } from 'react';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import { useRouter } from 'next/navigation';
import { AGENT_ACCOUNT_TYPE, AgentProfile, CompanyAgentFieldLabels, IndividualAgentFieldLabels } from '@/store/utils.ts/types/AgentProfile';
import SuccessfullyDeleted from '@/components/reusable/modals/SuccessfullyDeleted';
import Modal from '@/components/reusable/modals/modal';
import Loading from '@/components/layouts/loading';
import TextAreaInput from '@/components/reusable/TextAreaInput';
import { showMessage } from '@/app/lib/Alert';
import CompanyApplicationPreviewComponent from './CompanyApplicationPreview';
import IndividualApplicationPreviewComponent from './IndividualApplicationPreview';
import SearchDropDown from '@/components/reusable/SearchDropDown';

export const LICENSE_TAG_OPTIONS = [{ label: 'No Selection' }, { label: 'Licensed Broker' }, { label: 'Licensed Brokerage' }, { label: 'Licensed Developer' }
,
{ label: 'Licensed' }, { label: 'Certified' }, { label: 'Registered' }, { label: 'Authorized' }

];

const ApplicataionPreviewPageComponent = ({ pageData }: any) => {
    const id = pageData;
    const [updateModalReason, setUpdateModalReason] = useState(false);
    const router = useRouter();
    const [loader, setLoader] = useState(false);
    const [profileData, setProfileData] = useState<AgentProfile>();
    const [updateModalSuccess, setUpdateModalSuccess] = useState(false);
    const [selectedRecordId, setSelectedRecordId] = useState<number>();
    const [reasonError, setReasonError] = useState('');
    const [missingFields, setMissingFields] = useState<string[]>([]);
    const [reasons, setReasons] = useState<any[]>([]);
    const [expandedReasonId, setExpandedReasonId] = useState<number | null>(null);
    const [isPrivate, setIsPrivate] = useState(false);
    const [isLicensed, setIsLicensed] = useState(false);
    const [selectedStatusTag, setSelectedStatusTag] = useState('No Selection');

    const [successModalData, setSuccessModalData] = useState({
        title: '',
        description: '',
    });

    const [formValues, setFormValues] = useState({
        status: '',
        rejectionReason: '',
    });

    const GetProfileData = () => {
        try {
            const myHeaders = new Headers();
            setLoader(true);
            const requestOptions: RequestInit = {
                method: 'GET',
                headers: myHeaders,
                redirect: 'follow',
                credentials: 'include',
            };

            fetch(`${API_ENDPOINTS.AGENT_APPLICATIONS}/${id}`, requestOptions)
                .then((response) => response.json())
                .then((result) => {
                    if (result.success) {
                        setProfileData({ ...result.data });
                        setIsLicensed(result?.data?.isLicensed);
                        setSelectedStatusTag(result?.data?.licenseTag || 'No Selection');
                    }
                    setLoader(false);
                })
                .catch((error) => {
                    setLoader(false);
                    console.error(error);
                });
        } catch (error) {
            setLoader(false);
            console.error(error);
        }
    };

    useEffect(() => {
        GetProfileData();
        setSelectedRecordId(id);
    }, [id]);

    const updateApplicationStatus = async (id: number) => {
        if (!id || !formValues.status) {
            showMessage('Missing required data.', 'error');
            return;
        }

        try {
            setLoader(true);
            if (formValues.status && (formValues.status.trim().toLowerCase() === 'rejected' || formValues.status.trim().toLowerCase() === 'incomplete') && formValues.rejectionReason.trim() === '') {
                setUpdateModalReason(true);
                setReasonError('Reason field is required!');
                setLoader(false);
                return;
            }

            setUpdateModalReason(false);
            const formData = new FormData();
            formData.append('status', formValues.status);
            formData.append('rejectionReason', formValues.rejectionReason);
            formData.append('requiredFields', JSON.stringify(missingFields));
            formData.append('isPrivate', String(isPrivate));

            if (['activated', 'approved'].includes(formValues.status.trim().toLowerCase())) {
                formData.append('isLicensed', String(isLicensed));
            }

            const requestOptions: RequestInit = {
                method: 'PUT',
                headers: new Headers(),
                redirect: 'follow',
                credentials: 'include',
                body: formData,
            };

            const response = await fetch(`${API_ENDPOINTS.AGENT_APPLICATIONS}/${id || selectedRecordId}/status`, requestOptions);
            const result = await response.json();

            if (result.success) {
                setUpdateModalSuccess(true);
                setSuccessModalData({
                    title: 'Success',
                    description: 'Agent application status updated successfully.',
                });
                GetProfileData(); // Refresh list
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoader(false);
        }
    };

    const updateApplicationStatusToApproved = async (id: number, status: string) => {
        if (!id || !status) {
            showMessage('Missing required data.', 'error');
            return;
        }

        try {
            setLoader(true);
            setIsPrivate(false);
            setUpdateModalReason(false);
            setMissingFields([]);
            const formData = new FormData();
            formData.append('status', status);
            formData.append('rejectionReason', '');
            formData.append('requiredFields', JSON.stringify(missingFields));
            formData.append('isPrivate', String(isPrivate));

            if (['activated', 'approved'].includes(status.trim().toLowerCase())) {
                formData.append('isLicensed', String(isLicensed));
                const cleanedTag = selectedStatusTag && selectedStatusTag.trim().toLowerCase() !== 'no selection' ? selectedStatusTag : '';

                formData.append('licenseTag', cleanedTag);
            }

            const requestOptions: RequestInit = {
                method: 'PUT',
                headers: new Headers(),
                redirect: 'follow',
                credentials: 'include',
                body: formData,
            };

            const response = await fetch(`${API_ENDPOINTS.AGENT_APPLICATIONS}/${id || selectedRecordId}/status`, requestOptions);
            const result = await response.json();

            if (result.success) {
                setUpdateModalSuccess(true);
                setSuccessModalData({
                    title: 'Success',
                    description: 'Application status updated successfully.',
                });
                GetProfileData(); // Refresh list
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoader(false);
        }
    };

    // At the top of your component
    const fieldLabels = profileData?.profileAccountType === AGENT_ACCOUNT_TYPE.INDIVIDUAL ? IndividualAgentFieldLabels : CompanyAgentFieldLabels;

    const handleCheckboxChange = (field: string) => {
        setMissingFields((prev) => (prev.includes(field) ? prev.filter((f) => f !== field) : [...prev, field]));
    };

    const fetchReasonsForApplication = async (applicationId: number) => {
        try {
            setReasons([]); // clear previous
            setLoader(true);
            const response = await fetch(`${API_ENDPOINTS.AGENT_APPLICATIONS}/${applicationId}/getReasons`, {
                method: 'GET',
                credentials: 'include',
            });
            const result = await response.json();
            if (result.success) {
                setReasons(result.data.reasons || []);
            } else {
                console.error('Failed to fetch reasons:', result.message);
                setReasons([]);
            }
        } catch (error) {
            console.error('Error fetching reasons:', error);
            setReasons([]);
        } finally {
            setLoader(false);
        }
    };

    const handleAgentApplicationStatusUpdate = async (id: number, status: string) => {
        setSelectedRecordId(id);
        setMissingFields([]);
        setFormValues({
            status,
            rejectionReason: '',
        });
        setReasonError('');
        if (status && (status.trim().toLowerCase() === 'rejected' || formValues.status.trim().toLowerCase() === 'incomplete')) {
            await fetchReasonsForApplication(id);
            setUpdateModalReason(true);
            return;
        }

        await updateApplicationStatus(id);
    };

    useEffect(() => {
        if (updateModalSuccess) {
            const timeout = setTimeout(() => {
                router.push('/dashboard/agents');
            }, 2000);

            return () => clearTimeout(timeout);
        }
    }, [updateModalSuccess]);

    return (
        <>
            {loader && <Loading />}
            <Modal isOpen={updateModalSuccess} onClose={() => setUpdateModalSuccess(false)}>
                <SuccessfullyDeleted
                    onClose={() => {
                        setUpdateModalSuccess(false);
                        setSuccessModalData({
                            title: '',
                            description: '',
                        });

                        // Redirect after a short delay
                        setTimeout(() => {
                            router.push('/dashboard/agents');
                        }, 2000); // 2-second delay
                    }}
                    title={successModalData.title}
                    desc={successModalData.description}
                />
            </Modal>

            <Modal isOpen={updateModalReason} onClose={() => setUpdateModalReason(false)} classes="!max-w-3xl">
                <div className="mb-5 w-full font-inter">
                    {/* Reason History */}
                    <div className="px-6 py-4">
                        <h3 className="mb-2 text-base font-semibold text-[#2d2d2e]">Comment History</h3>
                        <div className="space-y-3">
                            {reasons.length > 0 ? (
                                reasons.map((reasonItem) => {
                                    const isExpanded = expandedReasonId === reasonItem.id;
                                    const displayText = isExpanded ? reasonItem.reason : reasonItem.reason.length > 50 ? `${reasonItem.reason.substring(0, 50)}...` : reasonItem.reason;

                                    return (
                                        <div key={reasonItem.id} className="rounded-md border border-gray-200 bg-gray-50 p-3">
                                            <div className="flex items-center justify-between">
                                                <span className="font-semibold text-[#2d2d2e]">
                                                    {reasonItem.firstName} {reasonItem.lastName}
                                                </span>
                                                <div className="flex items-center gap-2">
                                                    {reasonItem.isPrivate && <span className="rounded bg-gray-200 px-2 py-1 text-xs font-semibold text-gray-700">Private</span>}
                                                    {reasonItem.created_at && <span className="text-xs text-[#888]">{new Date(reasonItem.created_at).toLocaleString()}</span>}
                                                </div>
                                            </div>

                                            <div className="mt-1 flex items-center gap-2 text-xs italic text-[#666]">
                                                {reasonItem.oldStatusName && <span className="font-medium text-[#444]">{reasonItem.oldStatusName}</span>}

                                                {reasonItem.oldStatusName && reasonItem.newStatusName && reasonItem.oldStatusName !== reasonItem.newStatusName && (
                                                    <>
                                                        {/* Arrow icon */}
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#888]" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                                            <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                                                        </svg>
                                                        <span className="font-medium text-[#444]">{reasonItem.newStatusName}</span>
                                                    </>
                                                )}

                                                {reasonItem.oldStatusName && reasonItem.newStatusName && reasonItem.oldStatusName === reasonItem.newStatusName && (
                                                    <span className="font-medium text-[#444]">(current)</span>
                                                )}
                                            </div>

                                            <div
                                                className={`transition-max-height mt-1 overflow-hidden whitespace-pre-wrap break-words text-sm text-[#555] duration-300 ease-in-out ${
                                                    isExpanded ? 'max-h-[1000px]' : 'max-h-[100px]'
                                                }`}
                                            >
                                                {displayText}
                                            </div>
                                            {reasonItem.reason.length > 50 && (
                                                <button
                                                    onClick={() => setExpandedReasonId(isExpanded ? null : reasonItem.id)}
                                                    className="mt-1 text-sm text-blue-500 hover:underline focus:outline-none"
                                                >
                                                    {isExpanded ? 'Show Less' : 'Show More'}
                                                </button>
                                            )}
                                        </div>
                                    );
                                })
                            ) : (
                                <p className="text-sm text-[#888]">No comment history found.</p>
                            )}
                        </div>
                    </div>

                    {/* Reason TextArea */}
                    <div className="flex flex-col px-6 pt-4">
                        <p className="text-lg font-bold text-[#2d2d2e]">Reason</p>
                        <div className="grid grid-cols-1 gap-5">
                            <TextAreaInput
                                id="rejection-reason"
                                label=""
                                value={formValues.rejectionReason}
                                onChange={(e) =>
                                    setFormValues({
                                        ...formValues,
                                        rejectionReason: e.target.value,
                                    })
                                }
                                placeholder="Reason*"
                            />
                            <span className="text-sm text-red-600">{reasonError}</span>
                        </div>
                    </div>

                    {/* Missing Fields */}
                    <div className="px-6 pb-3">
                        <p className="py-4 text-sm font-medium text-gray-700">Missing / Invalid Fields ({profileData?.profileAccountType === AGENT_ACCOUNT_TYPE.INDIVIDUAL ? 'Agent' : 'Agency'}):</p>
                        <div className="grid grid-cols-2 gap-2">
                            {Object.entries(fieldLabels).map(([key, label]) => (
                                <label key={key} className="flex cursor-pointer items-center text-sm">
                                    <input type="checkbox" className="mr-2 cursor-pointer" checked={missingFields.includes(key)} onChange={() => handleCheckboxChange(key)} />
                                    {label}
                                </label>
                            ))}
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end border-t border-gray-200 px-6 py-4">
                        <button
                            onClick={() => updateApplicationStatus(selectedRecordId!)}
                            className="flex h-[46px] w-40 cursor-pointer items-center justify-center gap-2.5 rounded-md bg-blueMain px-3 text-base font-normal text-white hover:border hover:border-blueMain hover:bg-white hover:text-blueMain"
                        >
                            Submit
                        </button>
                    </div>
                </div>
            </Modal>

            <DefaultPageLayout>
                <BreadCrums mainHeading="Application Preview" breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Agents', url: '/dashboard/agents' }, { text: 'Application Preview' }]} />
                {profileData && (
                    <>
                        <div className="relative grid grid-cols-1 pb-72 pt-4 font-inter md:grid-cols-12 md:pb-24 md:pe-2">
                            {/* Left Section (Profile Steps & Profile Info) */}
                            <div className="px-3 md:col-span-12 md:pl-5">
                                {/* Profile Info Section */}
                                <div className="mb-5 rounded-lg border font-inter text-textDarkGray  shadow-[0px_4px_20px_0px_rgba(21,32,70,0.07)]">
                                    {AGENT_ACCOUNT_TYPE.INDIVIDUAL === profileData.profileAccountType ? (
                                        <IndividualApplicationPreviewComponent getProfileData={profileData} />
                                    ) : (
                                        <CompanyApplicationPreviewComponent getProfileData={profileData} />
                                    )}
                                </div>
                            </div>
                        </div>
                      
                        <div className="fixed bottom-0 z-10 w-full md:-ms-5">
                            <div className="flex w-full flex-col flex-wrap items-center justify-between gap-5 border-t bg-white px-3 py-3 text-sm md:flex-row md:gap-0 md:px-7 md:py-4">
                                {/*  left-hand helper text  */}
                                <div className="w-full md:max-w-[30%]">
                                    <p className="pl-4 text-center font-inter text-sm text-redMain md:text-left md:text-base">
                                        Verify user details and documents before approval. If anything is missing or doesn’t meet the criteria, mark as incomplete or rejected, and provide a clear
                                        reason for resubmission.
                                    </p>
                                </div>

                                {/*  right-hand controls  */}
                                <div className="flex w-full flex-col md:w-1/2 md:items-end lg:pe-80">
                                    {/*  ─── select dropdown row ─── */}
                                    <div className="mb-4 flex w-full md:w-auto">
                                        <SearchDropDown
                                            classes="!h-14 w-full md:w-[287px]"
                                            dropdownOptions={LICENSE_TAG_OPTIONS}
                                            initail={selectedStatusTag}
                                            setSelectedStatus={setSelectedStatusTag}
                                        />
                                    </div>

                                    {/*  ─── buttons row ─── */}
                                    <div className="flex w-full flex-col items-center gap-3 md:flex-row md:justify-end">
                                        <button
                                            onClick={() => handleAgentApplicationStatusUpdate(id, 'Incomplete')}
                                            className={`flex h-11 w-full max-w-[160px] items-center justify-center rounded-md bg-[#ff9500] text-white
                                            ${profileData?.currentStatusName === 'Incomplete' ? '!cursor-not-allowed !bg-gray-400' : ''}`}
                                        >
                                            Incomplete
                                        </button>

                                        <button
                                            onClick={() => handleAgentApplicationStatusUpdate(id, 'Rejected')}
                                            className={`flex h-11 w-full max-w-[160px] items-center justify-center rounded-md bg-[#ff3b30] text-white
                                            ${profileData?.currentStatusName === 'Rejected' ? '!cursor-not-allowed !bg-gray-400' : ''}`}
                                        >
                                            Reject
                                        </button>

                                        <button
                                            onClick={() => updateApplicationStatusToApproved(id, 'Activated')}
                                            className={`flex h-11 w-full max-w-[160px] items-center justify-center rounded-md bg-[#2ab141] text-white
                                            ${profileData?.currentStatusName === 'Activated' ? '!cursor-not-allowed !bg-gray-400' : ''}`}
                                        >
                                            Approve
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </>
                )}
            </DefaultPageLayout>
        </>
    );
};

export default ApplicataionPreviewPageComponent;
