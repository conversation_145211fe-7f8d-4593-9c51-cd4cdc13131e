import { EditIcon } from "@/components/icon/Icon";
import { Details } from "./SubscribtionInfo";

export default function CardInfo() {
    return (
        <div className="border p-5 rounded-lg flex flex-col gap-4 py-7 font-inter">
            {/* Header Section */}
            <div className="flex items-center justify-between gap-2 flex-wrap">
                <p className="flex flex-wrap text-[20px] md:text-[26px] font-semibold leading-7">
                    <span className="text-redMain">Card </span>
                    <span className="text-[#2d2d2e]">&nbsp;Information</span>
                </p>
                <button>
                    <EditIcon className="w-5 h-5" />
                </button>
            </div>

            {/* Card Details */}
            <div className="text-sm flex flex-wrap gap-4 text-grayText">
                <Details head={'Cardholder Name:'} details={'M <PERSON>'} />
                <Details head={'Card Type:'} details={'Visa'} />
                <Details head={'Last Four Digits:'} details={'**** **** **** 1234'} />
            </div>
        </div>
    );
}