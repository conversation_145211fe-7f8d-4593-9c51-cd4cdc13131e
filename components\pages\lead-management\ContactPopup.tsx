import React from 'react';

interface ContactPopupProps {
    isOpen: boolean;
    onClose: () => void;
    onSend: (subject: string, message: string) => void;
    recipientEmail: string;
    recipientName: string;
    recipientCompany: string;
}

const ContactPopup: React.FC<ContactPopupProps> = ({
    isOpen,
    onClose,
    onSend,
    recipientEmail,
    recipientName,
    recipientCompany
}) => {
    if (!isOpen) return null;

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const formData = new FormData(e.target as HTMLFormElement);
        const subject = formData.get('subject') as string;
        const message = formData.get('message') as string;
        onSend(subject, message);
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 font-inter">
            <div className="bg-white rounded-lg w-full max-w-md p-6 relative">
                {/* Close button */}
                <button 
                    onClick={onClose}
                    className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                >
                    ×
                </button>

                {/* Header */}
                <div className="flex items-center mb-6 border-b pb-4">
                    <h2 className="text-base font-medium">Contact {recipientCompany}</h2>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                    {/* To Field */}
                    <div>
                        <label htmlFor="to" className="block text-sm font-medium text-gray-700 mb-1">
                            To
                        </label>
                        <input
                            type="text"
                            id="to"
                            value={recipientEmail}
                            disabled
                            className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-500"
                        />
                    </div>

                    {/* Subject Field */}
                    <div>
                        <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                            Subject
                        </label>
                        <input
                            type="text"
                            id="subject"
                            name="subject"
                            placeholder="Enter email subject"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#1D7EB6] focus:border-[#1D7EB6]"
                            required
                        />
                    </div>

                    {/* Message Field */}
                    <div>
                        <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                            Message
                        </label>
                        <textarea
                            id="message"
                            name="message"
                            placeholder="Enter your message"
                            rows={6}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#1D7EB6] focus:border-[#1D7EB6]"
                            required
                        />
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end gap-3 mt-6">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 border border-gray-300 rounded-md"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            className="px-4 py-2 text-sm font-medium text-white bg-[#1D7EB6] hover:bg-[#1D7EB6]/90 rounded-md"
                        >
                            Send Email
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default ContactPopup; 