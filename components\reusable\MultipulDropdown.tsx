"use client";

import React, { useState, useEffect, useRef } from "react";

interface Item {
    label: string;
}

const MultiSelectDropdown = ({ className, placeholder, optionsList }: any) => {
    const options: Item[] = optionsList || [
        { label: "1 Star" },
        { label: "2 Star" },
        { label: "3 Star" },
        { label: "4 Star" },
        { label: "5 Star" },
    ];

    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [selectedItems, setSelectedItems] = useState<string[]>([]);
    const dropdownContainerRef = useRef<HTMLDivElement>(null);

    const toggleDropdown = () => setIsDropdownOpen(!isDropdownOpen);

    // Select or deselect an option
    const handleItemSelect = (itemLabel: string) => {
        setSelectedItems((prevSelected) =>
            prevSelected.includes(itemLabel)
                ? prevSelected.filter((item) => item !== itemLabel)
                : [...prevSelected, itemLabel]
        );
    };

    // Handle "Select All" functionality
    const handleSelectAll = () => {
        if (selectedItems.length === options.length) {
            setSelectedItems([]);
        } else {
            setSelectedItems(options.map((option) => option.label));
        }
    };

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                dropdownContainerRef.current &&
                !dropdownContainerRef.current.contains(event.target as Node)
            ) {
                setIsDropdownOpen(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    return (
        <div
            ref={dropdownContainerRef}
            className={`relative h-full w-full rounded-lg ${className}`}
        >
            <div
                onClick={toggleDropdown}
                className={`flex cursor-pointer h-full font-inter items-center justify-between rounded-md border border-gray-300 bg-white px-4 py-2 shadow-sm transition ${
                    isDropdownOpen ? "border-[#1D7EB6] ring-1 ring-[#1D7EB6]" : ""
                }`}
            >
                <p
                    className={`text-base ${
                        selectedItems.length ? "text-black" : "text-gray-400"
                    }`}
                >
                    {selectedItems.length ? selectedItems.join(", ") : placeholder || "Select Option"}
                </p>

               
                {selectedItems.length > 0 && (
                    <span className="flex h-5 w-5 items-center justify-center rounded-full bg-[#933] text-xs text-white">
                        {selectedItems.length}
                    </span>
                )}

                {/* Dropdown Icon */}
                <svg
                    className={`h-4 w-4 transform text-gray-600 transition-transform ${
                        isDropdownOpen ? "rotate-180" : "rotate-0"
                    }`}
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
            </div>

            {isDropdownOpen && (
                <div className="absolute top-full mt-2 w-full rounded-md border bg-white shadow-lg z-30">
                    <ul className="max-h-48 overflow-y-auto p-2">
                        {/* Select All Option */}
                        <li className="flex items-center py-2">
                            <label className="flex items-center cursor-pointer space-x-2 !font-normal">
                                <input
                                    type="checkbox"
                                    checked={selectedItems.length === options.length}
                                    onChange={handleSelectAll}
                                    className="hidden peer"
                                />
                                <div className="w-5 h-5 border-2 border-gray-400 rounded-md flex items-center justify-center peer-checked:bg-[#1D7EB6] peer-checked:border-[#1D7EB6]">
                                    {selectedItems.length === options.length && (
                                        <svg
                                            className="w-3 h-3 text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 20 20"
                                            fill="currentColor"
                                        >
                                            <path
                                                fillRule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-3-3a1 1 0 011.414-1.414L9 11.586l6.293-6.293a1 1 0 011.414 0z"
                                                clipRule="evenodd"
                                            />
                                        </svg>
                                    )}
                                </div>
                                <span>Select All</span>
                            </label>
                        </li>

                        {/* Individual Options */}
                        {options.map((option, index) => (
                            <li key={index} className="flex items-center py-2">
                                <label className="flex items-center cursor-pointer space-x-2 !font-normal">
                                    <input
                                        type="checkbox"
                                        checked={selectedItems.includes(option.label)}
                                        onChange={() => handleItemSelect(option.label)}
                                        className="hidden peer"
                                    />
                                    <div className="w-5 h-5 border-2 border-gray-400 rounded-md flex items-center justify-center peer-checked:bg-[#1D7EB6] peer-checked:border-[#1D7EB6]">
                                        {selectedItems.includes(option.label) && (
                                            <svg
                                                className="w-3 h-3 text-white"
                                                xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 20 20"
                                                fill="currentColor"
                                            >
                                                <path
                                                    fillRule="evenodd"
                                                    d="M16.707 5.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-3-3a1 1 0 011.414-1.414L9 11.586l6.293-6.293a1 1 0 011.414 0z"
                                                    clipRule="evenodd"
                                                />
                                            </svg>
                                        )}
                                    </div>
                                    <span>{option.label}</span>
                                </label>
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
};

export default MultiSelectDropdown;
