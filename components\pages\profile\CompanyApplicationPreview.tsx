import { AgentProfile } from '@/store/utils.ts/types/AgentProfile';
import React, { useState } from 'react';
import DocumentDetail from '../dashboard/components/DocumentDetail';
import images from '@/public/assets/images/main/agent-image.png';

const CompanyApplicationPreviewComponent = ({ getProfileData }: any) => {
    const [profileData, setProfileData] = useState<AgentProfile>(getProfileData);

    const industryOptions = profileData?.industry_mission?.map((industry) => industry.name).join(', ');

    const typeOptions = profileData?.industry_subcategory?.map((type) => type.name).join(', ');

    const renderField = (label: string, value: any) => {
        if (value === null || value === '' || (Array.isArray(value) && value.length === 0)) return null;
        return (
            <div className="mb-2">
                <span className="pe-1 font-semibold">{label}:</span> {String(value)}
            </div>
        );
    };

    console.log("profileData", profileData)
    const capitalizeFirstLetter = (str: string) => {
        if (!str) return str;
   
        return str?.charAt(0)?.toUpperCase() + str?.slice(1);
    };
 
    const formatDate = (date: string) => {
        if (!date) return date;
        const dateObj = new Date(date);
        return dateObj.toLocaleDateString('en-US', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    };
    const designationOption = [
        { value: 'owner', label: 'Owner' },
        { value: 'manager', label: 'Manager' },
        { value: 'authorizedSignatory', label: 'Authorized Signatory' },
        { value: 'adminManager', label: 'Admin Manager' },
        { value: 'other', label: 'Other' },
    ];
    return (
        <div className="mx-auto max-w-6xl rounded-lg bg-white p-4 font-golosText shadow-lg">
            <h2 className="mb-8 text-center text-3xl font-bold">Agency Application Information Preview</h2>

            {/* Form 1 - Company Basic Info */}
            <section className="mb-10">
                <h5 className="mb-4 border-b pb-2 text-xl font-semibold text-[#1d7eb6]">Business Information</h5>
                {renderField('Company Name', profileData?.name)}
                {renderField('Company Email', profileData?.companyEmail)}
                {renderField('Company Phone', profileData?.companyPhone)}
                {renderField('Emirate of Operation', profileData.operationArea)}
                {renderField('Nationality', profileData.nationality)}

                {renderField('Designation / Role',  profileData?.positionOther ? 
                                                    profileData?.positionOther :
                                                    designationOption.find((item: any) => item.value == profileData?.position)?.label)}
                         
            </section>

            {/* Form 2 - Primary Industry */}
            <section className="mb-10">
                <h5 className="mb-4 border-b pb-2 text-xl font-semibold text-[#1d7eb6]">Industry & Specialization</h5>
                {renderField('Primary Industry', industryOptions)}
                {renderField('Specialization', typeOptions)}
          
                {profileData?.companyrole?.length > 0 && <div className="flex border-t border-[#e4e4e4] bg-white">
                               
                                <div className="flex w-2/3 items-start p-4 py-2 text-sm font-normal text-[#636363] md:text-base">
                                    <div className="w-full">
                                 
                                        <ul className="list-disc pl-4">
                                        {profileData?.companyrole?.map((item: any, index: number) => (
                                            <li key={index} className='py-2'>
                                            {item?.roleName}
                                            {item?.rolesList?.length > 0 && (
                                                <ul className="list-disc pl-8">
                                                {item?.rolesList?.map((subRole: any, subIndex: number) => (
                                                    <li key={subIndex}>{subRole}</li>
                                                ))}
                                                </ul>
                                            )}
                                            </li>
                                        ))}
                                        </ul>
                                   
                                    </div>
                                </div>
                                </div>}
            </section>
            {profileData?.agentlicenses?.length >0 &&
            <section className="mb-10">
                <h5 className="text-xl font-semibold mb-4 border-b pb-2 text-[#1d7eb6]">Licensing & Credentials Per Role</h5>

                {
                    profileData?.agentlicenses?.map((item: any, index: number) => (
                        <div key={index}>
                             <div  className="mb-6"> 
                            {renderField('License Number', item?.licenseNumber)}
                            {renderField('License Authority', item?.licenseAuthority )}
                            {renderField('License Authority Other', item?.licenseAuthorityOther)}
                            {renderField('License Expiry Date', formatDate(item?.licenseexpiryDate))}
                            <DocumentDetail type={'Licence Doc'} dataArray={item?.licenseFile} />
                            </div> 
                           
                             
                        </div>
                    ))
                }
                 
            </section>}

            {/* Form 3 - Sub Industry */}
           

   
       

            {/* Form 5 - Representative Info */}
            <section className="mb-10">
                <h5 className="mb-4 border-b pb-2 text-xl font-semibold text-[#1d7eb6]">Company Representative</h5>
                {renderField('First Name', profileData?.firstName)}
                {renderField('Middle Name', profileData?.middleName)}
                {renderField('Last Name', profileData?.lastName)}
                {renderField('Phone', profileData?.phone)}
                {/* {renderField('Email', profileData?.representativeEmail)} */}
                {renderField('Designation', profileData?.designation)}
                {profileData?.profileImage && <DocumentDetail type={'Profile Image'} dataArray={[profileData.profileImage]} />}

                {profileData?.personalIdDoc && profileData?.personalIdDoc.length > 0 ? (
                    <DocumentDetail type={'Emirates ID'} dataArray={profileData.personalIdDoc} />
                ) : (
                    renderField('Emirates ID', ' Document is missing')
                )}
 
                {profileData?.passportDoc && profileData?.passportDoc.length > 0 ? (
                    <DocumentDetail type={'Passport Doc'} dataArray={profileData.passportDoc} />
                ) : (
                    renderField('Passport Doc', ' Document is missing')
                )}

                {profileData?.visaDoc && profileData?.visaDoc.length > 0 ? <DocumentDetail type={'Visa'} dataArray={profileData.visaDoc} /> : renderField('Visa', ' Document is missing')}

             
                   {profileData?.personalIdDoc && profileData?.personalIdDoc.length > 0 ? (
                    <DocumentDetail type={'Emirates ID'} dataArray={profileData.personalIdDoc} />
                ) : (
                    renderField('Emirates ID', ' Document is missing')
                )}
            </section>

            {/* Form 6 - Invite Agents */}
            <section className="mb-10">
                <h5 className="mb-4 border-b pb-2 text-xl font-semibold text-[#1d7eb6]">Team Members / Agents (Optional)</h5>
                {renderField('Invite Agents', profileData?.inviteAgents ? 'Yes' : 'No')}
                {renderField('Invited Agents', profileData?.inviteAgents)}
            </section>
            <section className="mb-10">
                <h5 className="mb-4 border-b pb-2 text-xl font-semibold text-[#1d7eb6]">Verification & Agreements</h5>
                {profileData?.supportingDocsDoc && profileData?.supportingDocsDoc.length > 0 ? (
                    <DocumentDetail type={'Supportive Docs'} dataArray={profileData.supportingDocsDoc} />
                ) : (
                    renderField('Supportive Docs', ' Document is missing')
                )}
                {renderField('Referral ID', profileData?.referenceded)}
            </section>

            <section className="mb-10">
            <h5 className="mb-4 border-b pb-2 text-xl font-semibold text-[#1d7eb6]">Confirmation & Consent</h5>
            {renderField('Accuracy Confirmation', profileData?.accuracyConfirm ? 'Yes' : 'No')}
                {renderField('Communication Consent', profileData?.communicationConsent ? 'Yes' : 'No')}
                {renderField('Terms Agreement', profileData?.termsAgree ? 'Yes' : 'No')}
            </section>
        </div>
    );
};

export default CompanyApplicationPreviewComponent;
