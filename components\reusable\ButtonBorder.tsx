"use client";

import React from "react";

export const ButtonBorder = ({
  className,
  value,
  onClick,
  icon,
  textColor,
  disabled,
  invertColors = false,
}: any) => {
  return (
    <button
      className={`flex items-center font-inter justify-center gap-2 rounded-lg border px-8 py-2 text-sm w-full transition-colors duration-300 ease-in-out
        ${
          invertColors
            ? "bg-blueMain text-white border-blueMain hover:bg-white hover:text-blueMain"
            : "bg-white text-blueMain border-blueMain hover:bg-blueMain hover:text-white"
        } 
        ${className}`}
      disabled={disabled}
      onClick={onClick}
    >
      {icon ? icon : null}
      {value}
    </button>
  );
};