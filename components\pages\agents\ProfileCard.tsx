'use client';
import { VerifiedSmallIcon } from '@/components/icon/Icon';
import HorizantalLine from '@/components/reusable/HorizantalLine';
import Image from 'next/image';
import Link from 'next/link';
import images from '@/public/assets/images/main/agent-image.png';

export default function ReviewProfileDetails({ profleData }: any) {
    const timeAgo = (dateString: string): string => {
        const now = new Date();
        const past = new Date(dateString);
        const diff = now.getTime() - past.getTime(); // difference in milliseconds

        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        const weeks = Math.floor(days / 7);
        const months = Math.floor(days / 30); // approx
        const years = Math.floor(days / 365); // approx

        if (seconds < 60) return 'just now';
        if (minutes < 60) return `${minutes} min${minutes !== 1 ? 's' : ''} ago`;
        if (hours < 24) return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
        if (days < 7) return `${days} day${days !== 1 ? 's' : ''} ago`;
        if (weeks < 5) return `${weeks} week${weeks !== 1 ? 's' : ''} ago`;
        if (months < 12) return `${months} month${months !== 1 ? 's' : ''} ago`;
        return `${years} year${years !== 1 ? 's' : ''} ago`;
    };

    return (
        <>
            <div className=" max-w-96  shadow-[0px_4px_20px_0px_rgba(21,32,70,0.07)] ">
                <div className="flex flex-col gap-2 rounded-lg border border-[#E4E4E4] bg-white p-[18px_16px]">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center">
                            <Image src={profleData.profilePhotos[0] || images} alt="agency" className="h-20 w-20 rounded-lg" width={800} height={800} />
                            <div className="ml-4">
                                <div className="flex items-center gap-1">
                                    <h2 className="py-1 text-lg font-semibold"> {profleData?.firstName + ' ' + profleData?.lastName} </h2>
                                    <div className="h-2 w-2 rounded-full bg-[#2ab140]" />
                                </div>
                                <div className="inline-flex items-center gap-1">
                                    <span className="text-base font-semibold text-[#636363]">Account Created:</span>
                                    <span className="text-base font-normal text-[#636363]">{timeAgo(profleData?.created_at)}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="flex flex-col gap-4">
                        <InfoItem label="Username" value={profleData?.username} />
                        <InfoItem label="Contact" value={profleData?.phone} isVerified />
                        <InfoItem label="Email" value={profleData?.email} isVerified link />
                    </div>

                    <HorizantalLine />
                    <div className="flex  gap-2">
                        <Link href="#" className="w-full rounded bg-transparent py-3 text-center text-redMain hover:bg-redMain hover:text-white">
                            View Profile
                        </Link>
                    </div>
                </div>
            </div>
        </>
    );
}
const InfoItem = ({ label, value, isVerified, link }: any) => (
    <div className="inline-flex items-center gap-1">
        <span className="text-base font-semibold text-[#636363]">{label}:</span>

        {link ? (
            <Link href={`mailto:${value}`}>
                <span className="text-[#1d7eb6]">{value}</span>
            </Link>
        ) : (
            <span className="text-base font-normal text-[#1d7eb6]">{value}</span>
        )}
        {isVerified && <VerifiedSmallIcon />}
    </div>
);
