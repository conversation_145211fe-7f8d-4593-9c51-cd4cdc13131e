'use client'; // Mark as client component to use hooks

import React, { useState, useEffect, useRef } from 'react';

interface Option {
    label: string;
    initail?: string;
    dropdownOptions?: Option[];
    setSelectedStatus?: string;
}

const SearchDropDown = ({ classes, initail, dropdownOptions, status, setSelectedStatus }: any) => {
    /* ---------------- main list ---------------- */
    const mainOptions: Option[] = dropdownOptions
        ? dropdownOptions
        : [{ label: 'Active' }, { label: 'Inactive' }, { label: 'Pending' }, { label: 'Completed' }];

    /* ---------------- state -------------------- */
    const [isOpen, setIsOpen] = useState(false);
    const [selected, setSelected] = useState(initail);
    const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>('bottom'); // new
    const dropdownRef = useRef<HTMLDivElement>(null);

    /* ---------------- handlers ----------------- */
    const toggleDropdown = () => {
        if (!isOpen && dropdownRef.current) {
            const rect = dropdownRef.current.getBoundingClientRect();
            const spaceBelow = window.innerHeight - rect.bottom;
            const spaceAbove = rect.top;
            const estimatedMenuHeight = 5 * 40 + 16; // ≈ five items + padding

            setDropdownPosition(
                spaceBelow < estimatedMenuHeight && spaceAbove > estimatedMenuHeight ? 'top' : 'bottom'
            );
        }
        setIsOpen(!isOpen);
    };

    const selectOption = (option: string) => {
        setSelected(option);
        setSelectedStatus(option);
        setIsOpen(false); // Close the dropdown after selection
    };

    /* ---- close dropdown on outside click ----- */
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    /* ---- sync with parent --------------------- */
    useEffect(() => {
        setSelected(initail);
    }, [initail]);

    /* ---------------- render ------------------- */
    return (
        <div ref={dropdownRef} className={`relative h-full w-full rounded-lg ${classes}`}>
            {/* trigger */}
            <div
                onClick={toggleDropdown}
                className={`text-grayText flex h-full max-h-14 cursor-pointer items-center justify-between gap-2 rounded-md border border-[#E4E4E4] bg-white p-2 py-4 font-inter ${
                    isOpen ? '!border-lightBlue  focus:outline-none' : ''
                }`}
            >
                {status && selected && (
                    <div className="absolute left-3 top-3.5 -mt-2 -ml-2 rounded-t-lg px-2 py-1 text-xs font-normal">
                        Selected Option
                    </div>
                )}

                <p
                    className={`ps-3 ${
                        selected ? 'text-base font-normal text-black' : 'text-base font-normal text-neutral-400'
                    } ${status && selected ? 'pt-5' : ''}`}
                >
                    {initail && !selected ? initail : selected ? selected : 'Select Option'}
                </p>

                <svg
                    className={`h-4 w-4 transform text-gray-600 transition-transform ${isOpen ? 'rotate-180' : 'rotate-0'}`}
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
            </div>

            {/* dropdown menu */}
            {isOpen && (
                <div
                    className={`border-borderColor shadow-shadowcustom absolute z-30 mt-2 w-full rounded border bg-white py-2 ${
                        dropdownPosition === 'bottom' ? 'top-[50px]' : 'bottom-[50px]'
                    }`}
                >
                    <ul className="scrollbar-main-22 max-h-40 overflow-y-scroll py-2">
                        {mainOptions.map((option: Option, index: number) => (
                            <li
                                key={index}
                                className="cursor-pointer px-4 py-2 text-lg hover:bg-gray-200 hover:text-[#1D7EB6]"
                                onClick={() => selectOption(option.label)}
                            >
                                {option.label}
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
};

export default SearchDropDown;
