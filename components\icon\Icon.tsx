import React from 'react';

export const CloseIcon = () => {
    return (
        <div>
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                <path d="M3 12C4.5 9 7.5 6.5 12 6.5C16.5 6.5 19.5 9 21 12C19.5 15 16.5 17.5 12 17.5C7.5 17.5 4.5 15 3 12Z" stroke="#1D7EB6" stroke-width="1.5" />
                <path d="M2 2L22 22" stroke="#1D7EB6" stroke-width="1.5" />
            </svg>
        </div>
    );
};

export const LogoIcon = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="183" height="120" viewBox="0 0 183 120" fill="none">
            <g clipPath="url(#clip0_124_7152)">
                <path
                    d="M50.0984 103.014L46.7673 93.7049H29.1612L25.8301 103.014H14.8863L33.6481 56.8125H42.2804L61.0422 103.014H50.0984ZM37.9297 68.906L32.0835 85.3478H43.776L37.9297 68.906Z"
                    fill="#1D7EB6"
                />
                <path
                    d="M84.0874 73.0504H94.2843V102.946C94.2843 112.798 86.4663 120 76.9491 120C68.7912 120 59.8864 115.924 59.4103 106.819H69.6072C70.3559 110.352 73.1418 112.458 76.4057 112.458C80.8926 112.458 84.0874 108.382 84.0874 104.101V99.3443C82.0484 102.47 77.6305 103.693 73.8232 103.693C65.3256 103.693 58.9359 96.2873 58.9359 87.9974C58.9359 79.7075 65.3256 72.3711 73.8232 72.3711C77.6305 72.3711 81.1651 73.3228 84.0874 76.7195V73.0504ZM84.0874 87.9991C84.0874 84.0576 81.0288 80.5248 76.9491 80.5248C72.8693 80.5248 69.8125 84.0576 69.8125 87.9991C69.8125 91.9406 72.871 95.5407 76.9491 95.5407C81.0271 95.5407 84.0874 92.0079 84.0874 87.9991Z"
                    fill="#1D7EB6"
                />
                <path
                    d="M129.972 89.289H106.724C106.724 92.7547 109.172 96.4237 113.182 96.4237C116.786 96.4237 119.029 94.0461 119.096 91.8719H129.496C127.728 99.7531 121.338 103.695 113.386 103.695C103.053 103.695 95.9161 97.5807 95.9161 88.0683C95.9161 78.556 103.325 72.373 113.386 72.373C122.494 72.373 130.04 77.8077 130.04 87.389C130.04 88.0011 129.972 88.6804 129.972 89.2908V89.289ZM120.252 84.4648C120.252 84.4648 119.776 78.8939 113.589 78.8939C107.403 78.8939 106.86 84.4648 106.86 84.4648H120.252Z"
                    fill="#1D7EB6"
                />
                <path
                    d="M162.331 103.013H152.134V86.0267C152.134 82.4938 150.775 80.592 147.58 80.592C144.385 80.592 141.87 83.9887 141.87 88.0664V103.013H131.673V73.0504H141.87V76.7195C143.909 74.1384 147.104 72.3711 151.048 72.3711C159.069 72.3711 162.331 77.8747 162.331 86.0284V103.015V103.013Z"
                    fill="#1D7EB6"
                />
                <path d="M177.491 103.014H167.361V80.9321H161.924V73.0508H167.361V61.6367H177.491V73.0508H182.928V80.9321H177.491V103.014Z" fill="#1D7EB6" />
                <path
                    d="M47.2296 12.8323C47.3987 13.0047 47.5643 13.1754 47.7213 13.3375C43.9779 17.0549 37.6106 21.1774 29.9306 24.5551C22.2524 27.9328 14.9105 29.8397 9.63867 30.088C9.62487 29.8621 9.61107 29.6259 9.59727 29.3845C3.29904 33.8381 -0.318423 37.6451 0.104219 38.6072C0.842548 40.2866 16.8132 39.9348 33.4757 32.607C50.1398 25.2775 61.1854 13.7461 60.4471 12.0685C60.0227 11.1064 54.7716 11.2012 47.2296 12.8341"
                    fill="#1D7EB6"
                />
                <path
                    d="M44.5609 10.1472C42.5529 8.15577 40.3017 5.97295 38.4438 4.18497C37.5951 3.36598 36.8274 2.63147 36.2047 2.03491C35.7527 3.5815 33.9949 4.86602 33.9949 4.86602C35.268 3.49012 35.8269 2.24008 35.4267 1.33144C34.4175 -0.961732 27.6846 -0.218608 20.3893 2.99011C13.8099 5.88329 9.02109 9.79547 8.88308 12.218C8.86928 12.4818 9.10906 15.4284 9.15047 17.0681C9.16599 17.7181 9.18324 18.4061 9.20394 19.1147L9.28157 19.0871L12.9473 17.725L9.46615 19.5061L9.21774 19.6337C9.27467 21.6596 9.3454 23.8304 9.42475 25.8339C14.4775 25.2666 20.9689 23.4338 27.7225 20.463C34.7832 17.3578 40.7364 13.6214 44.5575 10.1489"
                    fill="#1D7EB6"
                />
                <path
                    d="M15.8317 90.6718C15.1917 92.3133 13.3424 93.1253 11.7018 92.4874C10.0596 91.8494 9.24535 90.0028 9.88362 88.3597L9.93537 88.2269C9.81462 89.6563 10.6375 91.0563 12.0451 91.6029C13.4528 92.1495 15.0036 91.6736 15.8817 90.5391L15.8317 90.6718ZM52.1892 22.5371C51.1921 23.3268 50.1191 24.1354 48.9754 24.9492C51.9132 29.4994 52.7015 35.3444 50.5883 40.7773C47.0881 49.7758 36.9499 54.2363 27.9451 50.7362C22.5111 48.6241 18.7315 44.0946 17.3583 38.8548C15.9852 39.1479 14.6638 39.3928 13.4079 39.5945C14.9191 45.6861 19.0696 51.0276 25.0866 53.8656L22.3352 60.9382C21.1311 60.471 19.7769 61.0675 19.3077 62.2693L9.16427 88.3476C8.39489 90.3235 9.3178 92.5839 11.2827 93.384C13.2889 94.203 15.5677 93.2184 16.3492 91.2063L26.5184 65.0711C26.9877 63.8676 26.3891 62.5141 25.1867 62.0469L27.8416 55.2243C27.1395 54.9139 26.4615 54.5656 25.8094 54.1897C26.0354 54.288 26.2631 54.3811 26.4943 54.4708C37.5623 58.7726 50.0242 53.2932 54.3283 42.2291C56.9624 35.4564 55.9291 28.1631 52.1926 22.5371"
                    fill="#993333"
                />
                <path
                    d="M36.6101 48.1409C31.5142 49.0081 26.3114 46.9495 23.1925 42.8149C22.8854 42.4079 22.3403 42.2597 21.8728 42.4648C21.2242 42.7493 21.0172 43.5649 21.4433 44.1304C25.0538 48.9168 31.0778 51.3013 36.9775 50.2944C37.6813 50.1737 38.0884 49.4288 37.8004 48.7754C37.5968 48.3133 37.1052 48.0564 36.6083 48.1409"
                    fill="#993333"
                />
                <path d="M58.5529 46.5882V53.6833H70.323V55.7368H58.5529V65.1974H55.9688V44.502H71.258V46.5865H58.5529V46.5882Z" fill="#993333" />
                <path d="M72.3172 44.502H75.5241V47.0848H72.3172V44.502ZM72.6588 49.6383H75.1498V65.1991H72.6588V49.6383Z" fill="#993333" />
                <path
                    d="M92.4315 55.2701V65.198H89.9405V55.5494C89.9405 52.22 88.4759 51.2234 86.204 51.2234C82.747 51.2234 80.9098 53.8994 80.9098 59.253V65.198H78.4188V49.6372H80.9098V53.7149C81.8741 50.8821 83.8061 49.1406 86.8888 49.1406C90.7806 49.1406 92.4315 51.2562 92.4315 55.2718V55.2701Z"
                    fill="#993333"
                />
                <path
                    d="M109.31 43.4121V65.1972H106.819V62.2092C105.759 64.4196 103.798 65.6955 100.934 65.6955C96.0455 65.6955 93.8029 62.1782 93.8029 57.4177C93.8029 52.6572 96.0455 49.1399 100.934 49.1399C103.8 49.1399 105.761 50.3847 106.819 52.5951V43.4138H109.31V43.4121ZM106.819 57.4177C106.819 53.0296 104.733 51.1623 101.431 51.1623C97.881 51.1623 96.325 53.2158 96.325 57.4177C96.325 61.6195 97.881 63.673 101.431 63.673C104.731 63.673 106.819 61.8057 106.819 57.4177Z"
                    fill="#993333"
                />
                <path
                    d="M130.704 59.9078H120.428L118.404 65.1993H115.82L123.667 44.5039H127.497L135.344 65.1993H132.729L130.706 59.9078H130.704ZM129.926 57.8233L125.567 46.3695L121.207 57.8233H129.926Z"
                    fill="#993333"
                />
                <path
                    d="M150.135 55.2701V65.198H147.644V55.5494C147.644 52.22 146.181 51.2234 143.907 51.2234C140.45 51.2234 138.613 53.8994 138.613 59.253V65.198H136.122V49.6372H138.613V53.7149C139.579 50.8821 141.51 49.1406 144.592 49.1406C148.484 49.1406 150.135 51.2562 150.135 55.2718V55.2701Z"
                    fill="#993333"
                />
                <path
                    d="M166.078 49.6387L158.76 67.7823C157.794 70.272 157.172 71.4238 154.37 71.4238H151.412V69.4013H155.74L157.453 65.1995H156.582L150.416 49.6387H153.031L157.796 61.62L158.326 63.1132L158.793 61.62L163.62 49.6387H166.08H166.078Z"
                    fill="#993333"
                />
            </g>
            <defs>
                <clipPath id="clip0_124_7152">
                    <rect width="182.857" height="120" fill="white" transform="translate(0.0714111)" />
                </clipPath>
            </defs>
        </svg>
    );
};
export const NavLogoIcon = () => {
    return (
        <div data-svg-wrapper className="relative">
            <svg width="109" height="70" viewBox="0 0 109 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M28.1867 59.9687L26.3095 54.7215H16.3898L14.5136 59.9687H8.34717L18.9181 33.9241H23.7822L34.3531 59.9687H28.1867ZM21.3303 40.7417L18.0366 50.0103H24.625L21.3313 40.7417H21.3303Z"
                    fill="#1D7EB6"
                />
                <path
                    d="M47.3374 43.0784H53.082V59.931C53.082 65.485 48.6775 69.544 43.3151 69.544C38.719 69.544 33.7011 67.246 33.4341 62.1139H39.1786C39.5995 64.1052 41.1699 65.2924 43.0083 65.2924C45.5357 65.2924 47.3364 62.9944 47.3364 60.5812V57.9C46.1879 59.662 43.6973 60.3509 41.5531 60.3509C36.7655 60.3509 33.165 56.1757 33.165 51.5032C33.165 46.8307 36.7655 42.6943 41.5531 42.6943C43.6983 42.6943 45.6895 43.2303 47.3364 45.1452V43.0775L47.3374 43.0784ZM47.3374 51.5042C47.3374 49.2826 45.6141 47.2913 43.3161 47.2913C41.018 47.2913 39.2948 49.2826 39.2948 51.5042C39.2948 53.7258 41.018 55.7558 43.3161 55.7558C45.6141 55.7558 47.3374 53.7645 47.3374 51.5042Z"
                    fill="#1D7EB6"
                />
                <path
                    d="M73.19 52.2319H60.0917C60.0917 54.1854 61.4705 56.2532 63.7308 56.2532C65.7609 56.2532 67.0245 54.9131 67.0622 53.6871H72.923C71.9273 58.1303 68.3269 60.3519 63.846 60.3519C58.024 60.3519 54.0027 56.9044 54.0027 51.5429C54.0027 46.1815 58.1779 42.6953 63.846 42.6953C68.9781 42.6953 73.2297 45.7597 73.2297 51.1598C73.2297 51.5042 73.191 51.8874 73.191 52.2319H73.19ZM67.7124 49.5129C67.7124 49.5129 67.4444 46.3721 63.9592 46.3721C60.4739 46.3721 60.1672 49.5129 60.1672 49.5129H67.7124Z"
                    fill="#1D7EB6"
                />
                <path
                    d="M91.4216 59.9687H85.6771V50.3934C85.6771 48.4021 84.9117 47.3291 83.111 47.3291C81.3103 47.3291 79.8938 49.2439 79.8938 51.5419V59.9677H74.1482V43.0775H79.8938V45.1452C81.0423 43.6899 82.843 42.6943 85.0636 42.6943C89.5832 42.6943 91.4216 45.7964 91.4216 50.3924V59.9677V59.9687Z"
                    fill="#1D7EB6"
                />
                <path d="M99.9624 59.9687H94.2555V47.5206H91.1912V43.0775H94.2555V36.643H99.9624V43.0775H103.027V47.5206H99.9624V59.9687Z" fill="#1D7EB6" />
                <path
                    d="M26.5707 7.68976C26.666 7.78705 26.7593 7.88334 26.8477 7.97466C24.7383 10.0702 21.1508 12.394 16.8237 14.298C12.4977 16.2019 8.36021 17.277 5.39114 17.4169C5.3832 17.2899 5.37526 17.1559 5.36732 17.0209C1.81852 19.5313 -0.219428 21.6775 0.018813 22.2205C0.434741 23.1665 9.43329 22.9689 18.821 18.8374C28.2096 14.706 34.4337 8.20496 34.0177 7.25895C33.7795 6.71695 30.8203 6.76956 26.5707 7.68976Z"
                    fill="#1D7EB6"
                />
                <path
                    d="M25.067 6.17594C23.9353 5.05423 22.6667 3.82232 21.6204 2.81476C21.142 2.35317 20.7102 1.93923 20.3587 1.60271C20.1036 2.47428 19.1129 3.19893 19.1129 3.19893C19.8306 2.42266 20.1453 1.71886 19.92 1.20664C19.3512 -0.0858163 15.5572 0.332098 11.4476 2.14173C7.74093 3.77269 5.04186 5.9784 4.96542 7.34332C4.95748 7.49222 5.09249 9.15296 5.11631 10.0781C5.12524 10.4444 5.13517 10.8326 5.14609 11.2316L5.18977 11.2157L7.25551 10.4474L5.294 11.451L5.15403 11.5225C5.18679 12.664 5.2265 13.888 5.27117 15.0177C8.11814 14.698 11.7761 13.6646 15.581 11.99C19.5596 10.2389 22.9129 8.1325 25.067 6.17594Z"
                    fill="#1D7EB6"
                />
                <path
                    d="M8.8805 51.5697C8.52016 52.4949 7.47885 52.9535 6.55368 52.5932C5.62851 52.2338 5.1699 51.1925 5.52924 50.2664L5.55803 50.1919C5.49053 50.998 5.95411 51.7862 6.74725 52.0949C7.54039 52.4036 8.41394 52.1346 8.90928 51.4953L8.8805 51.5697ZM29.3642 13.1614C28.8024 13.6071 28.1978 14.0617 27.5536 14.5213C29.2084 17.0864 29.6531 20.3811 28.4619 23.4444C26.4895 28.517 20.7776 31.0314 15.7041 29.058C12.6417 27.8668 10.5134 25.3136 9.73915 22.3604C8.96587 22.5252 8.22136 22.6642 7.51359 22.7774C8.36431 26.211 10.704 29.2228 14.093 30.8229L12.5434 34.8095C11.8655 34.5465 11.1021 34.882 10.838 35.56L5.12225 50.2604C4.68945 51.3742 5.2096 52.6488 6.31544 53.0995C7.4451 53.561 8.72961 53.0061 9.17035 51.8725L14.9 37.1393C15.1641 36.4613 14.8276 35.698 14.1496 35.4339L15.6455 31.5883C15.2495 31.4126 14.8683 31.217 14.501 31.0046C14.629 31.0592 14.7571 31.1128 14.8861 31.1634C21.1221 33.5885 28.1442 30.4993 30.5683 24.2624C32.0524 20.4446 31.4697 16.3329 29.3652 13.1614"
                    fill="white"
                />
                <path
                    d="M20.5858 27.5948C17.715 28.0832 14.7827 26.9227 13.0266 24.5929C12.8539 24.3636 12.5462 24.2803 12.2831 24.3964C11.9178 24.5572 11.8007 25.0158 12.0409 25.3345C14.0749 28.0335 17.4698 29.3766 20.7933 28.8098C21.1903 28.7423 21.4186 28.3214 21.2568 27.9531C21.1427 27.693 20.8657 27.5471 20.5858 27.5948Z"
                    fill="white"
                />
                <path d="M38.547 25.4228L38.273 26.9446H33.2293L32.5969 30.5519H36.5289L36.2549 32.0568H32.323L31.3998 37.3051H29.4512L31.5536 25.4228H38.546H38.547Z" fill="white" />
                <path
                    d="M41.1459 27.8846L39.4872 37.3051H37.5386L39.1973 27.8846H41.1469H41.1459ZM39.6579 26.346C39.4644 26.1524 39.3671 25.9072 39.3671 25.6104C39.3671 25.2114 39.518 24.8669 39.8207 24.5761C40.1225 24.2852 40.467 24.1403 40.8551 24.1403C41.1628 24.1403 41.411 24.2376 41.5986 24.4311C41.7862 24.6247 41.8805 24.8699 41.8805 25.1667C41.8805 25.5658 41.7326 25.9102 41.4358 26.2011C41.139 26.4919 40.7975 26.6368 40.4104 26.6368C40.1026 26.6368 39.8515 26.5406 39.6579 26.346Z"
                    fill="white"
                />
                <path
                    d="M50.1462 28.5259C50.7328 29.056 51.0267 29.8164 51.0267 30.8081C51.0267 31.0592 50.9979 31.3719 50.9413 31.7481L49.9496 37.3041H48.0179L48.9579 32.038C49.0036 31.7193 49.0264 31.491 49.0264 31.354C49.0264 30.7276 48.8527 30.2482 48.5053 29.9176C48.1579 29.5871 47.6764 29.4213 47.061 29.4213C46.3204 29.4213 45.696 29.6466 45.1888 30.0963C44.6815 30.547 44.3599 31.1932 44.2229 32.037V31.9854L43.2829 37.3021H41.3333L42.992 27.8817H44.9406L44.753 28.9756C45.1401 28.5884 45.5988 28.2837 46.1288 28.0613C46.6589 27.839 47.2089 27.7278 47.7787 27.7278C48.7703 27.7278 49.5595 27.9928 50.1462 28.5229V28.5259Z"
                    fill="white"
                />
                <path
                    d="M52.5912 30.0387C53.1094 29.3091 53.7457 28.7423 54.4972 28.3373C55.2496 27.9333 56.0417 27.7308 56.8736 27.7308C57.4891 27.7308 58.0708 27.8648 58.6177 28.1328C59.1647 28.4008 59.5806 28.7572 59.8655 29.2009L60.6686 24.6535H62.6351L60.3956 37.3051H58.4291L58.6852 35.8856C58.2753 36.3531 57.765 36.7323 57.1545 37.0222C56.545 37.313 55.875 37.4579 55.1454 37.4579C54.4158 37.4579 53.7894 37.2981 53.2305 36.9795C52.6716 36.6608 52.2359 36.2072 51.9222 35.6205C51.6085 35.0338 51.4526 34.3578 51.4526 33.5945C51.4526 33.253 51.4864 32.9046 51.5549 32.5512C51.7256 31.6052 52.0701 30.7673 52.5892 30.0377L52.5912 30.0387ZM59.3364 31.8851C59.3364 31.1217 59.1111 30.5202 58.6614 30.0814C58.2107 29.6427 57.6499 29.4233 56.9768 29.4233C56.4755 29.4233 55.9852 29.5464 55.5067 29.7906C55.0282 30.0357 54.6123 30.3951 54.2589 30.8676C53.9055 31.3411 53.6722 31.902 53.5581 32.5512C53.5124 32.7676 53.4896 33.0018 53.4896 33.252C53.4896 34.0273 53.7149 34.6398 54.1646 35.0894C54.6143 35.5401 55.1761 35.7644 55.8482 35.7644C56.3495 35.7644 56.8399 35.6394 57.3193 35.3882C57.7978 35.1381 58.2137 34.7698 58.5671 34.2854C58.9195 33.8009 59.1538 33.2341 59.2679 32.5839C59.3126 32.3675 59.3364 32.1333 59.3364 31.8831V31.8851Z"
                    fill="white"
                />
                <path
                    d="M73.5606 34.877H68.5853L67.3028 37.3051H65.2688L71.6288 25.4059H73.8852L76.0393 37.3051H73.9874L73.5596 34.877H73.5606ZM73.3035 33.3562L72.3287 27.7655L69.3884 33.3562H73.3035Z"
                    fill="white"
                />
                <path
                    d="M85.6396 28.5259C86.2262 29.056 86.5201 29.8164 86.5201 30.8081C86.5201 31.0592 86.4913 31.3719 86.4347 31.7481L85.443 37.3041H83.5113L84.4513 32.038C84.496 31.7193 84.5198 31.491 84.5198 31.354C84.5198 30.7276 84.3461 30.2482 83.9987 29.9176C83.6512 29.5871 83.1698 29.4213 82.5543 29.4213C81.8138 29.4213 81.1894 29.6466 80.6822 30.0963C80.1749 30.547 79.8533 31.1932 79.7163 32.037V31.9854L78.7762 37.3021H76.8276L78.4864 27.8817H80.435L80.2474 28.9756C80.6345 28.5884 81.0941 28.2837 81.6242 28.0613C82.1543 27.839 82.7042 27.7278 83.274 27.7278C84.2657 27.7278 85.0549 27.9928 85.6415 28.5229L85.6396 28.5259Z"
                    fill="white"
                />
                <path d="M89.5792 27.8846L90.964 35.0646L94.9823 27.8846H96.9994L88.793 41.7324H86.7759L89.4938 37.1502L87.4082 27.8836H89.5792V27.8846Z" fill="white" />
                <path
                    d="M109 34.9801C109 36.6548 107.655 37.9869 106.007 37.9869C104.36 37.9869 103.027 36.6548 103.027 34.9801C103.027 33.3055 104.347 32.0131 106.007 32.0131C107.668 32.0131 109 33.3452 109 34.9801ZM108.341 34.9801C108.341 33.6877 107.313 32.6464 106.02 32.6464C104.728 32.6464 103.686 33.7016 103.686 34.9801C103.686 36.2587 104.702 37.3407 106.02 37.3407C107.339 37.3407 108.341 36.2726 108.341 34.9801ZM106.746 35.0467C107.115 35.1122 107.234 35.3365 107.234 35.7058V35.9827C107.234 36.1942 107.26 36.3917 107.3 36.5367H106.733C106.693 36.4046 106.654 36.2597 106.654 35.9956V35.7316C106.654 35.402 106.522 35.2442 106.165 35.2442H105.466V36.5367H104.873V33.4643H106.337C106.956 33.4643 107.339 33.7681 107.339 34.3081C107.339 34.6774 107.128 34.9672 106.746 35.0467ZM106.692 34.3607C106.692 34.0967 106.56 33.9517 106.257 33.9517H105.466V34.7826H106.218C106.547 34.7826 106.692 34.6377 106.692 34.3607Z"
                    fill="#1D7EB6"
                />
            </svg>
        </div>
    );
};

export const CalendarIcon = () => {
    return (
        <>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="3" y="3.5" width="18" height="18" rx="5" stroke="#2D2D2E" stroke-width="1.5" />
                <path d="M3 8.49997H21" stroke="#2D2D2E" stroke-width="1.5" stroke-linejoin="round" />
                <path d="M16.5 1.99997L16.5 4.99997" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M7.5 1.99997L7.5 4.99997" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M6.5 12.5H7.5" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M11.5 12.5H12.5" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M16.5 12.5H17.5" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M6.5 16.5H7.5" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M11.5 16.5H12.5" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M16.5 16.5H17.5" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
        </>
    );
};

export const NavLogoIconPhone = () => {
    return (
        <div data-svg-wrapper className="relative">
            <svg width="54" height="35" viewBox="0 0 109 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M28.1867 59.9687L26.3095 54.7215H16.3898L14.5136 59.9687H8.34717L18.9181 33.9241H23.7822L34.3531 59.9687H28.1867ZM21.3303 40.7417L18.0366 50.0103H24.625L21.3313 40.7417H21.3303Z"
                    fill="#1D7EB6"
                />
                <path
                    d="M47.3374 43.0784H53.082V59.931C53.082 65.485 48.6775 69.544 43.3151 69.544C38.719 69.544 33.7011 67.246 33.4341 62.1139H39.1786C39.5995 64.1052 41.1699 65.2924 43.0083 65.2924C45.5357 65.2924 47.3364 62.9944 47.3364 60.5812V57.9C46.1879 59.662 43.6973 60.3509 41.5531 60.3509C36.7655 60.3509 33.165 56.1757 33.165 51.5032C33.165 46.8307 36.7655 42.6943 41.5531 42.6943C43.6983 42.6943 45.6895 43.2303 47.3364 45.1452V43.0775L47.3374 43.0784ZM47.3374 51.5042C47.3374 49.2826 45.6141 47.2913 43.3161 47.2913C41.018 47.2913 39.2948 49.2826 39.2948 51.5042C39.2948 53.7258 41.018 55.7558 43.3161 55.7558C45.6141 55.7558 47.3374 53.7645 47.3374 51.5042Z"
                    fill="#1D7EB6"
                />
                <path
                    d="M73.19 52.2319H60.0917C60.0917 54.1854 61.4705 56.2532 63.7308 56.2532C65.7609 56.2532 67.0245 54.9131 67.0622 53.6871H72.923C71.9273 58.1303 68.3269 60.3519 63.846 60.3519C58.024 60.3519 54.0027 56.9044 54.0027 51.5429C54.0027 46.1815 58.1779 42.6953 63.846 42.6953C68.9781 42.6953 73.2297 45.7597 73.2297 51.1598C73.2297 51.5042 73.191 51.8874 73.191 52.2319H73.19ZM67.7124 49.5129C67.7124 49.5129 67.4444 46.3721 63.9592 46.3721C60.4739 46.3721 60.1672 49.5129 60.1672 49.5129H67.7124Z"
                    fill="#1D7EB6"
                />
                <path
                    d="M91.4216 59.9687H85.6771V50.3934C85.6771 48.4021 84.9117 47.3291 83.111 47.3291C81.3103 47.3291 79.8938 49.2439 79.8938 51.5419V59.9677H74.1482V43.0775H79.8938V45.1452C81.0423 43.6899 82.843 42.6943 85.0636 42.6943C89.5832 42.6943 91.4216 45.7964 91.4216 50.3924V59.9677V59.9687Z"
                    fill="#1D7EB6"
                />
                <path d="M99.9624 59.9687H94.2555V47.5206H91.1912V43.0775H94.2555V36.643H99.9624V43.0775H103.027V47.5206H99.9624V59.9687Z" fill="#1D7EB6" />
                <path
                    d="M26.5707 7.68976C26.666 7.78705 26.7593 7.88334 26.8477 7.97466C24.7383 10.0702 21.1508 12.394 16.8237 14.298C12.4977 16.2019 8.36021 17.277 5.39114 17.4169C5.3832 17.2899 5.37526 17.1559 5.36732 17.0209C1.81852 19.5313 -0.219428 21.6775 0.018813 22.2205C0.434741 23.1665 9.43329 22.9689 18.821 18.8374C28.2096 14.706 34.4337 8.20496 34.0177 7.25895C33.7795 6.71695 30.8203 6.76956 26.5707 7.68976Z"
                    fill="#1D7EB6"
                />
                <path
                    d="M25.067 6.17594C23.9353 5.05423 22.6667 3.82232 21.6204 2.81476C21.142 2.35317 20.7102 1.93923 20.3587 1.60271C20.1036 2.47428 19.1129 3.19893 19.1129 3.19893C19.8306 2.42266 20.1453 1.71886 19.92 1.20664C19.3512 -0.0858163 15.5572 0.332098 11.4476 2.14173C7.74093 3.77269 5.04186 5.9784 4.96542 7.34332C4.95748 7.49222 5.09249 9.15296 5.11631 10.0781C5.12524 10.4444 5.13517 10.8326 5.14609 11.2316L5.18977 11.2157L7.25551 10.4474L5.294 11.451L5.15403 11.5225C5.18679 12.664 5.2265 13.888 5.27117 15.0177C8.11814 14.698 11.7761 13.6646 15.581 11.99C19.5596 10.2389 22.9129 8.1325 25.067 6.17594Z"
                    fill="#1D7EB6"
                />
                <path
                    d="M8.8805 51.5697C8.52016 52.4949 7.47885 52.9535 6.55368 52.5932C5.62851 52.2338 5.1699 51.1925 5.52924 50.2664L5.55803 50.1919C5.49053 50.998 5.95411 51.7862 6.74725 52.0949C7.54039 52.4036 8.41394 52.1346 8.90928 51.4953L8.8805 51.5697ZM29.3642 13.1614C28.8024 13.6071 28.1978 14.0617 27.5536 14.5213C29.2084 17.0864 29.6531 20.3811 28.4619 23.4444C26.4895 28.517 20.7776 31.0314 15.7041 29.058C12.6417 27.8668 10.5134 25.3136 9.73915 22.3604C8.96587 22.5252 8.22136 22.6642 7.51359 22.7774C8.36431 26.211 10.704 29.2228 14.093 30.8229L12.5434 34.8095C11.8655 34.5465 11.1021 34.882 10.838 35.56L5.12225 50.2604C4.68945 51.3742 5.2096 52.6488 6.31544 53.0995C7.4451 53.561 8.72961 53.0061 9.17035 51.8725L14.9 37.1393C15.1641 36.4613 14.8276 35.698 14.1496 35.4339L15.6455 31.5883C15.2495 31.4126 14.8683 31.217 14.501 31.0046C14.629 31.0592 14.7571 31.1128 14.8861 31.1634C21.1221 33.5885 28.1442 30.4993 30.5683 24.2624C32.0524 20.4446 31.4697 16.3329 29.3652 13.1614"
                    fill="white"
                />
                <path
                    d="M20.5858 27.5948C17.715 28.0832 14.7827 26.9227 13.0266 24.5929C12.8539 24.3636 12.5462 24.2803 12.2831 24.3964C11.9178 24.5572 11.8007 25.0158 12.0409 25.3345C14.0749 28.0335 17.4698 29.3766 20.7933 28.8098C21.1903 28.7423 21.4186 28.3214 21.2568 27.9531C21.1427 27.693 20.8657 27.5471 20.5858 27.5948Z"
                    fill="white"
                />
                <path d="M38.547 25.4228L38.273 26.9446H33.2293L32.5969 30.5519H36.5289L36.2549 32.0568H32.323L31.3998 37.3051H29.4512L31.5536 25.4228H38.546H38.547Z" fill="white" />
                <path
                    d="M41.1459 27.8846L39.4872 37.3051H37.5386L39.1973 27.8846H41.1469H41.1459ZM39.6579 26.346C39.4644 26.1524 39.3671 25.9072 39.3671 25.6104C39.3671 25.2114 39.518 24.8669 39.8207 24.5761C40.1225 24.2852 40.467 24.1403 40.8551 24.1403C41.1628 24.1403 41.411 24.2376 41.5986 24.4311C41.7862 24.6247 41.8805 24.8699 41.8805 25.1667C41.8805 25.5658 41.7326 25.9102 41.4358 26.2011C41.139 26.4919 40.7975 26.6368 40.4104 26.6368C40.1026 26.6368 39.8515 26.5406 39.6579 26.346Z"
                    fill="white"
                />
                <path
                    d="M50.1462 28.5259C50.7328 29.056 51.0267 29.8164 51.0267 30.8081C51.0267 31.0592 50.9979 31.3719 50.9413 31.7481L49.9496 37.3041H48.0179L48.9579 32.038C49.0036 31.7193 49.0264 31.491 49.0264 31.354C49.0264 30.7276 48.8527 30.2482 48.5053 29.9176C48.1579 29.5871 47.6764 29.4213 47.061 29.4213C46.3204 29.4213 45.696 29.6466 45.1888 30.0963C44.6815 30.547 44.3599 31.1932 44.2229 32.037V31.9854L43.2829 37.3021H41.3333L42.992 27.8817H44.9406L44.753 28.9756C45.1401 28.5884 45.5988 28.2837 46.1288 28.0613C46.6589 27.839 47.2089 27.7278 47.7787 27.7278C48.7703 27.7278 49.5595 27.9928 50.1462 28.5229V28.5259Z"
                    fill="white"
                />
                <path
                    d="M52.5912 30.0387C53.1094 29.3091 53.7457 28.7423 54.4972 28.3373C55.2496 27.9333 56.0417 27.7308 56.8736 27.7308C57.4891 27.7308 58.0708 27.8648 58.6177 28.1328C59.1647 28.4008 59.5806 28.7572 59.8655 29.2009L60.6686 24.6535H62.6351L60.3956 37.3051H58.4291L58.6852 35.8856C58.2753 36.3531 57.765 36.7323 57.1545 37.0222C56.545 37.313 55.875 37.4579 55.1454 37.4579C54.4158 37.4579 53.7894 37.2981 53.2305 36.9795C52.6716 36.6608 52.2359 36.2072 51.9222 35.6205C51.6085 35.0338 51.4526 34.3578 51.4526 33.5945C51.4526 33.253 51.4864 32.9046 51.5549 32.5512C51.7256 31.6052 52.0701 30.7673 52.5892 30.0377L52.5912 30.0387ZM59.3364 31.8851C59.3364 31.1217 59.1111 30.5202 58.6614 30.0814C58.2107 29.6427 57.6499 29.4233 56.9768 29.4233C56.4755 29.4233 55.9852 29.5464 55.5067 29.7906C55.0282 30.0357 54.6123 30.3951 54.2589 30.8676C53.9055 31.3411 53.6722 31.902 53.5581 32.5512C53.5124 32.7676 53.4896 33.0018 53.4896 33.252C53.4896 34.0273 53.7149 34.6398 54.1646 35.0894C54.6143 35.5401 55.1761 35.7644 55.8482 35.7644C56.3495 35.7644 56.8399 35.6394 57.3193 35.3882C57.7978 35.1381 58.2137 34.7698 58.5671 34.2854C58.9195 33.8009 59.1538 33.2341 59.2679 32.5839C59.3126 32.3675 59.3364 32.1333 59.3364 31.8831V31.8851Z"
                    fill="white"
                />
                <path
                    d="M73.5606 34.877H68.5853L67.3028 37.3051H65.2688L71.6288 25.4059H73.8852L76.0393 37.3051H73.9874L73.5596 34.877H73.5606ZM73.3035 33.3562L72.3287 27.7655L69.3884 33.3562H73.3035Z"
                    fill="white"
                />
                <path
                    d="M85.6396 28.5259C86.2262 29.056 86.5201 29.8164 86.5201 30.8081C86.5201 31.0592 86.4913 31.3719 86.4347 31.7481L85.443 37.3041H83.5113L84.4513 32.038C84.496 31.7193 84.5198 31.491 84.5198 31.354C84.5198 30.7276 84.3461 30.2482 83.9987 29.9176C83.6512 29.5871 83.1698 29.4213 82.5543 29.4213C81.8138 29.4213 81.1894 29.6466 80.6822 30.0963C80.1749 30.547 79.8533 31.1932 79.7163 32.037V31.9854L78.7762 37.3021H76.8276L78.4864 27.8817H80.435L80.2474 28.9756C80.6345 28.5884 81.0941 28.2837 81.6242 28.0613C82.1543 27.839 82.7042 27.7278 83.274 27.7278C84.2657 27.7278 85.0549 27.9928 85.6415 28.5229L85.6396 28.5259Z"
                    fill="white"
                />
                <path d="M89.5792 27.8846L90.964 35.0646L94.9823 27.8846H96.9994L88.793 41.7324H86.7759L89.4938 37.1502L87.4082 27.8836H89.5792V27.8846Z" fill="white" />
                <path
                    d="M109 34.9801C109 36.6548 107.655 37.9869 106.007 37.9869C104.36 37.9869 103.027 36.6548 103.027 34.9801C103.027 33.3055 104.347 32.0131 106.007 32.0131C107.668 32.0131 109 33.3452 109 34.9801ZM108.341 34.9801C108.341 33.6877 107.313 32.6464 106.02 32.6464C104.728 32.6464 103.686 33.7016 103.686 34.9801C103.686 36.2587 104.702 37.3407 106.02 37.3407C107.339 37.3407 108.341 36.2726 108.341 34.9801ZM106.746 35.0467C107.115 35.1122 107.234 35.3365 107.234 35.7058V35.9827C107.234 36.1942 107.26 36.3917 107.3 36.5367H106.733C106.693 36.4046 106.654 36.2597 106.654 35.9956V35.7316C106.654 35.402 106.522 35.2442 106.165 35.2442H105.466V36.5367H104.873V33.4643H106.337C106.956 33.4643 107.339 33.7681 107.339 34.3081C107.339 34.6774 107.128 34.9672 106.746 35.0467ZM106.692 34.3607C106.692 34.0967 106.56 33.9517 106.257 33.9517H105.466V34.7826H106.218C106.547 34.7826 106.692 34.6377 106.692 34.3607Z"
                    fill="#1D7EB6"
                />
            </svg>
        </div>
    );
};
export const DashboardIcon = ({ fillColor }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
            <path
                d="M20 17.9206V8.74603C20 8.57847 19.9616 8.4132 19.8878 8.26333C19.8141 8.11345 19.707 7.98308 19.575 7.88254L12.1375 2.21587C11.9536 2.07575 11.7299 2 11.5 2C11.2701 2 11.0464 2.07575 10.8625 2.21587L3.425 7.88254C3.29304 7.98308 3.18594 8.11345 3.11217 8.26333C3.0384 8.4132 3 8.57847 3 8.74603V17.9206C3 18.2069 3.11194 18.4814 3.3112 18.6839C3.51046 18.8863 3.78071 19 4.0625 19H8.3125C8.59429 19 8.86454 18.8863 9.0638 18.6839C9.26306 18.4814 9.375 18.2069 9.375 17.9206V14.6825C9.375 14.3963 9.48694 14.1217 9.6862 13.9193C9.88546 13.7169 10.1557 13.6032 10.4375 13.6032H12.5625C12.8443 13.6032 13.1145 13.7169 13.3138 13.9193C13.5131 14.1217 13.625 14.3963 13.625 14.6825V17.9206C13.625 18.2069 13.7369 18.4814 13.9362 18.6839C14.1355 18.8863 14.4057 19 14.6875 19H18.9375C19.2193 19 19.4895 18.8863 19.6888 18.6839C19.8881 18.4814 20 18.2069 20 17.9206Z"
                fill={fillColor ? fillColor : '#636363'}
            />
        </svg>
    );
};
export const CRMIcon = ({ fillColor }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
            <path
                d="M19.8592 8.66621H14.9298C14.4893 8.66621 14.1328 8.25278 14.1328 7.74323V7.72364C14.1328 7.21409 14.4893 6.80066 14.9298 6.80066H19.8592C20.3034 6.80066 20.6655 7.22062 20.6655 7.7339C20.6655 8.24719 20.3034 8.66714 19.8592 8.66714V8.66621ZM19.8592 12.3992H14.9298C14.4893 12.3992 14.1328 11.9858 14.1328 11.4762V11.4557C14.1328 10.9461 14.4893 10.5327 14.9298 10.5327H19.8592C20.3034 10.5327 20.6655 10.9527 20.6655 11.4659C20.6655 11.9792 20.3034 12.3992 19.8592 12.3992ZM19.8797 16.1321H16.7748C16.3455 16.1321 15.9993 15.7187 15.9993 15.2092V15.1886C15.9993 14.6791 16.3465 14.2657 16.7748 14.2657H19.8797C20.3118 14.2657 20.6655 14.6856 20.6655 15.1989C20.6655 15.7122 20.3118 16.1321 19.8797 16.1321ZM8.53338 11.4659C7.54333 11.4659 6.59384 11.0726 5.89377 10.3726C5.1937 9.67251 4.80041 8.72301 4.80041 7.73297C4.80041 6.74292 5.1937 5.79343 5.89377 5.09336C6.59384 4.39329 7.54333 4 8.53338 4C9.52342 4 10.4729 4.39329 11.173 5.09336C11.8731 5.79343 12.2663 6.74292 12.2663 7.73297C12.2663 8.72301 11.8731 9.67251 11.173 10.3726C10.4729 11.0726 9.52342 11.4659 8.53338 11.4659ZM2.90593 19C2.36185 19 1.92789 18.5166 2.01002 17.98C2.25274 16.4245 3.04365 15.007 4.23994 13.9836C5.43623 12.9601 6.95904 12.3982 8.53338 12.3992C10.1076 12.3984 11.6301 12.9604 12.8262 13.9839C14.0223 15.0073 14.8131 16.4246 15.0558 17.98C15.1398 18.5166 14.7049 19 14.1618 19H2.90593Z"
                fill={fillColor ? fillColor : '#636363'}
            />
        </svg>
    );
};
export const ListingIcon = ({ fillColor }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
            <path
                d="M17.3089 7.79928H3.68313C3.30557 7.79928 3 7.40054 3 6.90909V6.89019C3 6.39874 3.30557 6 3.68313 6H17.3089C17.6896 6 18 6.40504 18 6.90009C18 7.39514 17.6896 7.79928 17.3089 7.79928ZM17.3089 11.3996H3.68313C3.30557 11.3996 3 11.0009 3 10.5095V10.4896C3 9.9982 3.30557 9.59946 3.68313 9.59946H17.3089C17.6896 9.59946 18 10.0045 18 10.4995C18 10.9946 17.6896 11.3996 17.3089 11.3996ZM14.3265 15H3.68313C3.31517 15 3.0184 14.6013 3.0184 14.1098V14.09C3.0184 13.5986 3.31597 13.1998 3.68313 13.1998H14.3265C14.6968 13.1998 15 13.6049 15 14.0999C15 14.595 14.6968 15 14.3265 15Z"
                fill={fillColor ? fillColor : '#636363'}
            />
        </svg>
    );
};
export const AdManagerIcon = ({ fillColor }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
            <path
                d="M3.00011 10C3.25499 10.0003 3.50015 10.0979 3.68548 10.2728C3.87082 10.4478 3.98235 10.687 3.99728 10.9414C4.01222 11.1958 3.92944 11.4464 3.76585 11.6418C3.60226 11.8373 3.37021 11.9629 3.11711 11.993L3.00011 12H2.00011C1.74523 11.9997 1.50008 11.9021 1.31474 11.7272C1.12941 11.5522 1.01788 11.313 1.00294 11.0586C0.988004 10.8042 1.07079 10.5536 1.23438 10.3582C1.39797 10.1627 1.63002 10.0371 1.88311 10.007L2.00011 10H3.00011ZM11.0001 1C11.245 1.00003 11.4815 1.08996 11.6645 1.25272C11.8475 1.41547 11.9645 1.63975 11.9931 1.883L12.0001 2V3C11.9998 3.25488 11.9022 3.50003 11.7273 3.68537C11.5523 3.8707 11.3132 3.98223 11.0587 3.99717C10.8043 4.01211 10.5537 3.92933 10.3583 3.76574C10.1628 3.60214 10.0372 3.3701 10.0071 3.117L10.0001 3V2C10.0001 1.73478 10.1055 1.48043 10.293 1.29289C10.4805 1.10536 10.7349 1 11.0001 1ZM20.0001 10C20.255 10.0003 20.5001 10.0979 20.6855 10.2728C20.8708 10.4478 20.9823 10.687 20.9973 10.9414C21.0122 11.1958 20.9294 11.4464 20.7658 11.6418C20.6023 11.8373 20.3702 11.9629 20.1171 11.993L20.0001 12H19.0001C18.7452 11.9997 18.5001 11.9021 18.3147 11.7272C18.1294 11.5522 18.0179 11.313 18.0029 11.0586C17.988 10.8042 18.0708 10.5536 18.2344 10.3582C18.398 10.1627 18.63 10.0371 18.8831 10.007L19.0001 10H20.0001ZM3.89311 3.893C4.06531 3.72082 4.29441 3.61739 4.53743 3.60211C4.78046 3.58683 5.02071 3.66075 5.21311 3.81L5.30711 3.893L6.00711 4.593C6.18646 4.77296 6.29059 5.01443 6.29834 5.26838C6.3061 5.52233 6.2169 5.76971 6.04886 5.96028C5.88083 6.15084 5.64656 6.27031 5.39364 6.2944C5.14072 6.31849 4.8881 6.24541 4.68711 6.09L4.59311 6.007L3.89311 5.307C3.70564 5.11947 3.60033 4.86516 3.60033 4.6C3.60033 4.33484 3.70564 4.08053 3.89311 3.893ZM16.6931 3.893C16.8731 3.71365 17.1145 3.60953 17.3685 3.60177C17.6224 3.59402 17.8698 3.68322 18.0604 3.85125C18.251 4.01928 18.3704 4.25355 18.3945 4.50647C18.4186 4.7594 18.3455 5.01201 18.1901 5.213L18.1071 5.307L17.4071 6.007C17.2272 6.18635 16.9857 6.29047 16.7317 6.29823C16.4778 6.30598 16.2304 6.21678 16.0398 6.04875C15.8493 5.88072 15.7298 5.64645 15.7057 5.39353C15.6816 5.1406 15.7547 4.88799 15.9101 4.687L15.9931 4.593L16.6931 3.893ZM13.0001 17C13.2653 17 13.5197 17.1054 13.7072 17.2929C13.8948 17.4804 14.0001 17.7348 14.0001 18C14.0001 18.7956 13.684 19.5587 13.1214 20.1213C12.5588 20.6839 11.7958 21 11.0001 21C10.2045 21 9.4414 20.6839 8.87879 20.1213C8.31618 19.5587 8.00011 18.7956 8.00011 18C8.00015 17.7551 8.09007 17.5187 8.25283 17.3356C8.41559 17.1526 8.63986 17.0357 8.88311 17.007L9.00011 17H13.0001ZM11.0001 5C12.2594 5 13.4868 5.39622 14.5084 6.13255C15.53 6.86887 16.294 7.90796 16.6922 9.10263C17.0904 10.2973 17.1027 11.587 16.7272 12.789C16.3517 13.991 15.6075 15.0444 14.6001 15.8C14.4623 15.9035 14.3 15.9697 14.1291 15.992L14.0001 16H8.00011C7.78374 16 7.57321 15.9298 7.40011 15.8C6.39268 15.0444 5.64851 13.991 5.27303 12.789C4.89755 11.587 4.90979 10.2973 5.30801 9.10263C5.70624 7.90796 6.47026 6.86887 7.49185 6.13255C8.51344 5.39622 9.74082 5 11.0001 5Z"
                fill={fillColor ? fillColor : '#636363'}
            />
        </svg>
    );
};
export const MessageIcon = ({ fillColor }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
            <path
                d="M18.2 3H3.8C2.81 3 2.009 3.81 2.009 4.8L2 21L5.6 17.4H18.2C19.19 17.4 20 16.59 20 15.6V4.8C20 3.81 19.19 3 18.2 3ZM15.5 13.8H6.5C6.005 13.8 5.6 13.395 5.6 12.9C5.6 12.405 6.005 12 6.5 12H15.5C15.995 12 16.4 12.405 16.4 12.9C16.4 13.395 15.995 13.8 15.5 13.8ZM15.5 11.1H6.5C6.005 11.1 5.6 10.695 5.6 10.2C5.6 9.705 6.005 9.3 6.5 9.3H15.5C15.995 9.3 16.4 9.705 16.4 10.2C16.4 10.695 15.995 11.1 15.5 11.1ZM15.5 8.4H6.5C6.005 8.4 5.6 7.995 5.6 7.5C5.6 7.005 6.005 6.6 6.5 6.6H15.5C15.995 6.6 16.4 7.005 16.4 7.5C16.4 7.995 15.995 8.4 15.5 8.4Z"
                fill={fillColor ? fillColor : '#636363'}
            />
        </svg>
    );
};
export const ReviewsIcon = ({ fillColor }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
            <path
                d="M11.4995 17.0795L6.8945 19.8312C6.69107 19.9596 6.47839 20.0147 6.25646 19.9963C6.03453 19.978 5.84034 19.9046 5.67389 19.7762C5.50745 19.6478 5.37799 19.4874 5.28552 19.2952C5.19305 19.1029 5.17455 18.8872 5.23004 18.648L6.45065 13.4472L2.3727 9.9525C2.18776 9.7874 2.07236 9.59918 2.02649 9.38785C1.98063 9.17651 1.99431 8.97032 2.06755 8.76926C2.14079 8.5682 2.25175 8.40309 2.40044 8.27394C2.54914 8.1448 2.75257 8.06225 3.01075 8.02629L8.39252 7.5585L10.4731 2.66042C10.5656 2.44028 10.7091 2.27517 10.9036 2.1651C11.0982 2.05503 11.2968 2 11.4995 2C11.7022 2 11.9008 2.05503 12.0954 2.1651C12.29 2.27517 12.4335 2.44028 12.5259 2.66042L14.6065 7.5585L19.9883 8.02629C20.2472 8.06298 20.4507 8.14553 20.5986 8.27394C20.7466 8.40236 20.8575 8.56746 20.9315 8.76926C21.0055 8.97105 21.0195 9.17761 20.9737 9.38895C20.9278 9.60028 20.812 9.78813 20.6264 9.9525L16.5484 13.4472L17.769 18.648C17.8245 18.8865 17.806 19.1022 17.7135 19.2952C17.6211 19.4882 17.4916 19.6485 17.3252 19.7762C17.1587 19.9039 16.9645 19.9772 16.7426 19.9963C16.5207 20.0154 16.308 19.9604 16.1046 19.8312L11.4995 17.0795Z"
                fill={fillColor ? fillColor : '#636363'}
            />
        </svg>
    );
};
export const AgentsIcon = ({ fillColor }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
            <path
                d="M9.75 18C9.75 18 8.5 18 8.5 16.75C8.5 15.5 9.75 11.75 14.75 11.75C19.75 11.75 21 15.5 21 16.75C21 18 19.75 18 19.75 18H9.75ZM14.75 10.5C15.7446 10.5 16.6984 10.1049 17.4017 9.40165C18.1049 8.69839 18.5 7.74456 18.5 6.75C18.5 5.75544 18.1049 4.80161 17.4017 4.09835C16.6984 3.39509 15.7446 3 14.75 3C13.7554 3 12.8016 3.39509 12.0983 4.09835C11.3951 4.80161 11 5.75544 11 6.75C11 7.74456 11.3951 8.69839 12.0983 9.40165C12.8016 10.1049 13.7554 10.5 14.75 10.5ZM7.52 18C7.33478 17.6097 7.24237 17.1819 7.25 16.75C7.25 15.0562 8.1 13.3125 9.67 12.1C8.88648 11.858 8.06997 11.74 7.25 11.75C2.25 11.75 1 15.5 1 16.75C1 18 2.25 18 2.25 18H7.52ZM6.625 10.5C7.4538 10.5 8.24866 10.1708 8.83471 9.58471C9.42076 8.99866 9.75 8.2038 9.75 7.375C9.75 6.5462 9.42076 5.75134 8.83471 5.16529C8.24866 4.57924 7.4538 4.25 6.625 4.25C5.7962 4.25 5.00134 4.57924 4.41529 5.16529C3.82924 5.75134 3.5 6.5462 3.5 7.375C3.5 8.2038 3.82924 8.99866 4.41529 9.58471C5.00134 10.1708 5.7962 10.5 6.625 10.5Z"
                fill={fillColor ? fillColor : '#636363'}
            />
        </svg>
    );
};
export const SubscribeIcon = ({ fillColor }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
            <path d="M2 2V21L5 19L8 21L11 19L14 21L17 19L20 21V2H2ZM16 6V8H6V6H16ZM14 10V12H6V10H14Z" fill={fillColor ? fillColor : '#636363'} />
        </svg>
    );
};
export const ReportIcon = ({ fillColor }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="18" viewBox="0 0 16 18" fill="none">
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M0.64493 0C0.473884 0 0.309844 0.0679479 0.188896 0.188896C0.0679479 0.309844 0 0.473884 0 0.64493V14.7173C0 14.8883 0.0679479 15.0524 0.188896 15.1733C0.309844 15.2943 0.473884 15.3622 0.64493 15.3622H12.2537C12.4247 15.3622 12.5888 15.2943 12.7097 15.1733C12.8307 15.0524 12.8986 14.8883 12.8986 14.7173V5.57091C12.8988 5.48431 12.8815 5.39856 12.8478 5.31878C12.8141 5.23901 12.7647 5.16683 12.7025 5.10656L7.62436 0.18445C7.50425 0.0673429 7.34324 0.00164262 7.17549 0.00128992L0.64493 0ZM10.662 4.92597L7.82042 2.16954V4.92597H10.662ZM5.15944 5.74633H2.57972V4.45647H5.15944V5.74633ZM10.3189 8.97098H2.57972V7.68112H10.3189V8.97098ZM2.57972 12.1956H10.3189V10.9058H2.57972V12.1956Z"
                fill={fillColor ? fillColor : '#636363'}
            />
            <path
                d="M14.1884 8.32605V16.7101H3.22461V18H14.8333C15.0044 18 15.1684 17.9321 15.2894 17.8111C15.4103 17.6902 15.4783 17.5261 15.4783 17.3551V8.32605H14.1884Z"
                fill={fillColor ? fillColor : '#636363'}
            />
        </svg>
    );
};
export const DownIcon = ({ className, fillColor }: any) => {
    return (
        <svg className={className} xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M4 7.85714L9.21905 12.3306C9.66844 12.7158 10.3316 12.7158 10.7809 12.3306L16 7.85714" stroke={fillColor ? fillColor : '#636363'} stroke-width="1.5" stroke-linecap="round" />
        </svg>
    );
};

export const DocumentIcon = ({ className, fillColor }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" className={className || 'h-5 w-5'} width="24" height="24" viewBox="0 0 24 24">
            <path
                fill={fillColor ? fillColor : '#636363'}
                d="M4 20q-.825 0-1.412-.587T2 18V6q0-.825.588-1.412T4 4h5.175q.4 0 .763.15t.637.425L12 6h8q.825 0 1.413.588T22 8v10q0 .825-.587 1.413T20 20z"
            />
        </svg>
    );
};

export const UpIcon = ({ className, fillColor }: any) => {
    return (
        <svg className={className} xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
            <path
                d="M6 7.26211C5.76507 7.26252 5.53238 7.2165 5.3153 7.12669C5.09821 7.03688 4.90102 6.90505 4.73505 6.73878L0 2.00283L1.26495 0.737885L6 5.47294L10.7351 0.737885L12 2.00283L7.26495 6.73788C7.09905 6.90431 6.90189 7.03631 6.6848 7.12627C6.46772 7.21624 6.23499 7.2624 6 7.26211Z"
                fill={fillColor ? fillColor : 'white'}
            />
        </svg>
    );
};
export const BellIcon = ({ className, fillColor }: any) => {
    return (
        <div data-svg-wrapper className="absolute left-[1.20px] top-[0.02px]">
            <svg width="22" height="24" viewBox="0 0 22 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M3.8002 9.6C3.79954 8.10403 4.26487 6.645 5.13154 5.42565C5.9982 4.2063 7.22316 3.28718 8.6362 2.796C8.58148 2.45317 8.60178 2.10255 8.69572 1.76834C8.78965 1.43412 8.95498 1.12426 9.18029 0.860135C9.4056 0.596011 9.68552 0.383913 10.0008 0.238473C10.316 0.0930336 10.659 0.0177155 11.0062 0.0177155C11.3534 0.0177155 11.6964 0.0930336 12.0116 0.238473C12.3269 0.383913 12.6068 0.596011 12.8321 0.860135C13.0574 1.12426 13.2227 1.43412 13.3167 1.76834C13.4106 2.10255 13.4309 2.45317 13.3762 2.796C14.787 3.28919 16.0094 4.20917 16.8737 5.42835C17.7381 6.64753 18.2017 8.10549 18.2002 9.6V16.8L21.8002 19.2V20.4H0.200195V19.2L3.8002 16.8V9.6ZM13.4002 21.6C13.4002 22.2365 13.1473 22.847 12.6973 23.2971C12.2472 23.7471 11.6367 24 11.0002 24C10.3637 24 9.75323 23.7471 9.30314 23.2971C8.85305 22.847 8.6002 22.2365 8.6002 21.6H13.4002Z"
                    fill="white"
                />
            </svg>
        </div>
    );
};
export const EditIcon = ({ className, fillColor }: any) => {
    return (
        <div data-svg-wrapper className="relative">
            <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 21.5H21" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M7 17.5V13.5L17 3.5L21 7.5L11 17.5H7Z" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M14 6.5L18 10.5" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
        </div>
    );
};

export const InfoIcon = ({ className = 'min-w-6 min-h-6' }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="26" height="27" viewBox="0 0 26 27" fill="none" className={className}>
            <path
                d="M12 18.5H14V12.5H12V18.5ZM13 10.5C13.2833 10.5 13.521 10.404 13.713 10.212C13.905 10.02 14.0007 9.78266 14 9.5C13.9993 9.21733 13.9033 8.98 13.712 8.788C13.5207 8.596 13.2833 8.5 13 8.5C12.7167 8.5 12.4793 8.596 12.288 8.788C12.0967 8.98 12.0007 9.21733 12 9.5C11.9993 9.78266 12.0953 10.0203 12.288 10.213C12.4807 10.4057 12.718 10.5013 13 10.5ZM13 23.5C11.6167 23.5 10.3167 23.2373 9.1 22.712C7.88333 22.1867 6.825 21.4743 5.925 20.575C5.025 19.6757 4.31267 18.6173 3.788 17.4C3.26333 16.1827 3.00067 14.8827 3 13.5C2.99933 12.1173 3.262 10.8173 3.788 9.6C4.314 8.38267 5.02633 7.32433 5.925 6.425C6.82367 5.52567 7.882 4.81333 9.1 4.288C10.318 3.76267 11.618 3.5 13 3.5C14.382 3.5 15.682 3.76267 16.9 4.288C18.118 4.81333 19.1763 5.52567 20.075 6.425C20.9737 7.32433 21.6863 8.38267 22.213 9.6C22.7397 10.8173 23.002 12.1173 23 13.5C22.998 14.8827 22.7353 16.1827 22.212 17.4C21.6887 18.6173 20.9763 19.6757 20.075 20.575C19.1737 21.4743 18.1153 22.187 16.9 22.713C15.6847 23.239 14.3847 23.5013 13 23.5ZM13 21.5C15.2333 21.5 17.125 20.725 18.675 19.175C20.225 17.625 21 15.7333 21 13.5C21 11.2667 20.225 9.375 18.675 7.825C17.125 6.275 15.2333 5.5 13 5.5C10.7667 5.5 8.875 6.275 7.325 7.825C5.775 9.375 5 11.2667 5 13.5C5 15.7333 5.775 17.625 7.325 19.175C8.875 20.725 10.7667 21.5 13 21.5Z"
                fill="#1D7EB6"
            />
        </svg>
    );
};

export const RedInfoIcon = ({ className = 'w-6 h-6' }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none" className={className}>
            <path
                d="M11.625 17.1165H13.625V11.1165H11.625V17.1165ZM12.625 9.11645C12.9083 9.11645 13.146 9.02045 13.338 8.82845C13.53 8.63645 13.6257 8.39912 13.625 8.11645C13.6243 7.83379 13.5283 7.59645 13.337 7.40445C13.1457 7.21245 12.9083 7.11645 12.625 7.11645C12.3417 7.11645 12.1043 7.21245 11.913 7.40445C11.7217 7.59645 11.6257 7.83379 11.625 8.11645C11.6243 8.39912 11.7203 8.63679 11.913 8.82945C12.1057 9.02212 12.343 9.11779 12.625 9.11645ZM12.625 22.1164C11.2417 22.1164 9.94166 21.8538 8.725 21.3284C7.50833 20.8031 6.45 20.0908 5.55 19.1915C4.65 18.2921 3.93767 17.2338 3.413 16.0165C2.88833 14.7991 2.62567 13.4991 2.625 12.1165C2.62433 10.7338 2.887 9.43379 3.413 8.21645C3.939 6.99912 4.65133 5.94079 5.55 5.04145C6.44867 4.14212 7.507 3.42979 8.725 2.90445C9.943 2.37912 11.243 2.11646 12.625 2.11646C14.007 2.11646 15.307 2.37912 16.525 2.90445C17.743 3.42979 18.8013 4.14212 19.7 5.04145C20.5987 5.94079 21.3113 6.99912 21.838 8.21645C22.3647 9.43379 22.627 10.7338 22.625 12.1165C22.623 13.4991 22.3603 14.7991 21.837 16.0165C21.3137 17.2338 20.6013 18.2921 19.7 19.1915C18.7987 20.0908 17.7403 20.8034 16.525 21.3294C15.3097 21.8554 14.0097 22.1178 12.625 22.1164ZM12.625 20.1164C14.8583 20.1164 16.75 19.3414 18.3 17.7914C19.85 16.2415 20.625 14.3498 20.625 12.1165C20.625 9.88312 19.85 7.99145 18.3 6.44145C16.75 4.89145 14.8583 4.11645 12.625 4.11645C10.3917 4.11645 8.5 4.89145 6.95 6.44145C5.4 7.99145 4.625 9.88312 4.625 12.1165C4.625 14.3498 5.4 16.2415 6.95 17.7914C8.5 19.3414 10.3917 20.1164 12.625 20.1164Z"
                fill="#993333"
            />
        </svg>
    );
};
export const RedInfoFillIcon = ({ className = 'w-6 h-6' }: any) => {
    return (
        <div data-svg-wrapper className="relative">
            <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M18 2.5C9.72 2.5 3 9.22 3 17.5C3 25.78 9.72 32.5 18 32.5C26.28 32.5 33 25.78 33 17.5C33 9.22 26.28 2.5 18 2.5ZM19.5 25H16.5V16H19.5V25ZM19.5 13H16.5V10H19.5V13Z"
                    fill="#993333"
                />
            </svg>
        </div>
    );
};
export const SuccessIconIcon = ({ className = 'w-6 h-6' }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="220" height="168" viewBox="0 0 220 168" fill="none" className={className}>
            <path d="M2.15137 57.9689C9.62008 55.4793 25.9318 54.3839 31.4288 69.9188C36.9258 85.4538 46.2667 87.1468 50.2501 86.0513" stroke="#2AB141" strokeWidth="2.46922" strokeLinecap="round" />
            <path d="M169.751 87.7814C177.22 85.2919 193.531 84.1964 199.028 99.7314C204.525 115.266 213.866 116.959 217.85 115.864" stroke="#2AB141" strokeWidth="2.46922" strokeLinecap="round" />
            <path
                d="M102.215 35.0384C104.851 31.7781 109.821 31.7781 112.457 35.0384L117.996 41.8906C119.633 43.9163 122.311 44.7863 124.826 44.11L133.335 41.8222C137.384 40.7337 141.405 43.6553 141.621 47.842L142.074 56.6413C142.208 59.2426 143.863 61.5203 146.296 62.4517L154.524 65.6022C158.439 67.1012 159.975 71.8285 157.689 75.3424L152.884 82.7279C151.463 84.9111 151.463 87.7265 152.884 89.9098L157.689 97.2952C159.975 100.809 158.439 105.537 154.524 107.035L146.296 110.186C143.863 111.117 142.208 113.395 142.074 115.996L141.621 124.796C141.405 128.982 137.384 131.904 133.335 130.815L124.826 128.528C122.311 127.851 119.633 128.721 117.996 130.747L112.457 137.599C109.821 140.86 104.851 140.86 102.215 137.599L96.6762 130.747C95.0387 128.721 92.3611 127.851 89.8457 128.528L81.3369 130.815C77.2884 131.904 73.2671 128.982 73.0513 124.796L72.5978 115.996C72.4637 113.395 70.8088 111.117 68.3763 110.186L60.1478 107.035C56.2327 105.537 54.6967 100.809 56.983 97.2952L61.7882 89.9098C63.2087 87.7265 63.2087 84.9111 61.7882 82.7279L56.983 75.3424C54.6967 71.8285 56.2327 67.1012 60.1478 65.6022L68.3763 62.4517C70.8088 61.5203 72.4637 59.2426 72.5978 56.6413L73.0513 47.842C73.2671 43.6553 77.2884 40.7337 81.3369 41.8222L89.8457 44.11C92.3611 44.7863 95.0387 43.9163 96.6762 41.8906L102.215 35.0384Z"
                fill="#2AB141"
            />
            <path d="M121.703 76.442L101.949 96.1958L92.9697 87.2168" stroke="white" strokeWidth="4.11537" strokeLinecap="round" strokeLinejoin="round" />
            <path
                d="M172.938 29.4945C173.197 27.9227 175.457 27.9227 175.716 29.4945L176.517 34.3566L181.379 35.1574C182.951 35.4164 182.951 37.6764 181.379 37.9353L176.517 38.7362L175.716 43.5983C175.457 45.1701 173.197 45.1701 172.938 43.5983L172.138 38.7362L167.276 37.9353C165.704 37.6764 165.704 35.4164 167.276 35.1574L172.138 34.3566L172.938 29.4945Z"
                fill="#FFCC00"
            />
            <path
                d="M41.112 121.399C41.358 119.906 43.505 119.906 43.751 121.399L44.5118 126.018L49.1308 126.779C50.624 127.025 50.624 129.172 49.1308 129.418L44.5118 130.179L43.751 134.798C43.505 136.291 41.358 136.291 41.112 134.798L40.3512 130.179L35.7322 129.418C34.239 129.172 34.239 127.025 35.7322 126.779L40.3512 126.018L41.112 121.399Z"
                fill="#FFCC00"
            />
            <path
                d="M36.9035 33.4183C37.0848 32.318 38.6668 32.318 38.848 33.4183L39.4086 36.8217L42.8121 37.3823C43.9124 37.5636 43.9124 39.1456 42.8121 39.3269L39.4086 39.8875L38.848 43.2909C38.6668 44.3912 37.0848 44.3912 36.9035 43.2909L36.3429 39.8875L32.9395 39.3269C31.8392 39.1456 31.8392 37.5636 32.9395 37.3823L36.3429 36.8217L36.9035 33.4183Z"
                fill="#FFCC00"
            />
            <path
                d="M180.852 137.566C181.033 136.465 182.615 136.465 182.796 137.566L183.357 140.969L186.76 141.53C187.861 141.711 187.861 143.293 186.76 143.474L183.357 144.035L182.796 147.438C182.615 148.539 181.033 148.539 180.852 147.438L180.291 144.035L176.888 143.474C175.787 143.293 175.787 141.711 176.888 141.53L180.291 140.969L180.852 137.566Z"
                fill="#FFCC00"
            />
            <circle cx="176.331" cy="69.1793" r="3.2923" fill="#2AB141" />
            <circle cx="92.9691" cy="3.29224" r="3.2923" fill="#2AB141" />
            <circle cx="138.872" cy="164.058" r="3.2923" fill="#2AB141" />
            <circle cx="75.3516" cy="153.999" r="2.05769" fill="#2AB141" />
        </svg>
    );
};
export const FileUploadIcon = ({ className = 'w-6 h-6' }: any) => {
    return (
        <div data-svg-wrapper className="relative">
            <svg width="72" height="62" viewBox="0 0 72 62" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_962_12032)">
                    <path
                        d="M37.9327 29.3516C37.2686 28.8057 36.3757 28.5 35.4458 28.5C34.5159 28.5 33.623 28.8057 32.9589 29.3516L22.3008 37.9906C21.9239 38.2514 21.6151 38.5747 21.3939 38.9401C21.1727 39.3056 21.0439 39.7054 21.0153 40.1145C20.9868 40.5236 21.0593 40.9333 21.2282 41.3178C21.3971 41.7024 21.6588 42.0536 21.997 42.3495C22.3351 42.6454 22.7425 42.8796 23.1935 43.0374C23.6445 43.1953 24.1296 43.2734 24.6182 43.2669C25.1069 43.2604 25.5888 43.1695 26.0336 42.9997C26.4784 42.83 26.8767 42.5851 27.2035 42.2803L31.8576 38.497V55.2985C31.8576 56.0886 32.2319 56.8463 32.8981 57.4049C33.5644 57.9636 34.468 58.2775 35.4102 58.2775C36.3525 58.2775 37.2561 57.9636 37.9224 57.4049C38.5886 56.8463 38.9629 56.0886 38.9629 55.2985V38.646L43.5459 42.5187C43.8762 42.7979 44.2691 43.0195 44.702 43.1707C45.135 43.322 45.5993 43.3998 46.0683 43.3998C46.5373 43.3998 47.0017 43.322 47.4346 43.1707C47.8675 43.0195 48.2604 42.7979 48.5907 42.5187C48.9237 42.2417 49.188 41.9122 49.3684 41.5492C49.5487 41.1862 49.6416 40.7968 49.6416 40.4036C49.6416 40.0103 49.5487 39.6209 49.3684 39.2579C49.188 38.8949 48.9237 38.5654 48.5907 38.2885L37.9327 29.3516Z"
                        fill="#D9D9D9"
                    />
                    <path
                        d="M55.4112 17.0491C53.9671 13.0928 51.2582 9.66179 47.6615 7.23339C44.0648 4.80499 39.7592 3.5 35.3437 3.5C30.9283 3.5 26.6227 4.80499 23.026 7.23339C19.4293 9.66179 16.7204 13.0928 15.2762 17.0491C12.0964 17.4529 9.09216 18.6687 6.5849 20.5665C4.07764 22.4643 2.16168 24.9727 1.04197 27.8233C-0.0777378 30.6739 -0.359062 33.7595 0.228089 36.75C0.81524 39.7405 2.24878 42.5234 4.37535 44.801C4.63222 45.23 4.9857 45.6001 5.41061 45.8848C5.83551 46.1695 6.3214 46.3618 6.83367 46.4482C7.34593 46.5345 7.87198 46.5127 8.37435 46.3843C8.87671 46.256 9.34304 46.0241 9.74013 45.7053C10.1372 45.3866 10.4553 44.9887 10.6717 44.5401C10.8881 44.0915 10.9976 43.6031 10.9922 43.1098C10.9869 42.6165 10.8669 42.1304 10.6408 41.6861C10.4147 41.2418 10.0881 40.8502 9.68421 40.5392C8.31493 39.0877 7.41905 37.2909 7.10512 35.3663C6.7912 33.4418 7.07272 31.4723 7.91556 29.6964C8.75841 27.9205 10.1264 26.4146 11.8537 25.361C13.5811 24.3074 15.5936 23.7515 17.6475 23.7605H18.0014C18.8294 23.7764 19.6371 23.5164 20.2837 23.0259C20.9304 22.5353 21.3752 21.8453 21.5407 21.0759C22.1905 18.0416 23.9268 15.3135 26.4552 13.354C28.9837 11.3946 32.1489 10.3241 35.4145 10.3241C38.6801 10.3241 41.8453 11.3946 44.3738 13.354C46.9022 15.3135 48.6385 18.0416 49.2883 21.0759C49.4538 21.8453 49.8986 22.5353 50.5453 23.0259C51.192 23.5164 51.9996 23.7764 52.8276 23.7605H53.0399C55.0938 23.7515 57.1064 24.3074 58.8337 25.361C60.5611 26.4146 61.929 27.9205 62.7719 29.6964C63.6147 31.4723 63.8963 33.4418 63.5823 35.3663C63.2684 37.2909 62.3725 39.0877 61.0032 40.5392C60.6915 40.8707 60.4522 41.2576 60.2993 41.6774C60.1465 42.0972 60.083 42.5416 60.1126 42.9849C60.1422 43.4281 60.2644 43.8614 60.4719 44.2596C60.6795 44.6577 60.9684 45.0129 61.3218 45.3044C61.967 45.8439 62.7974 46.1421 63.6577 46.1433C64.1602 46.1427 64.6568 46.0406 65.1145 45.8439C65.5722 45.6472 65.9804 45.3603 66.3121 45.0024C68.4977 42.7287 69.9819 39.929 70.6025 36.9095C71.2231 33.8899 70.9562 30.7667 69.8309 27.8813C68.7057 24.9959 66.7655 22.4593 64.2224 20.5489C61.6793 18.6385 58.6311 17.4277 55.4112 17.0491Z"
                        fill="#D9D9D9"
                    />
                </g>
                <defs>
                    <clipPath id="clip0_962_12032">
                        <rect width="71.6292" height="61" fill="white" transform="translate(0.114746 0.5)" />
                    </clipPath>
                </defs>
            </svg>
        </div>
    );
};
export const VerifiedIcon = ({ className = 'w-6 h-6' }: any) => {
    return (
        <div data-svg-wrapper className="relative">
            <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_21187_1393)">
                    <mask id="mask0_21187_1393" maskUnits="userSpaceOnUse" x="0" y="0" width="14" height="15">
                        <path
                            d="M7.00033 13.3332C7.76651 13.3341 8.52533 13.1837 9.23319 12.8904C9.94104 12.5972 10.584 12.167 11.1251 11.6246C11.6675 11.0835 12.0977 10.4406 12.3909 9.7327C12.6842 9.02484 12.8346 8.26602 12.8337 7.49984C12.8346 6.73366 12.6842 5.97484 12.3909 5.26698C12.0977 4.55913 11.6675 3.91618 11.1251 3.37509C10.584 2.83264 9.94104 2.40245 9.23319 2.10923C8.52533 1.81602 7.76651 1.66556 7.00033 1.66651C6.23415 1.66556 5.47533 1.81602 4.76747 2.10923C4.05962 2.40245 3.41667 2.83264 2.87558 3.37509C2.33313 3.91618 1.90294 4.55913 1.60972 5.26698C1.31651 5.97484 1.16605 6.73366 1.167 7.49984C1.16605 8.26602 1.31651 9.02484 1.60972 9.7327C1.90294 10.4406 2.33313 11.0835 2.87558 11.6246C3.41667 12.167 4.05962 12.5972 4.76747 12.8904C5.47533 13.1837 6.23415 13.3341 7.00033 13.3332Z"
                            fill="white"
                            stroke="white"
                            stroke-width="1.83333"
                            stroke-linejoin="round"
                        />
                        <path d="M4.66699 7.49985L6.41699 9.24985L9.91699 5.74985" stroke="black" stroke-width="1.83333" stroke-linecap="round" stroke-linejoin="round" />
                    </mask>
                    <g mask="url(#mask0_21187_1393)">
                        <path d="M0 0.5H14V14.5H0V0.5Z" fill="white" />
                    </g>
                </g>
                <defs>
                    <clipPath id="clip0_21187_1393">
                        <rect width="14" height="14" fill="white" transform="translate(0 0.5)" />
                    </clipPath>
                </defs>
            </svg>
        </div>
    );
};
export const BootProfileIcon = ({ className = 'w-6 h-6' }: any) => {
    return (
        <div data-svg-wrapper className="relative">
            <svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M28.0116 53.8773C26.4744 56.3753 23.4295 60.1296 18.345 61.3268L19.5127 57.4837C19.5127 57.4837 15.0045 60.4842 10.6738 61.3268C11.5164 56.9812 14.5169 52.4879 14.5169 52.4879L10.6738 53.6556C11.871 48.5711 15.6253 45.5262 18.1233 43.989L28.7668 41.754L28.0116 53.8773Z"
                    fill="#FFAE4B"
                />
                <path
                    d="M44.8268 64.0982C39.2732 69.6518 30.3813 69.8277 24.6173 64.6283C26.8926 64.3872 29.3455 62.5773 29.6887 59.3925C29.8317 58.0697 29.6092 56.5087 28.8585 54.7242L17.2754 43.141C15.4909 42.3904 13.9299 42.1679 12.6071 42.3109C9.42224 42.654 7.6124 45.1068 7.3713 47.3821C2.17199 41.6182 2.34766 32.7265 7.90134 27.1727C11.1854 23.8886 15.6395 22.4857 19.9229 22.9642C21.8869 23.184 23.8144 23.7971 25.5774 24.8085L47.1908 46.4219C48.2023 48.1849 48.8165 50.1134 49.0351 52.0765C49.5139 56.3598 48.1107 60.8142 44.8268 64.0982Z"
                    fill="#8080D2"
                />
                <path
                    d="M49.0356 52.0766C43.2828 55.9186 36.7037 58.3415 29.689 59.3922C29.832 58.0694 29.6095 56.5084 28.8589 54.7239L17.2758 43.1408C15.4913 42.3901 13.9303 42.1676 12.6074 42.3106C13.6581 35.2959 16.0812 28.7167 19.9231 22.9641C21.8871 23.1838 23.8146 23.797 25.5776 24.8083L47.1911 46.4218C48.2027 48.185 48.8169 50.1133 49.0356 52.0766Z"
                    fill="#4B4BC0"
                />
                <path
                    d="M28.8584 54.7244C37.8742 52.6537 46.4365 48.1081 53.46 41.0845C63.7755 30.7691 68.748 17.1349 68.3809 3.61894C54.8649 3.25178 41.2307 8.22416 30.9153 18.5396C23.8916 25.5633 19.3461 34.1255 17.2754 43.1413L28.8584 54.7244Z"
                    fill="#E3E8EC"
                />
                <path
                    d="M36.7734 24.3984C29.7503 31.4216 24.1603 38.941 20.2854 46.1511L17.2754 43.1411C19.3449 34.1259 23.8918 25.5632 30.915 18.5401C41.2303 8.22447 54.8645 3.25235 68.3806 3.61976C58.4718 6.85975 47.0889 14.083 36.7734 24.3984Z"
                    fill="#D5D8DB"
                />
                <path
                    d="M65.367 22.3745C62.3467 20.5601 59.3516 18.2324 56.5602 15.4412C55.2123 14.0933 53.9735 12.6993 52.8481 11.2785C51.6412 9.75383 50.5634 8.1975 49.623 6.63446C55.6367 4.461 61.9775 3.4561 68.293 3.61696C68.3229 3.61886 68.3529 3.61886 68.3819 3.61974L68.381 3.62063C68.5528 9.96418 67.5498 16.3329 65.367 22.3745Z"
                    fill="#8080D2"
                />
                <path d="M66.4014 47.1339C66.4004 47.1329 66.4014 47.1318 66.4004 47.1308L66.4014 47.1339Z" fill="#E30E08" />
                <path
                    d="M68.3796 3.6198L68.3788 3.62069C63.4608 5.22765 58.1792 7.81699 52.8462 11.2786C51.6393 9.7539 50.5615 8.19756 49.6211 6.63452C55.6346 4.46106 61.9755 3.45616 68.2909 3.61702C68.3208 3.61892 68.3507 3.61892 68.3796 3.6198Z"
                    fill="#4B4BC0"
                />
                <path
                    d="M45.7262 35.7987C50.9868 35.7987 55.2513 31.5342 55.2513 26.2737C55.2513 21.0131 50.9868 16.7486 45.7262 16.7486C40.4657 16.7486 36.2012 21.0131 36.2012 26.2737C36.2012 31.5342 40.4657 35.7987 45.7262 35.7987Z"
                    fill="#4B4BC0"
                />
                <path
                    d="M45.7268 32.6879C49.2693 32.6879 52.1411 29.8162 52.1411 26.2736C52.1411 22.7311 49.2693 19.8593 45.7268 19.8593C42.1843 19.8593 39.3125 22.7311 39.3125 26.2736C39.3125 29.8162 42.1843 32.6879 45.7268 32.6879Z"
                    fill="#C2C2FF"
                />
            </svg>
        </div>
    );
};
export const BootServiceIcon = ({ className = 'w-6 h-6' }: any) => {
    return (
        <div data-svg-wrapper className="relative">
            <svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_20059_31193)">
                    <path d="M72.0003 19.8916V50.1579L62.1252 54.7852L52.25 50.3426V20.0778C58.5132 20.0183 54.4308 20.0568 72.0003 19.8916Z" fill="#ACCDFF" />
                    <path d="M62.1252 24.5204V54.7852L52.25 50.3426V20.0778L57.1534 20.0312L62.1252 24.5204Z" fill="#81B0FE" />
                    <path d="M62.1247 15.6346L71.9996 19.8922L62.1247 24.5197L52.25 20.0771L62.1247 15.6346Z" fill="#6396FF" />
                    <path d="M59.4183 34.1922V55.9877L49.5431 60.6165L39.668 56.174V34.3769C51.1768 34.2687 46.7625 34.31 59.4183 34.1922Z" fill="#F79FB2" />
                    <path d="M49.5431 34.3769V60.6166L39.668 56.174V34.3769L49.2468 34.2869L49.5431 34.3769Z" fill="#FA8292" />
                    <path d="M49.5437 29.9342L59.4185 34.1916L49.5437 38.8193L39.6689 34.3768L49.5437 29.9342Z" fill="#FF4C4C" />
                    <path d="M46.5691 48.4914V61.9572L36.6955 66.586C34.4239 65.5641 28.9698 63.1103 26.8203 62.1434V48.676C32.1981 48.6251 28.3123 48.6614 46.5691 48.4914Z" fill="#FAE26B" />
                    <path d="M36.6955 53.1186V66.586L28.2277 62.7765L26.8203 60.8322V48.676L30.9138 48.6372L36.6955 53.1186Z" fill="#EDD15A" />
                    <path d="M36.6942 44.2338L46.5689 48.4913L36.6942 53.119L26.8193 48.6764L36.6942 44.2338Z" fill="#EAC736" />
                    <path d="M33.7201 59.0431V67.3728L23.8449 72L13.9697 67.5574V59.2278C16.1859 59.2079 12.5682 59.2416 33.7201 59.0431Z" fill="#B4FFC6" />
                    <path d="M23.8449 63.6703V72L13.9697 67.5574V59.2277L15.5277 59.2138L23.8449 63.6703Z" fill="#79E9B3" />
                    <path d="M23.8454 54.785L33.7203 59.0425L23.8454 63.6702L13.9707 59.2276L23.8454 54.785Z" fill="#14D2AA" />
                    <path
                        d="M59.4186 5.80966L44.5345 25.0495L30.5984 30.766L19.4989 46.6153L5.81583 52.1736C5.81583 52.1736 -0.381752 54.4733 0.018593 49.2129L13.7017 43.6547L24.8012 27.8053L38.7372 22.0888L53.6213 2.84897L59.4186 5.80966Z"
                        fill="#FFA585"
                    />
                    <path
                        d="M48.608 3.99881L58.6177 0.125238C59.6283 -0.265951 60.8152 0.306014 60.8152 1.18429V9.29081C60.8152 10.1879 59.5818 10.7584 58.5688 10.33L48.5589 6.09705C47.4779 5.63991 47.5063 4.42522 48.608 3.99881Z"
                        fill="#EF7C85"
                    />
                    <path d="M44.5341 25.0495L30.598 30.766L24.8008 27.8054L38.7368 22.0888L44.5341 25.0495Z" fill="#EF7C85" />
                    <path d="M19.4989 46.6154L5.81583 52.1736C5.81583 52.1736 -0.381752 54.4733 0.018593 49.2129L13.7017 43.6547L19.4989 46.6154Z" fill="#EF7C85" />
                </g>
                <defs>
                    <clipPath id="clip0_20059_31193">
                        <rect width="72" height="72" fill="white" />
                    </clipPath>
                </defs>
            </svg>
        </div>
    );
};
export const BootEventIcon = ({ className = 'w-6 h-6' }: any) => {
    return (
        <div data-svg-wrapper className="relative">
            <svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_20059_31256)">
                    <path
                        d="M27.8528 65.4224C27.4287 65.8296 26.8999 66.1396 26.2928 66.3055L20.7169 67.8303C18.6113 68.4063 16.38 67.8026 14.894 66.255L11.9778 63.2177C10.4918 61.6699 9.97934 59.416 10.6404 57.3353L12.4444 51.4071L5.22875 53.2722C3.28855 53.8028 1.28548 52.6601 0.754906 50.7199C0.224328 48.7797 1.36705 46.7768 3.30725 46.246L8.88317 44.7212C10.989 44.1454 13.2203 44.7491 14.7062 46.2965L17.6225 49.3339C19.1084 50.8815 19.6209 53.1354 18.9599 55.2161L16.8733 61.1995L24.3715 59.2794C26.3117 58.7489 28.3147 59.8916 28.8453 61.8318C29.21 63.1649 28.7843 64.528 27.8528 65.4224Z"
                        fill="#81B0FE"
                    />
                    <path
                        d="M12.4452 51.4073L12.2552 52.0316L10.6411 57.3356C10.073 59.1239 10.3724 61.0398 11.4111 62.5256C13.0586 62.1489 15.2498 61.6718 16.8742 61.1996L17.1417 60.4326L18.9608 55.2161C19.5558 53.343 19.1993 51.3296 18.0382 49.8175C16.3775 50.311 14.1127 50.9397 12.4452 51.4073Z"
                        fill="#6396FF"
                    />
                    <path
                        d="M51.6959 66.2429C52.2414 66.4624 52.8476 66.5526 53.4727 66.48L59.2148 65.8133C61.3833 65.5616 63.228 64.1688 64.0288 62.1783L65.6006 58.2719C66.4015 56.2813 66.0356 53.999 64.6456 52.3156L60.7592 47.4892L68.1493 46.526C70.1474 46.294 71.579 44.4863 71.3471 42.4881C71.1151 40.4901 69.3073 39.0586 67.3092 39.2903L61.5671 39.957C59.3983 40.2089 57.5538 41.6018 56.7531 43.592L55.1813 47.4985C54.3804 49.4889 54.7463 51.7711 56.1363 53.4546L60.3055 58.2267L52.6326 59.2443C50.6346 59.4763 49.2029 61.2841 49.4348 63.2822C49.5943 64.6553 50.4979 65.7609 51.6959 66.2429Z"
                        fill="#A183E2"
                    />
                    <path
                        d="M60.7594 47.4892L61.1686 47.9976L64.6459 52.3156C65.8405 53.7625 66.278 55.6518 65.8689 57.4181C64.1997 57.6836 61.9888 58.0587 60.3056 58.2271L59.7713 57.6154L56.1364 53.4548C54.8851 51.9392 54.4643 49.9384 54.9771 48.1021C56.702 47.9401 59.0376 47.6778 60.7594 47.4892Z"
                        fill="#9762E2"
                    />
                    <path
                        d="M35.4692 48.6647C36.0007 48.4135 36.4789 48.0299 36.8503 47.5218L40.2618 42.8552C41.5502 41.093 41.7814 38.7931 40.8648 36.8531L39.0661 33.046C38.1495 31.1061 36.226 29.8241 34.0463 29.7004L27.8645 29.2736L32.1783 23.1962C33.3654 21.5724 33.0113 19.2937 31.3877 18.1066C29.7639 16.9194 27.4852 17.2735 26.2979 18.8972L22.8863 23.5638C21.5979 25.3263 21.3669 27.6262 22.2833 29.5658L24.0821 33.373C24.9987 35.3127 26.922 36.5947 29.1015 36.7186L35.4357 36.9011L30.9699 43.2228C29.7827 44.8466 30.1368 47.1252 31.7605 48.3124C32.8765 49.1283 34.3017 49.2163 35.4692 48.6647Z"
                        fill="#FFA585"
                    />
                    <path
                        d="M27.5217 29.7884L27.8652 29.2736L34.0471 29.7004C35.9205 29.8067 37.6039 30.7693 38.6222 32.2692C37.7872 33.5188 36.904 34.7352 36.0014 35.9381C35.7772 36.2368 35.5872 36.5595 35.4365 36.9011L29.1023 36.7186C27.14 36.607 25.3859 35.5566 24.3867 33.9329C25.4397 32.5572 26.4905 31.18 27.5217 29.7884Z"
                        fill="#EF7C85"
                    />
                    <path
                        d="M9.54032 37.6294L6.48665 36.1025C5.57568 35.647 5.2064 34.5392 5.66188 33.6281L7.18879 30.5744C7.64428 29.6634 8.75212 29.2941 9.66309 29.7496L12.7168 31.2765C13.6277 31.732 13.997 32.8399 13.5415 33.751L12.0146 36.8046C11.5591 37.7156 10.4514 38.0849 9.54032 37.6294Z"
                        fill="#FFE266"
                    />
                    <path
                        d="M35.1252 65.0991L38.4081 64.1613C39.3876 63.8814 40.4082 64.4486 40.6881 65.428L41.6259 68.7109C41.9058 69.6903 41.3386 70.711 40.3592 70.9908L37.0763 71.9287C36.0968 72.2085 35.0762 71.6414 34.7963 70.6619L33.8585 67.379C33.5788 66.3996 34.1459 65.3789 35.1252 65.0991Z"
                        fill="#EF8BBD"
                    />
                    <path
                        d="M58.5361 30.841L54.2254 33.3298C52.9394 34.0723 51.2949 33.6317 50.5526 32.3457L48.0638 28.035C47.3213 26.749 47.762 25.1045 49.0479 24.3621L53.3586 21.8734C54.6446 21.1309 56.2889 21.5714 57.0314 22.8575L59.5202 27.168C60.2627 28.4542 59.8222 30.0985 58.5361 30.841Z"
                        fill="#78EAC4"
                    />
                    <path
                        d="M12.7697 6.9443L13.9576 10.2846C14.0085 10.4276 14.1092 10.5409 14.2362 10.5982L17.2038 11.9352C17.623 12.1241 17.623 12.7922 17.2038 12.9811L14.2362 14.3181C14.1092 14.3753 14.0084 14.4887 13.9576 14.6317L12.7697 17.9718C12.602 18.4438 12.0084 18.4438 11.8406 17.9718L10.6526 14.6316C10.6017 14.4886 10.501 14.3752 10.3741 14.318L7.4063 12.9811C6.98696 12.7922 6.98696 12.1242 7.4063 11.9352L10.3739 10.5982C10.501 10.5409 10.6017 10.4276 10.6525 10.2846L11.8405 6.9443C12.0084 6.4725 12.6018 6.4725 12.7697 6.9443Z"
                        fill="#CAB4EF"
                    />
                    <path
                        d="M57.1179 0.262512L57.999 2.74018C58.0367 2.84621 58.1114 2.93031 58.2057 2.97278L60.4071 3.96461C60.7181 4.10481 60.7181 4.60023 60.4071 4.74043L58.2057 5.73226C58.1115 5.77473 58.0368 5.85882 57.999 5.96486L57.1179 8.44253C56.9934 8.79254 56.5531 8.79254 56.4287 8.44253L55.5475 5.96486C55.5098 5.85882 55.4351 5.77473 55.3408 5.73226L53.1394 4.74043C52.8285 4.60023 52.8285 4.10481 53.1394 3.96461L55.3408 2.97278C55.435 2.93031 55.5097 2.84621 55.5475 2.74018L56.4287 0.262512C56.5531 -0.0875039 56.9933 -0.0875039 57.1179 0.262512Z"
                        fill="#FFC7B6"
                    />
                    <path
                        d="M45.0164 15.515C46.34 14.9663 46.9683 13.4485 46.4196 12.1248C45.871 10.8012 44.3532 10.1729 43.0295 10.7216C41.7059 11.2702 41.0776 12.788 41.6262 14.1117C42.1749 15.4354 43.6927 16.0636 45.0164 15.515Z"
                        fill="#ACCDFF"
                    />
                    <path
                        d="M20.6092 6.59035C21.2272 6.59035 21.7281 6.08938 21.7281 5.4714C21.7281 4.85342 21.2272 4.35245 20.6092 4.35245C19.9912 4.35245 19.4902 4.85342 19.4902 5.4714C19.4902 6.08938 19.9912 6.59035 20.6092 6.59035Z"
                        fill="#EF8BBD"
                    />
                    <path
                        d="M66.7713 23.6908C67.3893 23.6908 67.8903 23.1898 67.8903 22.5718C67.8903 21.9539 67.3893 21.4529 66.7713 21.4529C66.1533 21.4529 65.6523 21.9539 65.6523 22.5718C65.6523 23.1898 66.1533 23.6908 66.7713 23.6908Z"
                        fill="#FFE266"
                    />
                </g>
                <defs>
                    <clipPath id="clip0_20059_31256">
                        <rect width="72" height="72" fill="white" />
                    </clipPath>
                </defs>
            </svg>
        </div>
    );
};
export const BootAddBannerIcon = ({ className = 'w-6 h-6' }: any) => {
    return (
        <div data-svg-wrapper className="relative">
            <svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_20021_13428)">
                    <path
                        d="M60.2718 66.429L5.65382 60.3756C3.02779 60.0847 1.13504 57.72 1.426 55.0939L6.4461 9.79874C6.73721 7.17272 9.10191 5.27982 11.7278 5.57093L66.3457 11.6243C68.9718 11.9154 70.8646 14.2801 70.5735 16.906L65.5534 62.2012C65.2625 64.8272 62.8978 66.7201 60.2718 66.429Z"
                        fill="url(#paint0_linear_20021_13428)"
                    />
                    <path d="M70.5739 16.9061L68.5947 34.7697L64.1953 11.3861L66.347 11.6244C68.9722 11.915 70.8646 14.2794 70.5739 16.9061Z" fill="url(#paint1_linear_20021_13428)" />
                    <path
                        d="M68.0998 58.015L14.0947 68.1747C11.4982 68.6631 8.99722 66.9542 8.50874 64.3577L0.0833322 19.5707C-0.405138 16.9742 1.3038 14.4732 3.90032 13.9848L57.9054 3.82515C60.5019 3.33668 63.0029 5.04562 63.4913 7.64214L71.9167 52.4291C72.4052 55.0258 70.6963 57.5267 68.0998 58.015Z"
                        fill="url(#paint2_linear_20021_13428)"
                    />
                    <path d="M64.6445 13.7711V58.6651L68.1 58.015C70.6965 57.5265 72.4054 55.0256 71.917 52.4291L64.6445 13.7711Z" fill="url(#paint3_linear_20021_13428)" />
                    <path d="M8.25195 62.9911L8.50904 64.3577C8.99751 66.9542 11.4984 68.6631 14.095 68.1747L41.6491 62.9911H8.25195Z" fill="url(#paint4_linear_20021_13428)" />
                    <path
                        d="M59.8602 63.2704H4.90793C2.26581 63.2704 0.124023 61.1286 0.124023 58.4865V12.9139C0.124023 10.2718 2.26581 8.13 4.90793 8.13H59.8602C62.5023 8.13 64.6441 10.2718 64.6441 12.9139V58.4865C64.6441 61.1286 62.5023 63.2704 59.8602 63.2704Z"
                        fill="url(#paint5_linear_20021_13428)"
                    />
                    <path
                        d="M55.3805 58.7754H9.3877C7.17645 58.7754 5.38379 56.9828 5.38379 54.7715V16.6292C5.38379 14.4179 7.17645 12.6252 9.3877 12.6252H55.3805C57.5917 12.6252 59.3844 14.4179 59.3844 16.6292V54.7715C59.3844 56.9828 57.5917 58.7754 55.3805 58.7754Z"
                        fill="url(#paint6_linear_20021_13428)"
                    />
                    <path
                        d="M35.5857 58.7753H9.38799C7.17632 58.7753 5.38281 56.9832 5.38281 54.7717V48.519L12.0297 40.4397C14.2233 37.7711 18.3064 37.7711 20.5015 40.4397L24.6401 45.4713L35.5857 58.7753Z"
                        fill="url(#paint7_linear_20021_13428)"
                    />
                    <path
                        d="M59.3844 44.7175V54.7717C59.3844 56.9834 57.5909 58.7753 55.3792 58.7753H13.6943L24.64 45.4713L38.0865 29.1255C40.2801 26.4583 44.3648 26.4583 46.5584 29.1255L59.3844 44.7175Z"
                        fill="url(#paint8_linear_20021_13428)"
                    />
                    <path
                        d="M19.3733 27.9724C22.5091 27.9724 25.0513 25.4303 25.0513 22.2944C25.0513 19.1586 22.5091 16.6165 19.3733 16.6165C16.2374 16.6165 13.6953 19.1586 13.6953 22.2944C13.6953 25.4303 16.2374 27.9724 19.3733 27.9724Z"
                        fill="url(#paint9_linear_20021_13428)"
                    />
                </g>
                <defs>
                    <linearGradient id="paint0_linear_20021_13428" x1="18.3969" y1="14.0007" x2="59.0029" y2="64.7122" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FFA936" />
                        <stop offset="0.4112" stop-color="#FF8548" />
                        <stop offset="0.7781" stop-color="#FF6C54" />
                        <stop offset="1" stop-color="#FF6359" />
                    </linearGradient>
                    <linearGradient id="paint1_linear_20021_13428" x1="69.2455" y1="22.4491" x2="65.8492" y2="23.1983" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#F82814" stop-opacity="0" />
                        <stop offset="1" stop-color="#C0272D" />
                    </linearGradient>
                    <linearGradient id="paint2_linear_20021_13428" x1="12.739" y1="20.1067" x2="66.3903" y2="56.7623" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#CDEC7A" />
                        <stop offset="0.2157" stop-color="#B0E995" />
                        <stop offset="0.5613" stop-color="#87E4BB" />
                        <stop offset="0.8347" stop-color="#6EE1D2" />
                        <stop offset="1" stop-color="#65E0DB" />
                    </linearGradient>
                    <linearGradient id="paint3_linear_20021_13428" x1="69.4143" y1="36.2182" x2="63.6197" y2="36.2182" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#CDEC7A" stop-opacity="0" />
                        <stop offset="0.2354" stop-color="#9AD57D" stop-opacity="0.235" />
                        <stop offset="0.6035" stop-color="#51B482" stop-opacity="0.604" />
                        <stop offset="0.8679" stop-color="#239F85" stop-opacity="0.868" />
                        <stop offset="1" stop-color="#119786" />
                    </linearGradient>
                    <linearGradient id="paint4_linear_20021_13428" x1="24.9504" y1="65.9177" x2="24.9504" y2="62.4848" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#CDEC7A" stop-opacity="0" />
                        <stop offset="0.2354" stop-color="#9AD57D" stop-opacity="0.235" />
                        <stop offset="0.6035" stop-color="#51B482" stop-opacity="0.604" />
                        <stop offset="0.8679" stop-color="#239F85" stop-opacity="0.868" />
                        <stop offset="1" stop-color="#119786" />
                    </linearGradient>
                    <linearGradient id="paint5_linear_20021_13428" x1="12.4607" y1="15.7768" x2="58.395" y2="61.7111" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#F8F6FB" />
                        <stop offset="1" stop-color="#EFDCFB" />
                    </linearGradient>
                    <linearGradient id="paint6_linear_20021_13428" x1="15.9201" y1="14.2807" x2="60.7216" y2="72.5677" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#18CEFB" />
                        <stop offset="0.2969" stop-color="#2BB9F9" />
                        <stop offset="0.7345" stop-color="#42A0F7" />
                        <stop offset="1" stop-color="#4A97F6" />
                    </linearGradient>
                    <linearGradient id="paint7_linear_20021_13428" x1="10.6707" y1="44.6865" x2="30.2982" y2="70.2219" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#CDEC7A" />
                        <stop offset="0.2154" stop-color="#B0E995" stop-opacity="0.784" />
                        <stop offset="0.5604" stop-color="#87E4BB" stop-opacity="0.439" />
                        <stop offset="0.8334" stop-color="#6EE1D2" stop-opacity="0.165" />
                        <stop offset="0.9985" stop-color="#65E0DB" stop-opacity="0" />
                    </linearGradient>
                    <linearGradient id="paint8_linear_20021_13428" x1="28.0685" y1="40.7256" x2="51.7405" y2="71.523" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#CDEC7A" />
                        <stop offset="0.2157" stop-color="#B0E995" />
                        <stop offset="0.5613" stop-color="#87E4BB" />
                        <stop offset="0.8347" stop-color="#6EE1D2" />
                        <stop offset="1" stop-color="#65E0DB" />
                    </linearGradient>
                    <linearGradient id="paint9_linear_20021_13428" x1="16.5522" y1="18.6243" x2="24.2288" y2="28.6117" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FFD945" />
                        <stop offset="0.3043" stop-color="#FFCD3E" />
                        <stop offset="0.8558" stop-color="#FFAD2B" />
                        <stop offset="1" stop-color="#FFA325" />
                    </linearGradient>
                    <clipPath id="clip0_20021_13428">
                        <rect width="72" height="72" fill="white" />
                    </clipPath>
                </defs>
            </svg>
        </div>
    );
};

export const NeedAssistanceIcon = () => {
    return (
        <>
            <div data-svg-wrapper className="relative">
                <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M15 2.08333C8.1 2.08333 2.5 7.68333 2.5 14.5833C2.5 21.4833 8.1 27.0833 15 27.0833C21.9 27.0833 27.5 21.4833 27.5 14.5833C27.5 7.68333 21.9 2.08333 15 2.08333ZM16.25 20.8333H13.75V13.3333H16.25V20.8333ZM16.25 10.8333H13.75V8.33333H16.25V10.8333Z"
                        fill="#993333"
                    />
                </svg>
            </div>
        </>
    );
};
export const CopyIcon = () => {
    return (
        <>
            <div data-svg-wrapper className="relative">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M5 9.16663C5 6.80996 5 5.63079 5.7325 4.89913C6.46417 4.16663 7.64333 4.16663 10 4.16663H12.5C14.8567 4.16663 16.0358 4.16663 16.7675 4.89913C17.5 5.63079 17.5 6.80996 17.5 9.16663V13.3333C17.5 15.69 17.5 16.8691 16.7675 17.6008C16.0358 18.3333 14.8567 18.3333 12.5 18.3333H10C7.64333 18.3333 6.46417 18.3333 5.7325 17.6008C5 16.8691 5 15.69 5 13.3333V9.16663Z"
                        stroke="#636363"
                        stroke-width="1.5"
                    />
                    <path
                        d="M5 15.8333C4.33696 15.8333 3.70107 15.5699 3.23223 15.1011C2.76339 14.6322 2.5 13.9963 2.5 13.3333V8.33329C2.5 5.19079 2.5 3.61913 3.47667 2.64329C4.45333 1.66746 6.02417 1.66663 9.16667 1.66663H12.5C13.163 1.66663 13.7989 1.93002 14.2678 2.39886C14.7366 2.8677 15 3.50358 15 4.16663"
                        stroke="#636363"
                        stroke-width="1.5"
                    />
                </svg>
            </div>
        </>
    );
};
export const DateIcon = () => {
    return (
        <>
            <div className="relative h-[19.50px] w-[18px]">
                <div className="absolute left-0 top-[1.50px] h-[18px] w-[18px] rounded-[5px] border border-[#2d2d2e]" />
                <div data-svg-wrapper className="absolute left-0 top-[6px]">
                    <svg width="18" height="3" viewBox="0 0 18 3" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 1.5H18" stroke="#2D2D2E" stroke-width="1.5" stroke-linejoin="round" />
                    </svg>
                </div>
                <div data-svg-wrapper className="absolute left-[13px] top-0">
                    <svg width="3" height="5" viewBox="0 0 3 5" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1.5 1L1.5 4" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </div>
                <div data-svg-wrapper className="absolute left-[4px] top-0">
                    <svg width="3" height="5" viewBox="0 0 3 5" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1.5 1L1.5 4" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </div>
                <div data-svg-wrapper className="absolute left-[3.50px] top-[10px]">
                    <svg width="14" height="7" viewBox="0 0 14 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1.5 1.5H2.5" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M6.5 1.5H7.5" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M11.5 1.5H12.5" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M1.5 5.5H2.5" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M6.5 5.5H7.5" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M11.5 5.5H12.5" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </div>
            </div>
        </>
    );
};
export const AdvertisingChargesChartIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" fill="none">
                <path
                    d="M1.18749 5.9375V19C1.18749 19.9448 1.56283 20.851 2.23093 21.5191C2.89902 22.1872 3.80516 22.5625 4.74999 22.5625H10.6875C11.0024 22.5625 11.3045 22.4374 11.5272 22.2147C11.7499 21.992 11.875 21.6899 11.875 21.375V7.125C11.8767 5.9682 11.5405 4.83609 10.9076 3.86777C10.2747 2.89945 9.37272 2.13709 8.31249 1.67437H8.25312C7.52115 1.3602 6.73401 1.1947 5.93749 1.1875C4.67772 1.1875 3.46954 1.68795 2.57874 2.57874C1.68794 3.46954 1.18749 4.67772 1.18749 5.9375Z"
                    fill="#C19A00"
                />
                <path
                    d="M5.9374 1.1875C7.1851 1.55698 8.29843 2.2813 9.14179 3.27226C9.98516 4.26321 10.5222 5.47803 10.6874 6.76875V7.125V32.9412C10.6649 33.4619 10.7726 33.98 11.0006 34.4486C11.2287 34.9171 11.57 35.3215 11.9937 35.625C12.5154 35.9576 13.1283 36.1182 13.7461 36.0841C14.3639 36.05 14.9554 35.823 15.4374 35.435C15.7445 35.1158 16.1136 34.8627 16.522 34.6912C16.9304 34.5197 17.3695 34.4333 17.8124 34.4375C18.2277 34.4338 18.6395 34.5139 19.0231 34.6731C19.4067 34.8322 19.7542 35.0671 20.0449 35.3637C21.0591 36.2942 22.3854 36.8104 23.7618 36.8104C25.1381 36.8104 26.4644 36.2942 27.4787 35.3637C27.7645 35.0673 28.1078 34.8322 28.4876 34.673C28.8673 34.5137 29.2756 34.4336 29.6874 34.4375C30.0968 34.4352 30.5027 34.5137 30.8817 34.6686C31.2608 34.8235 31.6055 35.0516 31.8962 35.34L32.003 35.435C32.4368 35.7833 32.9604 36.0016 33.5131 36.0646C34.0658 36.1277 34.6251 36.0329 35.1261 35.7912C35.8286 35.452 36.37 34.8508 36.6343 34.1169C36.7549 33.7942 36.8153 33.452 36.8124 33.1075V8.3125C36.8124 6.42283 36.0617 4.61056 34.7255 3.27436C33.3893 1.93817 31.5771 1.1875 29.6874 1.1875H5.9374Z"
                    fill="#FFCC00"
                />
                <path
                    d="M15.6611 14.2503C15.6611 14.644 15.8175 15.0215 16.0959 15.2999C16.3743 15.5783 16.7518 15.7347 17.1455 15.7347H30.208C30.5504 15.7379 30.883 15.6208 31.1478 15.4037C31.4126 15.1867 31.5928 14.8835 31.6568 14.5472C31.6691 14.4605 31.6691 14.3726 31.6568 14.2859C31.6615 14.091 31.6278 13.897 31.5575 13.7151C31.4873 13.5332 31.3819 13.3669 31.2473 13.2257C31.1128 13.0846 30.9517 12.9713 30.7734 12.8924C30.5951 12.8135 30.403 12.7705 30.208 12.7659H17.1455C17.0589 12.7536 16.9709 12.7536 16.8843 12.7659C16.5352 12.8233 16.2188 13.0054 15.9939 13.2784C15.7689 13.5514 15.6507 13.8967 15.6611 14.2503Z"
                    fill="#C19A00"
                />
                <path
                    d="M16.0576 21.3753C16.0576 21.769 16.214 22.1465 16.4924 22.4249C16.7708 22.7033 17.1483 22.8597 17.542 22.8597H27.042C27.3844 22.8629 27.717 22.7458 27.9818 22.5287C28.2466 22.3117 28.4267 22.0085 28.4907 21.6722C28.5031 21.5855 28.5031 21.4976 28.4907 21.4109C28.4955 21.216 28.4618 21.022 28.3915 20.8401C28.3212 20.6582 28.2158 20.4919 28.0813 20.3507C27.9468 20.2096 27.7857 20.0963 27.6074 20.0174C27.4291 19.9385 27.2369 19.8955 27.042 19.8909H17.542C17.4553 19.8786 17.3674 19.8786 17.2807 19.8909C16.9317 19.9483 16.6153 20.1304 16.3904 20.4034C16.1654 20.6764 16.0472 21.0217 16.0576 21.3753Z"
                    fill="#C19A00"
                />
            </svg>
        </>
    );
};
export const ChatInititatedIcon = () => {
    return (
        <div>
            <div data-svg-wrapper className="relative">
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M28.6673 26.574L32.5513 29.1633C33.2153 29.59 34.096 29.1707 34.1833 28.386L34.5067 25.3333H36.6673C38.14 25.3333 39.334 24.1393 39.334 22.6667V6.66667C39.334 5.194 38.14 4 36.6673 4H14.0007C12.528 4 11.334 5.194 11.334 6.66667V10.6667L28.6673 26.574Z"
                        fill="#FECD3D"
                    />
                    <path
                        d="M28.666 13.3333V29.3333C28.666 30.806 27.472 32 25.9993 32H13.1933L7.44868 35.83C6.78468 36.2566 5.90402 35.8373 5.81668 35.0526L5.49335 32H3.33268C1.86002 32 0.666016 30.806 0.666016 29.3333V13.3333C0.666016 11.8606 1.86002 10.6666 3.33268 10.6666H25.9993C27.472 10.6666 28.666 11.8606 28.666 13.3333Z"
                        fill="#E23750"
                    />
                    <path
                        d="M12.6673 18.6667C13.036 18.6667 13.334 18.3687 13.334 18C13.334 17.6314 13.036 17.3334 12.6673 17.3334H10.0007C9.63198 17.3334 9.33398 17.6314 9.33398 18C9.33398 18.3687 9.63198 18.6667 10.0007 18.6667H10.6673V24H10.0007C9.63198 24 9.33398 24.298 9.33398 24.6667C9.33398 25.0354 9.63198 25.3334 10.0007 25.3334H12.6673C13.036 25.3334 13.334 25.0354 13.334 24.6667C13.334 24.298 13.036 24 12.6673 24H12.0007V18.6667H12.6673Z"
                        fill="white"
                    />
                    <path
                        d="M7.33333 24H5.33333V18C5.33333 17.6314 5.03533 17.3334 4.66667 17.3334C4.298 17.3334 4 17.6314 4 18V24.6667C4 25.0354 4.298 25.3334 4.66667 25.3334H7.33333C7.702 25.3334 8 25.0354 8 24.6667C8 24.298 7.702 24 7.33333 24Z"
                        fill="white"
                    />
                    <path
                        d="M24.6673 24H22.6673V22H23.334C23.7027 22 24.0007 21.702 24.0007 21.3334C24.0007 20.9647 23.7027 20.6667 23.334 20.6667H22.6673V18.6667H24.6673C25.036 18.6667 25.334 18.3687 25.334 18C25.334 17.6314 25.036 17.3334 24.6673 17.3334H22.0007C21.632 17.3334 21.334 17.6314 21.334 18V24.6667C21.334 25.0354 21.632 25.3334 22.0007 25.3334H24.6673C25.036 25.3334 25.334 25.0354 25.334 24.6667C25.334 24.298 25.036 24 24.6673 24Z"
                        fill="white"
                    />
                    <path
                        d="M19.5241 17.3613C19.1701 17.256 18.7994 17.456 18.6941 17.8087L17.3328 22.3467L15.9714 17.8087C15.8654 17.456 15.4941 17.256 15.1414 17.3613C14.7888 17.4673 14.5888 17.8393 14.6941 18.1913L16.6941 24.858C16.7788 25.14 17.0388 25.3333 17.3328 25.3333C17.6268 25.3333 17.8868 25.14 17.9714 24.858L19.9714 18.1913C20.0768 17.8393 19.8768 17.4673 19.5241 17.3613Z"
                        fill="white"
                    />
                </svg>
            </div>
        </div>
    );
};
export const TotalSpendIcon = () => {
    return (
        <div>
            <div data-svg-wrapper className="relative">
                <svg width="40" height="41" viewBox="0 0 40 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M20.255 15.6214C19.2529 13.8853 22.3864 10.202 27.2536 7.3912C32.1185 4.58268 36.8767 3.7101 37.8787 5.44612C38.4332 6.40729 38.9876 7.36846 39.5443 8.3319C40.5464 10.0656 37.4129 13.749 32.5457 16.5598C27.6785 19.3683 22.9226 20.2409 21.9205 18.5071C21.3662 17.5437 20.8094 16.5825 20.255 15.6214Z"
                        fill="#FE9923"
                    />
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M27.2536 7.3912C32.1185 4.58268 36.8767 3.7101 37.8787 5.44612C38.8809 7.17987 35.7473 10.8632 30.8801 13.674C26.013 16.4848 21.2571 17.3551 20.255 15.6214C19.2529 13.8853 22.3864 10.202 27.2536 7.3912Z"
                        fill="#FECD3D"
                    />
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M28.1017 8.85906C31.9737 6.62539 35.5434 5.56203 36.0774 6.48453C36.6114 7.40937 33.9052 9.97023 30.0331 12.2061C26.1612 14.442 22.5891 15.5055 22.0574 14.5806C21.5235 13.6559 24.2298 11.095 28.1017 8.85906Z"
                        fill="#FEA832"
                    />
                    <path
                        d="M37.5156 10.3179L38.4018 11.8539C38.6131 11.5949 38.804 11.3404 38.9676 11.0904L38.2677 9.88159L37.5156 10.3179ZM36.7566 13.5536C36.543 13.7445 36.318 13.9376 36.0863 14.1285L35.241 12.6629L35.9931 12.2312L36.7566 13.5536ZM34.248 15.4942C34.0117 15.6555 33.7686 15.8146 33.5209 15.9714L32.7143 14.5739L33.4664 14.1422L34.248 15.4942ZM31.5509 17.1075C31.2895 17.246 31.0305 17.3756 30.7738 17.5028L29.9898 16.1485L30.7441 15.7123L31.5509 17.1075ZM28.6719 18.4095C28.3879 18.5163 28.1106 18.614 27.838 18.7026L27.0722 17.3802L27.8266 16.9462L28.6719 18.4095ZM25.543 19.2775C25.2135 19.332 24.8976 19.3684 24.5999 19.3865L23.9023 18.1777L24.6567 17.7414L25.543 19.2775Z"
                        fill="#C85929"
                    />
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M19.515 23.1539C20.517 21.4202 17.3836 17.7346 12.5164 14.9261C7.65148 12.1153 2.89327 11.245 1.89124 12.9787C1.33679 13.9399 0.782413 14.9033 0.225694 15.8645C-0.776416 17.5982 2.3571 21.2839 7.22429 24.0924C12.0915 26.9032 16.8474 27.7735 17.8494 26.0397C18.4061 25.0786 18.9605 24.1151 19.515 23.1539Z"
                        fill="#FE9923"
                    />
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M12.5161 14.9261C7.65117 12.1153 2.89297 11.245 1.89094 12.9787C0.891175 14.7147 4.02461 18.3981 8.88953 21.2066C13.7567 24.0174 18.5149 24.8877 19.5147 23.1539C20.5168 21.4202 17.3833 17.7346 12.5161 14.9261Z"
                        fill="#FECD3D"
                    />
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M11.6679 16.3939C7.79822 14.1579 4.22619 13.0946 3.69221 14.0194C3.15822 14.9419 5.86447 17.5051 9.73643 19.7386C13.6084 21.9746 17.1804 23.0379 17.7144 22.1132C18.2461 21.1907 15.5399 18.6298 11.6679 16.3939Z"
                        fill="#FEA832"
                    />
                    <path
                        d="M2.25437 17.8505L1.3682 19.3865C1.15687 19.1275 0.968281 18.873 0.804688 18.6253L1.50227 17.4142L2.25437 17.8505ZM3.01328 21.0862C3.22687 21.2771 3.4518 21.4703 3.68359 21.6611L4.52883 20.1978L3.77672 19.7638L3.01328 21.0862ZM5.52187 23.029C5.7582 23.1881 6.00133 23.3472 6.25125 23.504L7.05789 22.1065L6.30352 21.6748L5.52187 23.029ZM8.21906 24.6423C8.48039 24.7786 8.73945 24.9104 8.99851 25.0354L9.78016 23.6811L9.02578 23.2449L8.21906 24.6423ZM11.1003 25.9421C11.3821 26.0489 11.6593 26.1466 11.9342 26.2352L12.6977 24.9128L11.9434 24.4811L11.1003 25.9421ZM14.227 26.8123C14.5587 26.8646 14.8723 26.9009 15.1699 26.9191L15.8675 25.7103L15.1154 25.274L14.227 26.8123Z"
                        fill="#C85929"
                    />
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M18.8129 32.6679C17.8131 30.9342 20.9443 27.2508 25.8115 24.44C30.6787 21.6315 35.4347 20.7589 36.4367 22.4927C36.9934 23.4561 37.5479 24.4173 38.1022 25.3785C39.1043 27.1145 35.9708 30.7979 31.1059 33.6086C26.2387 36.4171 21.4805 37.2897 20.4785 35.5537C19.9241 34.5925 19.3697 33.6314 18.8129 32.6679Z"
                        fill="#FE9923"
                    />
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M25.8115 24.44C30.6787 21.6315 35.4347 20.7589 36.4367 22.4927C37.4388 24.2287 34.3053 27.9121 29.4381 30.7229C24.5732 33.5314 19.815 34.4039 18.8129 32.6679C17.8131 30.9343 20.9443 27.2509 25.8115 24.44Z"
                        fill="#FECD3D"
                    />
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M26.6584 25.908C30.5303 23.672 34.1023 22.6109 34.6341 23.5335C35.1681 24.4582 32.4618 27.0191 28.5898 29.255C24.7178 31.4887 21.148 32.552 20.6141 31.6295C20.0802 30.7047 22.7864 28.1438 26.6584 25.908Z"
                        fill="#FEA832"
                    />
                    <path
                        d="M36.0739 27.3668L36.9623 28.9029C37.1737 28.6438 37.3623 28.3871 37.5259 28.1394L36.8283 26.9305L36.0739 27.3668ZM35.3172 30.6002C35.1013 30.7934 34.8787 30.9842 34.6446 31.1752L33.7993 29.7118L34.5537 29.2801L35.3172 30.6002ZM32.8086 32.543C32.57 32.7044 32.3269 32.8634 32.0792 33.0202L31.2726 31.6228L32.027 31.1888L32.8086 32.543ZM30.1091 34.1563C29.8501 34.2927 29.5888 34.4244 29.332 34.5494L28.5503 33.1952L29.3024 32.7589L30.1091 34.1563ZM27.2302 35.4584C26.9484 35.5629 26.6689 35.6605 26.3962 35.7515L25.6327 34.4267L26.3848 33.995L27.2302 35.4584ZM24.1035 36.3264C23.7717 36.3809 23.4559 36.415 23.1605 36.4332L22.4629 35.2244L23.215 34.7881L24.1035 36.3264Z"
                        fill="#C85929"
                    />
                    <path d="M16.8611 8.625V9.91797H18.154V11.0405H16.8611V12.3356H15.7386V11.0405H14.4434V9.91797H15.7386V8.625H16.8611Z" fill="#FEA832" />
                    <path d="M12.8627 29.3641V30.6571H14.1578V31.7795H12.8627V33.0748H11.7402V31.7795H10.4473V30.6571H11.7402V29.3641H12.8627Z" fill="#FEA832" />
                    <path d="M38.6655 16.6825V17.9754H39.9606V19.098H38.6655V20.3931H37.5429V19.098H36.25V17.9754H37.5429V16.6825H38.6655Z" fill="#FEA832" />
                </svg>
            </div>
        </div>
    );
};
export const WhatAppIcon = () => {
    return (
        <div>
            <div data-svg-wrapper className="relative">
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M20.5039 5H20.4961C11.9498 5 5 11.9517 5 20.5C5 23.8906 6.09275 27.0333 7.95081 29.5849L6.01912 35.3432L11.9769 33.4386C14.4279 35.0623 17.3516 36 20.5039 36C29.0502 36 36 29.0463 36 20.5C36 11.9537 29.0502 5 20.5039 5Z"
                        fill="#4CAF50"
                    />
                    <path
                        d="M28.2303 25.5856C27.9037 26.4442 26.6073 27.1564 25.5732 27.3643C24.8658 27.5046 23.9417 27.6164 20.8311 26.4159C16.8522 24.8813 14.2899 21.1174 14.0902 20.8732C13.899 20.629 12.4824 18.8802 12.4824 17.0715C12.4824 15.2628 13.4691 14.3821 13.8668 14.0039C14.1934 13.6936 14.7333 13.5518 15.2512 13.5518C15.4188 13.5518 15.5694 13.5596 15.7048 13.5659C16.1025 13.5817 16.3022 13.6038 16.5645 14.1883C16.8912 14.9209 17.6866 16.7296 17.7814 16.9155C17.8778 17.1014 17.9743 17.3535 17.8389 17.5977C17.712 17.8498 17.6003 17.9616 17.4006 18.1759C17.2009 18.3902 17.0113 18.554 16.8116 18.7841C16.6288 18.9841 16.4224 19.1984 16.6525 19.5687C16.8827 19.931 17.6781 21.1395 18.8493 22.11C20.3606 23.3625 21.5859 23.7627 22.0242 23.9329C22.3509 24.0589 22.7401 24.029 22.9788 23.7926C23.2817 23.4886 23.6557 22.9844 24.0365 22.4881C24.3073 22.132 24.6492 22.0879 25.008 22.214C25.3735 22.3321 27.3079 23.2223 27.7057 23.4066C28.1034 23.5925 28.3657 23.6808 28.4622 23.8367C28.5569 23.9927 28.5569 24.7253 28.2303 25.5856Z"
                        fill="#FAFAFA"
                    />
                </svg>
            </div>
        </div>
    );
};
export const EmailClickIcon = () => {
    return (
        <div>
            <div data-svg-wrapper className="relative">
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M16.75 24.25C12.6137 24.25 9.25 20.8862 9.25 16.75C9.25 12.6137 12.6137 9.25 16.75 9.25C20.5475 9.25 23 12.1937 23 16.75C23 21.3062 20.5475 24.25 16.75 24.25ZM16.75 11.75C13.9925 11.75 11.75 13.9925 11.75 16.75C11.75 19.5075 13.9925 21.75 16.75 21.75C20.135 21.75 20.5 18.2537 20.5 16.75C20.5 15.2463 20.135 11.75 16.75 11.75Z"
                        fill="#E23750"
                    />
                    <path
                        d="M17.375 30.5C9.44875 30.5 3 24.0513 3 16.125C3 8.8875 8.8875 3 16.125 3C23.3625 3 29.25 8.8875 29.25 16.125V19.875C29.25 22.2875 27.2875 24.25 24.875 24.25C22.4625 24.25 20.5 22.2875 20.5 19.875V16.75C20.5 16.06 21.06 15.5 21.75 15.5C22.44 15.5 23 16.06 23 16.75V19.875C23 20.9088 23.8412 21.75 24.875 21.75C25.9088 21.75 26.75 20.9088 26.75 19.875V16.125C26.75 10.2663 21.9837 5.5 16.125 5.5C10.2663 5.5 5.5 10.2663 5.5 16.125C5.5 22.6725 10.8275 28 17.375 28C18.065 28 18.625 28.5588 18.625 29.25C18.625 29.9412 18.065 30.5 17.375 30.5Z"
                        fill="#E23750"
                    />
                    <path
                        d="M24.25 37.5013C24.1087 37.5013 23.9663 37.4763 23.8288 37.4276C23.3313 37.2501 23 36.7788 23 36.2513V21.2513C23 20.7463 23.305 20.2888 23.7713 20.0963C24.2388 19.9001 24.775 20.0101 25.1338 20.3676L36.3838 31.6176C36.7675 32.0013 36.86 32.5888 36.6125 33.0726C36.3637 33.5563 35.835 33.8326 35.2975 33.7338L28.82 32.6663L25.215 37.0451C24.9725 37.3401 24.6163 37.5013 24.25 37.5013Z"
                        fill="#29ABE2"
                    />
                </svg>
            </div>
        </div>
    );
};
export const CallInitIcon = () => {
    return (
        <div>
            <div data-svg-wrapper className="relative">
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M9.89976 4.22856L15.6403 9.96984C15.9149 10.2436 15.946 10.6789 15.7128 10.9894C14.7775 12.2367 13.2634 14.2555 12.4389 15.3663C11.903 16.0809 11.8234 17.0443 12.2376 17.8399C12.2431 17.8501 12.2485 17.861 12.2548 17.8711C12.9085 19.0007 14.2759 21.0858 16.5974 23.4073C18.9173 25.7272 21.0009 27.0939 22.1265 27.7546C22.1374 27.7616 22.1491 27.7678 22.1616 27.7741C22.9596 28.1891 23.9261 28.1095 24.6454 27.5689L29.0153 24.2919C29.3258 24.0586 29.761 24.0898 30.0348 24.3644L35.7761 30.1049C35.922 30.2508 36.0047 30.4497 36.0047 30.6564C36.0047 30.8631 35.922 31.062 35.7761 31.2079C34.613 32.371 32.7955 34.1886 31.6582 35.3142C31.6574 35.3158 31.6566 35.3165 31.6558 35.3173C31.1511 35.8212 30.4444 36.067 29.7361 35.9843C26.7554 35.5654 19.8916 33.8562 13.02 26.9846C6.14842 20.1131 4.4393 13.2492 4.0126 10.2756C3.92757 9.56342 4.17408 8.85123 4.6819 8.3434L8.79675 4.22856C8.94262 4.08268 9.14154 4 9.34825 4C9.55497 4 9.75389 4.08268 9.89976 4.22856Z"
                        fill="#FE9923"
                    />
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M20.268 6.47818C27.5857 6.47818 33.5264 12.4189 33.5264 19.7365C33.5264 20.1672 33.8754 20.5163 34.3061 20.5163C34.7368 20.5163 35.0859 20.1672 35.0859 19.7365C35.0859 11.5581 28.4465 4.9187 20.268 4.9187C19.8373 4.9187 19.4883 5.26774 19.4883 5.69844C19.4883 6.12915 19.8373 6.47818 20.268 6.47818Z"
                        fill="#4CAF50"
                    />
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M20.2683 11.1547C25.0041 11.1547 28.8491 14.9997 28.8491 19.7355C28.8491 20.1661 29.1985 20.5155 29.6291 20.5155C30.0597 20.5155 30.4092 20.1661 30.4092 19.7355C30.4092 14.1385 25.8653 9.5946 20.2683 9.5946C19.8377 9.5946 19.4883 9.94407 19.4883 10.3747C19.4883 10.8053 19.8377 11.1547 20.2683 11.1547Z"
                        fill="#4CAF50"
                    />
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M20.2683 15.8352C22.4213 15.8352 24.1687 17.5825 24.1687 19.7355C24.1687 20.1661 24.5181 20.5156 24.9487 20.5156C25.3793 20.5156 25.7288 20.1661 25.7288 19.7355C25.7288 16.7221 23.2817 14.275 20.2683 14.275C19.8377 14.275 19.4883 14.6245 19.4883 15.0551C19.4883 15.4857 19.8377 15.8352 20.2683 15.8352Z"
                        fill="#4CAF50"
                    />
                </svg>
            </div>
        </div>
    );
};
export const TotalProfileIcon = () => {
    return (
        <div data-svg-wrapper className="relative">
            <svg width="40" height="41" viewBox="0 0 40 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M35.3125 25.5C35.3125 25.5 33.4375 30.5 27.8125 30.5C22.1875 30.5 20.3125 25.5 20.3125 25.5C20.3125 25.5 22.1875 20.5 27.8125 20.5C33.4375 20.5 35.3125 25.5 35.3125 25.5ZM22.4074 25.5C22.6151 25.1501 22.9156 24.7149 23.321 24.2824C23.9236 23.6396 24.7528 23.0031 25.8896 22.6523C25.5293 23.0855 25.3125 23.6425 25.3125 24.25C25.3125 25.6307 26.4318 26.75 27.8125 26.75C29.1932 26.75 30.3125 25.6307 30.3125 24.25C30.3125 23.6425 30.0957 23.0855 29.7354 22.6523C30.8722 23.0031 31.7014 23.6396 32.304 24.2824C32.7094 24.7149 33.0099 25.1501 33.2176 25.5C33.0099 25.8499 32.7094 26.2851 32.304 26.7176C31.4033 27.6784 29.9964 28.625 27.8125 28.625C25.6286 28.625 24.2217 27.6784 23.321 26.7176C22.9156 26.2851 22.6151 25.8499 22.4074 25.5ZM27.8125 23.625C27.8125 23.9701 27.5326 24.25 27.1875 24.25C26.8424 24.25 26.5625 23.9701 26.5625 23.625C26.5625 23.2799 26.8424 23 27.1875 23C27.5326 23 27.8125 23.2799 27.8125 23.625Z"
                    fill="#4CAF50"
                />
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M22.1875 13C22.1875 16.4517 19.3892 19.25 15.9375 19.25C12.4857 19.25 9.6875 16.4517 9.6875 13C9.6875 9.54822 12.4857 6.75 15.9375 6.75C19.3892 6.75 22.1875 9.54822 22.1875 13ZM17.8125 21.7902C17.2183 21.7642 16.5935 21.75 15.9375 21.75C10.115 21.75 6.75243 22.8681 5.39519 23.451C4.94458 23.6445 4.6875 24.095 4.6875 24.5854C4.6875 29.2326 8.45485 33 13.1021 33H18.7729C20.3921 33 21.9046 32.5426 23.1881 31.75H22.8125C20.0511 31.75 17.8125 29.5114 17.8125 26.75V21.7902Z"
                    fill="#29ABE2"
                />
            </svg>
        </div>
    );
};
export const ProfileClickIcon = () => {
    return (
        <div>
            <div data-svg-wrapper className="relative">
                <svg width="40" height="41" viewBox="0 0 40 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M25.8123 33.6913C25.712 33.7136 25.6071 33.7195 25.5022 33.7086C25.1226 33.6686 24.8171 33.4057 24.7384 33.0527L22.4993 23.0144C22.4239 22.6765 22.5721 22.322 22.8743 22.1194C23.1769 21.914 23.5739 21.9027 23.882 22.0852L33.5468 27.8326C33.8765 28.0287 34.0298 28.4072 33.9264 28.7701C33.822 29.1332 33.4879 29.4018 33.0917 29.4208L28.3344 29.732L26.4291 33.2331C26.301 33.469 26.0722 33.6333 25.8123 33.6913Z"
                        fill="#29ABE2"
                    />
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M23.5 13C23.5 16.4517 20.7017 19.25 17.25 19.25C13.7982 19.25 11 16.4517 11 13C11 9.54822 13.7982 6.75 17.25 6.75C20.7017 6.75 23.5 9.54822 23.5 13ZM18.3521 21.7902C17.7579 21.7642 17.906 21.75 17.25 21.75C11.4275 21.75 8.06493 22.8681 6.70769 23.451C6.25708 23.6445 6 24.095 6 24.5854C6 29.2326 9.76735 33 14.4146 33H19.3125C20.3693 33 22.3125 32.5552 22.3125 32.4495C22.3125 32.3438 22.3125 32 22.3125 32V31.6418C22.3125 31.6418 18.3521 29.1549 18.3521 26.75V21.7902Z"
                        fill="#FEA832"
                    />
                </svg>
            </div>
        </div>
    );
};
export const ActionIcon = () => {
    return (
        <div className="inline-flex w-[94px] items-center justify-center gap-1 p-[5px]">
            <div className="h-1.5 w-1.5 rounded-full bg-[#1d7eb6]"></div>
            <div className="h-1.5 w-1.5 rounded-full bg-[#1d7eb6]"></div>
            <div className="h-1.5 w-1.5 rounded-full bg-[#1d7eb6]"></div>
        </div>
    );
};
export const DatatableSortingIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
                <path
                    d="M9.03839 8.15764C9.11086 8.22794 9.19709 8.28373 9.2921 8.32181C9.3871 8.35988 9.489 8.37949 9.59192 8.37949C9.69484 8.37949 9.79675 8.35988 9.89175 8.32181C9.98676 8.28373 10.073 8.22794 10.1455 8.15764L12.4843 5.90764C12.6312 5.76641 12.7136 5.57487 12.7136 5.37514C12.7136 5.17541 12.6312 4.98387 12.4843 4.84264C12.3375 4.70141 12.1384 4.62207 11.9308 4.62207C11.7232 4.62207 11.5241 4.70141 11.3773 4.84264L9.59192 6.56764L7.80657 4.84264C7.73388 4.77271 7.64758 4.71724 7.55261 4.67939C7.45763 4.64155 7.35584 4.62207 7.25303 4.62207C7.04542 4.62207 6.8463 4.70141 6.6995 4.84264C6.62681 4.91257 6.56914 4.99559 6.5298 5.08695C6.49046 5.17832 6.47021 5.27624 6.47021 5.37514C6.47021 5.57487 6.55269 5.76641 6.6995 5.90764L9.03839 8.15764ZM10.1455 11.5926C10.073 11.5223 9.98676 11.4665 9.89175 11.4285C9.79675 11.3904 9.69484 11.3708 9.59192 11.3708C9.489 11.3708 9.3871 11.3904 9.2921 11.4285C9.19709 11.4665 9.11086 11.5223 9.03839 11.5926L6.6995 13.8426C6.55269 13.9839 6.47021 14.1754 6.47021 14.3751C6.47021 14.5749 6.55269 14.7664 6.6995 14.9076C6.8463 15.0489 7.04542 15.1282 7.25303 15.1282C7.46065 15.1282 7.65976 15.0489 7.80657 14.9076L9.59192 13.1826L11.3773 14.9076C11.4498 14.9779 11.536 15.0337 11.631 15.0718C11.726 15.1099 11.8279 15.1295 11.9308 15.1295C12.0337 15.1295 12.1356 15.1099 12.2306 15.0718C12.3256 15.0337 12.4119 14.9779 12.4843 14.9076C12.5574 14.8379 12.6154 14.755 12.655 14.6636C12.6946 14.5722 12.715 14.4741 12.715 14.3751C12.715 14.2761 12.6946 14.1781 12.655 14.0867C12.6154 13.9953 12.5574 13.9124 12.4843 13.8426L10.1455 11.5926Z"
                    fill="#1D7EB6"
                />
            </svg>
        </>
    );
};
export const DatatableAccendingSortingIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none" className="ml-1 inline-block">
                <path d="M5 0L9.33013 7.5H0.669873L5 0Z" fill="#333333" />
            </svg>
        </>
    );
};
export const DatatableDeccendingSortingIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none" className="ml-1 inline-block">
                <path d="M5 10L0.669872 2.5L9.33013 2.5L5 10Z" fill="#333333" />
            </svg>
        </>
    );
};
export const PaginationLeftIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="6" height="12" viewBox="0 0 6 12" fill="none">
                <path d="M0.699999 11.0039L4.33062 6.76819C4.7158 6.3188 4.7158 5.65568 4.33061 5.20629L0.7 0.970573" stroke="#2D2D2E" strokeWidth="1.1" strokeLinecap="round" />
            </svg>
        </>
    );
};
export const PaginationDownIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="6" viewBox="0 0 10 6" fill="none">
                <path d="M1 1L5 5L9 1" stroke="#2D2D2E" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
        </>
    );
};
export const PaginationRightIcon = () => {
    return (
        <div>
            <svg xmlns="http://www.w3.org/2000/svg" width="6" height="12" viewBox="0 0 6 12" fill="none">
                <path d="M5.3 11.0039L1.66938 6.76819C1.2842 6.3188 1.2842 5.65568 1.66939 5.20629L5.3 0.970573" stroke="#2D2D2E" strokeWidth="1.1" strokeLinecap="round" />
            </svg>
        </div>
    );
};
export const SearchIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="21" height="22" viewBox="0 0 21 22" fill="none">
                <circle cx="9" cy="9.5" r="8" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M14.5 15.458L19.5 20.458" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
        </>
    );
};
export const VerifiedGreenTickIcon = () => {
    return (
        <div data-svg-wrapper className="relative">
            <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask id="mask0_1183_11212" maskUnits="userSpaceOnUse" x="1" y="1" width="22" height="23">
                    <path
                        d="M12 22.4998C13.3135 22.5014 14.6143 22.2435 15.8278 21.7408C17.0412 21.2382 18.1434 20.5007 19.071 19.5708C20.0009 18.6432 20.7384 17.541 21.2411 16.3275C21.7437 15.114 22.0016 13.8132 22 12.4998C22.0016 11.1863 21.7437 9.88548 21.2411 8.67201C20.7384 7.45854 20.0009 6.35635 19.071 5.42876C18.1434 4.49884 17.0412 3.76137 15.8278 3.25872C14.6143 2.75607 13.3135 2.49814 12 2.49976C10.6866 2.49814 9.38572 2.75607 8.17225 3.25872C6.95878 3.76137 5.85659 4.49884 4.92901 5.42876C3.99909 6.35635 3.26162 7.45854 2.75897 8.67201C2.25631 9.88548 1.99839 11.1863 2.00001 12.4998C1.99839 13.8132 2.25631 15.114 2.75897 16.3275C3.26162 17.541 3.99909 18.6432 4.92901 19.5708C5.85659 20.5007 6.95878 21.2382 8.17225 21.7408C9.38572 22.2435 10.6866 22.5014 12 22.4998Z"
                        fill="white"
                        stroke="white"
                        stroke-width="1.83333"
                        stroke-linejoin="round"
                    />
                    <path d="M8 12.4998L11 15.4998L17 9.49976" stroke="black" stroke-width="1.83333" stroke-linecap="round" stroke-linejoin="round" />
                </mask>
                <g mask="url(#mask0_1183_11212)">
                    <path d="M0 0.5H24V24.5H0V0.5Z" fill="#2AB141" />
                </g>
            </svg>
        </div>
    );
};
export const RemoveFeatureIcon = () => {
    return (
        <div data-svg-wrapper className="cursor-pointer">
            <svg width="37" height="37" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.75" y="0.5" width="36" height="36" rx="5" fill="#D6F0FF" />
                <path d="M25.75 18.498H11.75V16.498H25.75V18.498Z" fill="#007AFF" />
            </svg>
        </div>
    );
};
export const AddFeatureIconIcon = () => {
    return (
        <div data-svg-wrapper className="cursor-pointer">
            <svg width="37" height="37" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.75" y="0.5" width="36" height="36" rx="5" fill="#D6F0FF" />
                <path d="M17.75 18.5H11.75V16.5H17.75V10.5H19.75V16.5H25.75V18.5H19.75V24.5H17.75V18.5Z" fill="#007AFF" />
            </svg>
        </div>
    );
};
export const DeleteRedIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path
                    d="M16.0553 4.77079C16.2561 4.77079 16.4486 4.85054 16.5906 4.99249C16.7325 5.13445 16.8123 5.32697 16.8123 5.52773C16.8123 5.72848 16.7325 5.921 16.5906 6.06296C16.4486 6.20491 16.2561 6.28466 16.0553 6.28466H15.2984L15.2961 6.3384L14.5899 16.2322C14.5627 16.6142 14.3918 16.9716 14.1116 17.2326C13.8314 17.4935 13.4627 17.6386 13.0798 17.6386H6.91917C6.53627 17.6386 6.16759 17.4935 5.88739 17.2326C5.60718 16.9716 5.43628 16.6142 5.40909 16.2322L4.70288 6.33916L4.70136 6.28466H3.94443C3.74368 6.28466 3.55115 6.20491 3.4092 6.06296C3.26725 5.921 3.1875 5.72848 3.1875 5.52773C3.1875 5.32697 3.26725 5.13445 3.4092 4.99249C3.55115 4.85054 3.74368 4.77079 3.94443 4.77079H16.0553ZM13.7823 6.28466H6.2175L6.91993 16.1248H13.0798L13.7823 6.28466ZM11.5137 2.5C11.7145 2.5 11.907 2.57975 12.049 2.7217C12.1909 2.86365 12.2707 3.05618 12.2707 3.25693C12.2707 3.45768 12.1909 3.65021 12.049 3.79216C11.907 3.93411 11.7145 4.01386 11.5137 4.01386H8.48602C8.28527 4.01386 8.09274 3.93411 7.95079 3.79216C7.80884 3.65021 7.72909 3.45768 7.72909 3.25693C7.72909 3.05618 7.80884 2.86365 7.95079 2.7217C8.09274 2.57975 8.28527 2.5 8.48602 2.5H11.5137Z"
                    fill="#FF3B30"
                />
            </svg>
        </>
    );
};
export const CrossIcon = () => {
    return (
        <>
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 4L4 12M4 4L12 12" stroke="#636363" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
        </>
    );
};
export const ReviewStarIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="16" viewBox="0 0 15 16" fill="none">
                <path
                    d="M14.8691 6.48773L11.519 9.91917L12.3065 14.7757C12.3759 15.1632 11.9552 15.447 11.619 15.2632L7.5 12.9818V0.337402C7.67501 0.337402 7.85002 0.418657 7.92502 0.587416L9.99388 4.9939L14.6004 5.69394C14.9854 5.76269 15.1273 6.21584 14.8691 6.48773Z"
                    fill="#FFC107"
                />
                <path
                    d="M7.50007 0.337402V12.9818L3.3811 15.2632C3.05046 15.4488 2.62356 15.1676 2.69356 14.7757L3.48111 9.91917L0.130927 6.48773C-0.127212 6.21584 0.0140454 5.76269 0.399691 5.69394L5.00619 4.9939L7.07505 0.587416C7.15006 0.418657 7.32507 0.337402 7.50007 0.337402Z"
                    fill="#FFC107"
                />
            </svg>
        </>
    );
};
export const VerifiedSmallIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <g clip-path="url(#clip0_809_72705)">
                    <mask id="mask0_809_72705" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                        <path
                            d="M8.00066 14.6654C8.87629 14.6664 9.74351 14.4945 10.5525 14.1594C11.3615 13.8243 12.0963 13.3326 12.7147 12.7127C13.3346 12.0943 13.8263 11.3595 14.1614 10.5505C14.4965 9.74156 14.6684 8.87434 14.6673 7.9987C14.6684 7.12307 14.4965 6.25585 14.1614 5.44687C13.8263 4.63789 13.3346 3.90309 12.7147 3.2847C12.0963 2.66476 11.3615 2.17311 10.5525 1.83801C9.74351 1.50291 8.87629 1.33096 8.00066 1.33204C7.12502 1.33096 6.2578 1.50291 5.44882 1.83801C4.63984 2.17311 3.90505 2.66476 3.28666 3.2847C2.66671 3.90309 2.17506 4.63789 1.83996 5.44687C1.50486 6.25585 1.33291 7.12307 1.33399 7.9987C1.33291 8.87434 1.50486 9.74156 1.83996 10.5505C2.17506 11.3595 2.66671 12.0943 3.28666 12.7127C3.90505 13.3326 4.63984 13.8243 5.44882 14.1594C6.2578 14.4945 7.12502 14.6664 8.00066 14.6654Z"
                            fill="white"
                            stroke="white"
                            stroke-width="1.83333"
                            stroke-linejoin="round"
                        />
                        <path d="M5.33398 8L7.33398 10L11.334 6" stroke="black" stroke-width="1.83333" stroke-linecap="round" stroke-linejoin="round" />
                    </mask>
                    <g mask="url(#mask0_809_72705)">
                        <path d="M0 0H16V16H0V0Z" fill="#2AB141" />
                    </g>
                </g>
                <defs>
                    <clipPath id="clip0_809_72705">
                        <rect width="16" height="16" fill="white" />
                    </clipPath>
                </defs>
            </svg>
        </>
    );
};

export const SearchMessageDashboradIcon = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21" fill="none">
            <circle cx="9" cy="9" r="8" stroke="#2D2D2E" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M14.5 14.957L19.5 19.957" stroke="#2D2D2E" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
    );
};
export const ReadMessageDashboradIcon = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path
                d="M0.273438 8.93875L4.0001 12.6654L4.9401 11.7188L1.2201 7.99875M14.8268 3.71875L7.77344 10.7788L5.0001 7.99875L4.04677 8.93875L7.77344 12.6654L15.7734 4.66542M12.0001 4.66542L11.0601 3.71875L6.82677 7.95208L7.77344 8.89208L12.0001 4.66542Z"
                fill="#2AB141"
            />
        </svg>
    );
};
export const UnReadMessageDashboradIcon = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path
                d="M0.273438 8.93875L4.0001 12.6654L4.9401 11.7188L1.2201 7.99875M14.8268 3.71875L7.77344 10.7788L5.0001 7.99875L4.04677 8.93875L7.77344 12.6654L15.7734 4.66542M12.0001 4.66542L11.0601 3.71875L6.82677 7.95208L7.77344 8.89208L12.0001 4.66542Z"
                fill="white"
            />
        </svg>
    );
};
export const CloseIconWhite = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="#ffffff">
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M19.71 5.70905L18.2901 4.28906L12 10.5891L5.71002 4.28906L4.29004 5.70905L10.59 11.9991L4.29004 18.2891L5.71002 19.7091L12 13.4091L18.2901 19.7091L19.71 18.2891L13.41 11.9991L19.71 5.70905Z"
                fill="#ffffff"
            />
        </svg>
    );
};
export const SmileIcon = () => {
    return (
        <>
            <div data-svg-wrapper className="relative">
                <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M12.9993 2.1665C18.9826 2.1665 23.8327 7.01659 23.8327 12.9998C23.8327 18.9831 18.9826 23.8332 12.9993 23.8332C7.0161 23.8332 2.16602 18.9831 2.16602 12.9998C2.16602 7.01659 7.0161 2.1665 12.9993 2.1665ZM12.9993 4.33317C10.7008 4.33317 8.49641 5.24626 6.87109 6.87158C5.24578 8.49689 4.33268 10.7013 4.33268 12.9998C4.33268 15.2984 5.24578 17.5028 6.87109 19.1281C8.49641 20.7534 10.7008 21.6665 12.9993 21.6665C15.2979 21.6665 17.5023 20.7534 19.1276 19.1281C20.7529 17.5028 21.666 15.2984 21.666 12.9998C21.666 10.7013 20.7529 8.49689 19.1276 6.87158C17.5023 5.24626 15.2979 4.33317 12.9993 4.33317ZM16.0327 15.0116C16.1338 14.9096 16.2541 14.8288 16.3867 14.7737C16.5193 14.7187 16.6615 14.6905 16.8051 14.6909C16.9486 14.6913 17.0907 14.7203 17.223 14.776C17.3553 14.8318 17.4751 14.9133 17.5756 15.0158C17.6762 15.1184 17.7553 15.2398 17.8084 15.3732C17.8616 15.5066 17.8877 15.6492 17.8852 15.7927C17.8828 15.9363 17.8518 16.0779 17.7942 16.2094C17.7365 16.3408 17.6533 16.4596 17.5494 16.5586C16.3356 17.7515 14.7012 18.4189 12.9993 18.4165C11.2975 18.4189 9.66311 17.7515 8.44935 16.5586C8.24896 16.3564 8.13619 16.0835 8.1354 15.7989C8.13462 15.5143 8.24589 15.2408 8.44515 15.0375C8.64442 14.8343 8.91567 14.7176 9.20027 14.7127C9.48487 14.7079 9.75994 14.8152 9.96602 15.0116C10.7748 15.8074 11.8647 16.2523 12.9993 16.2498C14.1802 16.2498 15.2494 15.7786 16.0327 15.0116ZM9.20768 8.6665C9.63866 8.6665 10.052 8.83771 10.3567 9.14246C10.6615 9.4472 10.8327 9.86053 10.8327 10.2915C10.8327 10.7225 10.6615 11.1358 10.3567 11.4406C10.052 11.7453 9.63866 11.9165 9.20768 11.9165C8.77671 11.9165 8.36338 11.7453 8.05863 11.4406C7.75389 11.1358 7.58268 10.7225 7.58268 10.2915C7.58268 9.86053 7.75389 9.4472 8.05863 9.14246C8.36338 8.83771 8.77671 8.6665 9.20768 8.6665ZM16.791 8.6665C17.222 8.6665 17.6353 8.83771 17.9401 9.14246C18.2448 9.4472 18.416 9.86053 18.416 10.2915C18.416 10.7225 18.2448 11.1358 17.9401 11.4406C17.6353 11.7453 17.222 11.9165 16.791 11.9165C16.36 11.9165 15.9467 11.7453 15.642 11.4406C15.3372 11.1358 15.166 10.7225 15.166 10.2915C15.166 9.86053 15.3372 9.4472 15.642 9.14246C15.9467 8.83771 16.36 8.6665 16.791 8.6665Z"
                        fill="#636363"
                    />
                </svg>
            </div>
        </>
    );
};
export const AttactchmentIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                    d="M2.86133 13.6248L12.2033 4.28281C12.6875 3.78832 13.265 3.39474 13.9023 3.12486C14.5396 2.85498 15.2241 2.71415 15.9162 2.71052C16.6083 2.70689 17.2942 2.84052 17.9343 3.1037C18.5744 3.36687 19.156 3.75436 19.6454 4.24375C20.1348 4.73313 20.5223 5.3147 20.7854 5.9548C21.0486 6.59491 21.1823 7.28084 21.1786 7.97293C21.175 8.66501 21.0342 9.34951 20.7643 9.98682C20.4944 10.6241 20.1008 11.2016 19.6063 11.6858L10.8803 20.4098C10.3398 20.9329 9.61528 21.2225 8.86311 21.2163C8.11094 21.2101 7.39133 20.9086 6.85945 20.3767C6.32757 19.8448 6.02602 19.1252 6.01982 18.373C6.01363 17.6209 6.30328 16.8964 6.82633 16.3558L15.1983 7.98281"
                    stroke="#636363"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                />
            </svg>
        </>
    );
};
export const CalenderIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M19 6H5C3.89543 6 3 6.89543 3 8V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V8C21 6.89543 20.1046 6 19 6Z" stroke="#636363" stroke-width="2" />
                <path d="M3 10C3 8.114 3 7.172 3.586 6.586C4.172 6 5.114 6 7 6H17C18.886 6 19.828 6 20.414 6.586C21 7.172 21 8.114 21 10H3Z" fill="#636363" />
                <path d="M7 3V6M17 3V6" stroke="#636363" stroke-width="2" stroke-linecap="round" />
            </svg>
        </>
    );
};

export const CalenderIcon2 = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22" fill="none">
                <rect x="1.25" y="2.5" width="18" height="18" rx="5" stroke="#2D2D2E" stroke-width="1.5" />
                <path d="M1.25 7.5H19.25" stroke="#2D2D2E" stroke-width="1.5" stroke-linejoin="round" />
                <path d="M14.75 1L14.75 4" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M5.75 1L5.75 4" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M4.75 11.5H5.75" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M9.75 11.5H10.75" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M14.75 11.5H15.75" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M4.75 15.5H5.75" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M9.75 15.5H10.75" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M14.75 15.5H15.75" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
        </>
    );
};

export const MinusIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                <path d="M19.75 11.998H5.75V9.99805H19.75V11.998Z" fill="#007AFF" />
            </svg>
        </>
    );
};

export const PlusIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                <path d="M11.75 12H5.75V10H11.75V4H13.75V10H19.75V12H13.75V18H11.75V12Z" fill="#007AFF" />
            </svg>
        </>
    );
};

export const PlusWhiteIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                <path d="M11.75 12H5.75V10H11.75V4H13.75V10H19.75V12H13.75V18H11.75V12Z" fill="#ffffff" />
            </svg>
        </>
    );
};
export const ToolTipIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                    d="M11 17H13V11H11V17ZM12 9C12.2833 9 12.521 8.904 12.713 8.712C12.905 8.52 13.0007 8.28266 13 8C12.9993 7.71733 12.9033 7.48 12.712 7.288C12.5207 7.096 12.2833 7 12 7C11.7167 7 11.4793 7.096 11.288 7.288C11.0967 7.48 11.0007 7.71733 11 8C10.9993 8.28266 11.0953 8.52033 11.288 8.713C11.4807 8.90566 11.718 9.00133 12 9ZM12 22C10.6167 22 9.31666 21.7373 8.1 21.212C6.88333 20.6867 5.825 19.9743 4.925 19.075C4.025 18.1757 3.31267 17.1173 2.788 15.9C2.26333 14.6827 2.00067 13.3827 2 12C1.99933 10.6173 2.262 9.31733 2.788 8.1C3.314 6.88267 4.02633 5.82433 4.925 4.925C5.82367 4.02567 6.882 3.31333 8.1 2.788C9.318 2.26267 10.618 2 12 2C13.382 2 14.682 2.26267 15.9 2.788C17.118 3.31333 18.1763 4.02567 19.075 4.925C19.9737 5.82433 20.6863 6.88267 21.213 8.1C21.7397 9.31733 22.002 10.6173 22 12C21.998 13.3827 21.7353 14.6827 21.212 15.9C20.6887 17.1173 19.9763 18.1757 19.075 19.075C18.1737 19.9743 17.1153 20.687 15.9 21.213C14.6847 21.739 13.3847 22.0013 12 22ZM12 20C14.2333 20 16.125 19.225 17.675 17.675C19.225 16.125 20 14.2333 20 12C20 9.76666 19.225 7.875 17.675 6.325C16.125 4.775 14.2333 4 12 4C9.76666 4 7.875 4.775 6.325 6.325C4.775 7.875 4 9.76666 4 12C4 14.2333 4.775 16.125 6.325 17.675C7.875 19.225 9.76666 20 12 20Z"
                    fill="#636363"
                />
            </svg>
        </>
    );
};

export const PdfIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16" fill="none">
                <path
                    d="M5.68438 8.00313C5.52813 7.50313 5.53125 6.5375 5.62187 6.5375C5.88438 6.5375 5.85938 7.69063 5.68438 8.00313ZM5.63125 9.47812C5.39062 10.1094 5.09063 10.8313 4.74375 11.4375C5.31563 11.2188 5.9625 10.9 6.70938 10.7531C6.3125 10.4531 5.93125 10.0219 5.63125 9.47812ZM2.69063 13.3781C2.69063 13.4031 3.10313 13.2094 3.78125 12.1219C3.57188 12.3187 2.87188 12.8875 2.69063 13.3781ZM7.75 5H12V15.25C12 15.6656 11.6656 16 11.25 16H0.75C0.334375 16 0 15.6656 0 15.25V0.75C0 0.334375 0.334375 0 0.75 0H7V4.25C7 4.6625 7.3375 5 7.75 5ZM7.5 10.3687C6.875 9.9875 6.45938 9.4625 6.16563 8.6875C6.30625 8.10938 6.52813 7.23125 6.35938 6.68125C6.2125 5.7625 5.03438 5.85313 4.86563 6.46875C4.70938 7.04063 4.85313 7.84688 5.11875 8.875C4.75625 9.7375 4.22188 10.8937 3.84375 11.5562C3.84063 11.5562 3.84063 11.5594 3.8375 11.5594C2.99063 11.9937 1.5375 12.95 2.13438 13.6844C2.30938 13.9 2.63438 13.9969 2.80625 13.9969C3.36563 13.9969 3.92188 13.4344 4.71562 12.0656C5.52187 11.8 6.40625 11.4688 7.18438 11.3406C7.8625 11.7094 8.65625 11.95 9.18437 11.95C10.0969 11.95 10.1594 10.95 9.8 10.5938C9.36563 10.1687 8.10313 10.2906 7.5 10.3687ZM11.7813 3.28125L8.71875 0.21875C8.57812 0.078125 8.3875 0 8.1875 0H8V4H12V3.80938C12 3.6125 11.9219 3.42188 11.7813 3.28125ZM9.46562 11.2594C9.59375 11.175 9.3875 10.8875 8.12813 10.9781C9.2875 11.4719 9.46562 11.2594 9.46562 11.2594Z"
                    fill="#1D7EB6"
                />
            </svg>
        </>
    );
};

export const CheckIconProfile = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <g clip-path="url(#clip0_1039_16238)">
                    <mask id="mask0_1039_16238" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                        <path
                            d="M8.00066 14.6654C8.87629 14.6664 9.74351 14.4945 10.5525 14.1594C11.3615 13.8243 12.0963 13.3326 12.7147 12.7127C13.3346 12.0943 13.8263 11.3595 14.1614 10.5505C14.4965 9.74156 14.6684 8.87434 14.6673 7.9987C14.6684 7.12307 14.4965 6.25585 14.1614 5.44687C13.8263 4.63789 13.3346 3.90309 12.7147 3.2847C12.0963 2.66476 11.3615 2.17311 10.5525 1.83801C9.74351 1.50291 8.87629 1.33096 8.00066 1.33204C7.12502 1.33096 6.2578 1.50291 5.44882 1.83801C4.63984 2.17311 3.90505 2.66476 3.28666 3.2847C2.66671 3.90309 2.17506 4.63789 1.83996 5.44687C1.50486 6.25585 1.33291 7.12307 1.33399 7.9987C1.33291 8.87434 1.50486 9.74156 1.83996 10.5505C2.17506 11.3595 2.66671 12.0943 3.28666 12.7127C3.90505 13.3326 4.63984 13.8243 5.44882 14.1594C6.2578 14.4945 7.12502 14.6664 8.00066 14.6654Z"
                            fill="white"
                            stroke="white"
                            stroke-width="1.83333"
                            stroke-linejoin="round"
                        />
                        <path d="M5.33398 8L7.33398 10L11.334 6" stroke="black" stroke-width="1.83333" stroke-linecap="round" stroke-linejoin="round" />
                    </mask>
                    <g mask="url(#mask0_1039_16238)">
                        <path d="M0 0H16V16H0V0Z" fill="#2AB141" />
                    </g>
                </g>
                <defs>
                    <clipPath id="clip0_1039_16238">
                        <rect width="16" height="16" fill="white" />
                    </clipPath>
                </defs>
            </svg>
        </>
    );
};

export const DeleteIcon = () => {
    return (
        <>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="mingcute:delete-line">
                    <g id="Group">
                        <path
                            id="Vector"
                            d="M19.2666 5.72495C19.5075 5.72495 19.7385 5.82065 19.9089 5.99099C20.0792 6.16134 20.1749 6.39237 20.1749 6.63327C20.1749 6.87417 20.0792 7.10521 19.9089 7.27555C19.7385 7.44589 19.5075 7.54159 19.2666 7.54159H18.3583L18.3556 7.60608L17.5081 19.4787C17.4755 19.937 17.2704 20.366 16.9341 20.6791C16.5979 20.9923 16.1555 21.1664 15.696 21.1664H8.3032C7.84372 21.1664 7.4013 20.9923 7.06506 20.6791C6.72882 20.366 6.52373 19.937 6.49111 19.4787L5.64365 7.60699L5.64183 7.54159H4.73351C4.49261 7.54159 4.26158 7.44589 4.09124 7.27555C3.92089 7.10521 3.8252 6.87417 3.8252 6.63327C3.8252 6.39237 3.92089 6.16134 4.09124 5.99099C4.26158 5.82065 4.49261 5.72495 4.73351 5.72495H19.2666ZM16.5389 7.54159H7.46119L8.30411 19.3497H15.696L16.5389 7.54159ZM13.8167 3C14.0576 3 14.2886 3.0957 14.459 3.26604C14.6293 3.43638 14.725 3.66742 14.725 3.90832C14.725 4.14922 14.6293 4.38025 14.459 4.5506C14.2886 4.72094 14.0576 4.81664 13.8167 4.81664H10.1834C9.94252 4.81664 9.71148 4.72094 9.54114 4.5506C9.3708 4.38025 9.2751 4.14922 9.2751 3.90832C9.2751 3.66742 9.3708 3.43638 9.54114 3.26604C9.71148 3.0957 9.94252 3 10.1834 3H13.8167Z"
                            fill="#FF3B30"
                        />
                    </g>
                </g>
            </svg>
        </>
    );
};

export const TimeIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path
                    d="M12 2C17.523 2 22 6.477 22 12C22 17.523 17.523 22 12 22C6.477 22 2 17.523 2 12C2 6.477 6.477 2 12 2ZM12 4C9.87827 4 7.84344 4.84285 6.34315 6.34315C4.84285 7.84344 4 9.87827 4 12C4 14.1217 4.84285 16.1566 6.34315 17.6569C7.84344 19.1571 9.87827 20 12 20C14.1217 20 16.1566 19.1571 17.6569 17.6569C19.1571 16.1566 20 14.1217 20 12C20 9.87827 19.1571 7.84344 17.6569 6.34315C16.1566 4.84285 14.1217 4 12 4ZM12 6C12.2449 6.00003 12.4813 6.08996 12.6644 6.25272C12.8474 6.41547 12.9643 6.63975 12.993 6.883L13 7V11.586L15.707 14.293C15.8863 14.473 15.9905 14.7144 15.9982 14.9684C16.006 15.2223 15.9168 15.4697 15.7488 15.6603C15.5807 15.8508 15.3464 15.9703 15.0935 15.9944C14.8406 16.0185 14.588 15.9454 14.387 15.79L14.293 15.707L11.293 12.707C11.1376 12.5514 11.0378 12.349 11.009 12.131L11 12V7C11 6.73478 11.1054 6.48043 11.2929 6.29289C11.4804 6.10536 11.7348 6 12 6Z"
                    fill="#636363"
                />
            </svg>
        </>
    );
};

export const HeartIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="28" height="25" viewBox="0 0 28 25" fill="none">
                <path
                    d="M9.40173 13.7049L9.04824 13.3494C8.95584 13.2563 8.84594 13.1825 8.72486 13.132C8.60377 13.0816 8.47391 13.0557 8.34275 13.0557C8.21159 13.0557 8.08173 13.0816 7.96065 13.132C7.83956 13.1825 7.72966 13.2563 7.63726 13.3494L5.5178 15.4779C5.33 15.6672 5.22461 15.923 5.22461 16.1896C5.22461 16.4563 5.33 16.7121 5.5178 16.9014L5.8708 17.2559C5.96315 17.3489 6.07301 17.4228 6.19405 17.4732C6.31509 17.5236 6.44491 17.5496 6.57603 17.5496C6.70715 17.5496 6.83697 17.5236 6.95801 17.4732C7.07905 17.4228 7.18892 17.3489 7.28127 17.2559L9.40123 15.1274C9.58892 14.9383 9.69429 14.6827 9.69438 14.4163C9.69448 14.1498 9.58929 13.8942 9.40173 13.7049Z"
                    fill="#FF2D55"
                />
                <path
                    d="M10.8174 22.224C11.0068 22.4057 11.2591 22.5071 11.5216 22.5071C11.7841 22.5071 12.0364 22.4057 12.2259 22.224V22.2215L14.3458 20.0956L14.3488 20.0931C14.5361 19.9041 14.6411 19.6489 14.6411 19.3828C14.6411 19.1168 14.5361 18.8616 14.3488 18.6726L13.9958 18.3171C13.8061 18.1349 13.5533 18.0332 13.2903 18.0332C13.0273 18.0332 12.7745 18.1349 12.5849 18.3171L10.4654 20.4456C10.2774 20.6345 10.1719 20.8903 10.1719 21.1568C10.1719 21.4234 10.2774 21.6791 10.4654 21.8681L10.8174 22.224Z"
                    fill="#FF2D55"
                />
                <path
                    d="M13.2912 24.7072C13.3835 24.8001 13.4933 24.8739 13.6143 24.9242C13.7352 24.9745 13.8649 25.0004 13.9959 25.0004C14.1269 25.0004 14.2566 24.9745 14.3776 24.9242C14.4985 24.8739 14.6083 24.8001 14.7007 24.7072L16.8191 22.5793C17.0069 22.3904 17.1124 22.1349 17.1124 21.8685C17.1124 21.6022 17.0069 21.3466 16.8191 21.1578L16.4691 20.7998C16.2813 20.613 16.0271 20.5083 15.7622 20.5088C15.6321 20.5086 15.5034 20.5342 15.3832 20.5839C15.2631 20.6337 15.154 20.7067 15.0622 20.7988L12.9427 22.9267C12.7554 23.1157 12.6504 23.371 12.6504 23.637C12.6504 23.903 12.7554 24.1583 12.9427 24.3472L13.2912 24.7072Z"
                    fill="#FF2D55"
                />
                <path
                    d="M9.04894 20.0342C9.18024 20.0345 9.31029 20.0086 9.4315 19.9582C9.55271 19.9077 9.66266 19.8336 9.75493 19.7402L11.8744 17.6122C12.0622 17.4229 12.1676 17.1671 12.1676 16.9005C12.1676 16.6338 12.0622 16.378 11.8744 16.1887L11.5214 15.8332C11.4289 15.7403 11.3189 15.6666 11.1978 15.6163C11.0767 15.5659 10.9468 15.54 10.8157 15.54C10.6845 15.54 10.5547 15.5659 10.4335 15.6163C10.3124 15.6666 10.2025 15.7403 10.1099 15.8332L7.99046 17.9622C7.80265 18.1515 7.69727 18.4073 7.69727 18.6739C7.69727 18.9406 7.80265 19.1964 7.99046 19.3857L8.34395 19.7402C8.4361 19.8335 8.54589 19.9075 8.66692 19.958C8.78795 20.0084 8.91781 20.0343 9.04894 20.0342Z"
                    fill="#FF2D55"
                />
                <path
                    d="M16.8235 9.80607L22.4764 15.483C22.6659 15.6653 22.9187 15.7672 23.1817 15.7672C23.4447 15.7672 23.6974 15.6653 23.8869 15.483L24.2404 15.128C24.4283 14.9389 24.5337 14.6833 24.5337 14.4167C24.5337 14.1502 24.4283 13.8945 24.2404 13.7055L16.4675 5.90064C16.3732 5.80925 16.2469 5.75814 16.1155 5.75814C15.9842 5.75814 15.8579 5.80925 15.7635 5.90064L11.8776 9.80057C11.5996 10.0799 11.2691 10.3016 10.9051 10.4529C10.5411 10.6042 10.1508 10.682 9.75665 10.682C9.36249 10.682 8.97222 10.6042 8.60824 10.4529C8.24426 10.3016 7.91375 10.0799 7.63569 9.80057C7.35522 9.51809 7.19782 9.13616 7.19782 8.73809C7.19782 8.34002 7.35522 7.95809 7.63569 7.67561L13.2731 2.01621C11.7541 0.661781 9.77347 -0.0585081 7.73926 0.00372091C5.70505 0.0659499 3.77217 0.905958 2.33879 2.35071C0.84064 3.86097 0 5.90207 0 8.02935C0 10.1566 0.84064 12.1977 2.33879 13.708L4.30025 15.678C4.38665 15.3364 4.56231 15.0239 4.80924 14.7725L6.92871 12.644C7.11409 12.4576 7.3345 12.3097 7.57724 12.2088C7.81999 12.1078 8.08029 12.0559 8.34318 12.0559C8.60607 12.0559 8.86637 12.1078 9.10912 12.2088C9.35186 12.3097 9.57227 12.4576 9.75766 12.644L10.1106 12.9995C10.3136 13.2047 10.4696 13.4516 10.5678 13.723C10.6661 13.9944 10.7042 14.2839 10.6796 14.5715C10.9627 14.5488 11.2473 14.5867 11.5145 14.6826C11.7817 14.7784 12.0255 14.9301 12.2296 15.1275L12.5831 15.483C12.7837 15.6861 12.9384 15.9298 13.0367 16.1978C13.135 16.4658 13.1747 16.7518 13.1531 17.0364C13.1986 17.0334 13.2426 17.0234 13.2886 17.0234C13.5516 17.0229 13.812 17.0746 14.0548 17.1755C14.2976 17.2765 14.518 17.4246 14.7031 17.6114L15.0561 17.9669C15.2591 18.1722 15.4151 18.4191 15.5134 18.6906C15.6116 18.9621 15.6497 19.2517 15.625 19.5394C15.9079 19.5171 16.1923 19.555 16.4594 19.6506C16.7266 19.7462 16.9705 19.8972 17.175 20.0939L17.518 20.4439C17.521 20.4464 17.525 20.4474 17.5275 20.4499C17.6199 20.543 17.7298 20.6169 17.8509 20.6673C17.972 20.7177 18.1018 20.7437 18.233 20.7437C18.3642 20.7437 18.494 20.7177 18.6151 20.6673C18.7362 20.6169 18.8461 20.543 18.9385 20.4499L19.292 20.0944C19.4794 19.9053 19.5846 19.6499 19.5846 19.3836C19.5846 19.1174 19.4794 18.862 19.292 18.6729L13.6416 12.9955C13.5952 12.949 13.5585 12.8937 13.5335 12.833C13.5085 12.7722 13.4957 12.7072 13.4958 12.6415C13.4959 12.5758 13.509 12.5107 13.5343 12.4501C13.5596 12.3895 13.5965 12.3344 13.6431 12.288C13.6896 12.2417 13.7449 12.2049 13.8056 12.1799C13.8664 12.1549 13.9314 12.1421 13.9971 12.1423C14.1298 12.1425 14.257 12.1955 14.3506 12.2895L20.0035 17.9669C20.1931 18.149 20.4458 18.2506 20.7087 18.2506C20.9716 18.2506 21.2243 18.149 21.4139 17.9669L21.7669 17.6119C21.9547 17.4227 22.0601 17.167 22.0601 16.9004C22.0601 16.6339 21.9547 16.3781 21.7669 16.189L16.1145 10.5121C16.0209 10.418 15.9685 10.2907 15.9688 10.158C15.969 10.0253 16.022 9.89819 16.116 9.80457C16.2101 9.71095 16.3374 9.65851 16.4701 9.6588C16.6028 9.65908 16.7299 9.71205 16.8235 9.80607Z"
                    fill="#FF2D55"
                />
                <path
                    d="M25.4588 13.9042L25.6533 13.7087C27.1514 12.1985 27.9921 10.1575 27.9921 8.03032C27.9921 5.90311 27.1514 3.8621 25.6533 2.35192C24.9129 1.60718 24.0326 1.01616 23.063 0.612868C22.0934 0.209572 21.0536 0.00195313 20.0034 0.00195312C18.9533 0.00195313 17.9135 0.209572 16.9439 0.612868C15.9742 1.01616 15.0939 1.60718 14.3535 2.35192L8.34613 8.38331C8.25203 8.4781 8.19922 8.60624 8.19922 8.7398C8.19922 8.87337 8.25203 9.00151 8.34613 9.0963C8.7258 9.46104 9.23187 9.66474 9.75835 9.66474C10.2848 9.66474 10.7909 9.46104 11.1706 9.0963L15.0565 5.19637C15.3419 4.92257 15.722 4.76969 16.1175 4.76969C16.513 4.76969 16.8931 4.92257 17.1785 5.19637L24.9493 13.0002C25.1963 13.251 25.3722 13.563 25.4588 13.9042Z"
                    fill="#FF2D55"
                />
            </svg>
        </>
    );
};

export const TypeIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="23" viewBox="0 0 32 23" fill="none">
                <path
                    d="M27.6776 0H9.42493C7.4556 0 5.84876 1.59522 5.84876 3.57618V5.2841H1.04567C0.468226 5.2841 0 5.75232 0 6.32976C0 6.9072 0.468226 7.37543 1.04567 7.37543H5.84992V8.84285H2.23656C1.65912 8.84285 1.1909 9.31107 1.1909 9.88851C1.1909 10.466 1.65912 10.9342 2.23656 10.9342H5.84992V12.4028H3.42978C2.85234 12.4028 2.38412 12.871 2.38412 13.4484C2.38412 14.0259 2.85234 14.4941 3.42978 14.4941H5.84992V16.1939C5.84992 18.1609 7.45908 19.7701 9.4261 19.7701H14.8275L17.6439 22.6201C18.1388 23.1266 18.9637 23.1266 19.4587 22.6201L22.275 19.7701H27.6765C29.6574 19.7701 31.2526 18.1632 31.2526 16.1939V3.57618C31.2538 1.60103 29.6528 0 27.6776 0ZM13.0836 11.3931C12.2529 11.3931 11.579 10.7192 11.579 9.88851C11.579 9.05779 12.2529 8.38392 13.0836 8.38392C13.9143 8.38392 14.5882 9.05779 14.5882 9.88851C14.5882 10.7192 13.9143 11.3931 13.0836 11.3931ZM18.5478 11.3931C17.7171 11.3931 17.0432 10.7192 17.0432 9.88851C17.0432 9.05779 17.7171 8.38392 18.5478 8.38392C19.3785 8.38392 20.0524 9.05779 20.0524 9.88851C20.0524 10.7192 19.3785 11.3931 18.5478 11.3931ZM24.0131 11.3931C23.1824 11.3931 22.5085 10.7192 22.5085 9.88851C22.5085 9.05779 23.1824 8.38392 24.0131 8.38392C24.8439 8.38392 25.5177 9.05779 25.5177 9.88851C25.5177 10.7192 24.8439 11.3931 24.0131 11.3931Z"
                    fill="#2AB141"
                />
            </svg>
        </>
    );
};

export const FAAIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 113 175" fill="none">
                <path
                    d="M87.5719 24.3443C87.885 24.6639 88.1916 24.9803 88.4819 25.2804C81.5511 32.1655 69.7637 39.8009 55.5465 46.0566C41.3326 52.3123 27.7383 55.8446 17.983 56.3044C17.9569 55.887 17.9308 55.4466 17.9047 55.0031C6.24458 63.2516 -0.451433 70.3031 0.331344 72.0872C1.69794 75.1955 31.2641 74.5464 62.1088 60.9718C92.9568 47.3971 113.407 26.037 112.04 22.9288C111.257 21.1479 101.535 21.3208 87.5719 24.3443Z"
                    fill="#E4E4E4"
                />
                <path
                    d="M82.63 19.371C78.9118 15.6854 74.7435 11.6378 71.3058 8.3273C69.7337 6.81066 68.315 5.45059 67.1604 4.34491C66.3221 7.20858 63.0671 9.58953 63.0671 9.58953C65.4252 7.03897 66.4591 4.72652 65.7188 3.04355C63.8499 -1.20302 51.3841 0.170101 37.8812 6.11595C25.7025 11.4747 16.8343 18.7219 16.5831 23.2066C16.557 23.6958 17.0006 29.1524 17.0789 32.1922C17.1083 33.3958 17.1409 34.671 17.1767 35.9822L17.3203 35.93L24.1076 33.4055L17.6627 36.703L17.2028 36.9378C17.3105 40.6886 17.4409 44.7102 17.5877 48.4218C26.9419 47.3716 38.9608 43.9763 51.4624 38.474C64.5348 32.7206 75.5524 25.7995 82.63 19.371Z"
                    fill="#E4E4E4"
                />
                <path
                    d="M29.4514 168.52C28.2674 171.56 24.846 173.067 21.8062 171.883C18.7665 170.702 17.2596 167.281 18.4403 164.238L18.5349 163.993C18.3131 166.641 19.8362 169.231 22.4422 170.245C25.0482 171.26 27.9184 170.376 29.546 168.275L29.4514 168.52ZM96.754 42.3232C94.9079 43.7877 92.9216 45.2815 90.8048 46.7916C96.2419 55.2195 97.7031 66.0447 93.7892 76.1099C87.3084 92.7765 68.5413 101.038 51.8714 94.5541C41.8095 90.6402 34.8167 82.2514 32.2726 72.5482C29.7319 73.0897 27.2857 73.5463 24.9602 73.9181C27.7554 85.1999 35.4429 95.0955 46.5779 100.353L41.4866 113.452C39.2589 112.587 36.7508 113.69 35.8832 115.917L17.103 164.218C15.681 167.878 17.3901 172.065 21.0235 173.546C24.7351 175.063 28.9556 173.24 30.4037 169.515L49.2296 121.107C50.0971 118.879 48.9915 116.371 46.7638 115.503L51.679 102.868C50.3776 102.291 49.1252 101.648 47.9184 100.95C48.3391 101.129 48.7599 101.306 49.1839 101.472C69.6731 109.44 92.7455 99.2899 100.71 78.7974C105.586 66.2534 103.672 52.744 96.7572 42.3232"
                    fill="#E4E4E4"
                />
                <path
                    d="M67.9083 89.7464C58.4758 91.3511 48.8411 87.5383 43.0714 79.8834C42.5039 79.13 41.4928 78.856 40.6285 79.2376C39.4282 79.766 39.0434 81.2728 39.8327 82.3198C46.5156 91.188 57.6702 95.6009 68.59 93.7386C69.8946 93.5168 70.6448 92.1339 70.1131 90.9238C69.738 90.0693 68.8281 89.5899 67.9083 89.7464Z"
                    fill="#E4E4E4"
                />
            </svg>
        </>
    );
};

export const LocationIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M12 2C14.3869 2 16.6761 2.94821 18.364 4.63604C20.0518 6.32387 21 8.61305 21 11C21 14.074 19.324 16.59 17.558 18.395C16.6757 19.2871 15.7129 20.0958 14.682 20.811L14.256 21.101L14.056 21.234L13.679 21.474L13.343 21.679L12.927 21.921C12.6446 22.0822 12.3251 22.1669 12 22.1669C11.6749 22.1669 11.3554 22.0822 11.073 21.921L10.657 21.679L10.137 21.359L9.945 21.234L9.535 20.961C8.42283 20.2085 7.3869 19.3491 6.442 18.395C4.676 16.589 3 14.074 3 11C3 8.61305 3.94821 6.32387 5.63604 4.63604C7.32387 2.94821 9.61305 2 12 2ZM12 4C10.1435 4 8.36301 4.7375 7.05025 6.05025C5.7375 7.36301 5 9.14348 5 11C5 13.322 6.272 15.36 7.871 16.996C8.55853 17.692 9.30166 18.3308 10.093 18.906L10.551 19.232C10.699 19.3353 10.8413 19.4313 10.978 19.52L11.368 19.77L11.711 19.979L12 20.148L12.455 19.879L12.822 19.649C13.0173 19.525 13.2263 19.386 13.449 19.232L13.907 18.906C14.6983 18.3308 15.4415 17.692 16.129 16.996C17.728 15.361 19 13.322 19 11C19 9.14348 18.2625 7.36301 16.9497 6.05025C15.637 4.7375 13.8565 4 12 4ZM12 7C13.0609 7 14.0783 7.42143 14.8284 8.17157C15.5786 8.92172 16 9.93913 16 11C16 12.0609 15.5786 13.0783 14.8284 13.8284C14.0783 14.5786 13.0609 15 12 15C10.9391 15 9.92172 14.5786 9.17157 13.8284C8.42143 13.0783 8 12.0609 8 11C8 9.93913 8.42143 8.92172 9.17157 8.17157C9.92172 7.42143 10.9391 7 12 7ZM12 9C11.4696 9 10.9609 9.21071 10.5858 9.58579C10.2107 9.96086 10 10.4696 10 11C10 11.5304 10.2107 12.0391 10.5858 12.4142C10.9609 12.7893 11.4696 13 12 13C12.5304 13 13.0391 12.7893 13.4142 12.4142C13.7893 12.0391 14 11.5304 14 11C14 10.4696 13.7893 9.96086 13.4142 9.58579C13.0391 9.21071 12.5304 9 12 9Z"
                    fill="#636363"
                />
            </svg>
        </>
    );
};

export const PersonIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M1.40332 12.0001C1.40332 9.18967 2.51977 6.49434 4.50705 4.50705C6.49434 2.51977 9.18967 1.40332 12.0001 1.40332C14.8106 1.40332 17.5059 2.51977 19.4932 4.50705C21.4805 6.49434 22.5969 9.18967 22.5969 12.0001C22.5969 14.8106 21.4805 17.5059 19.4932 19.4932C17.5059 21.4805 14.8106 22.5969 12.0001 22.5969C9.18967 22.5969 6.49434 21.4805 4.50705 19.4932C2.51977 17.5059 1.40332 14.8106 1.40332 12.0001ZM12.0001 2.92332C10.2408 2.92338 8.51939 3.43473 7.04534 4.39517C5.57129 5.3556 4.40812 6.72372 3.69735 8.33309C2.98658 9.94245 2.75883 11.7237 3.04182 13.4601C3.32481 15.1965 4.10634 16.8133 5.29132 18.1137C6.01061 16.9868 7.00241 16.0594 8.17503 15.4173C9.34764 14.7752 10.6632 14.4391 12.0001 14.4401C13.337 14.4391 14.6526 14.7752 15.8252 15.4173C16.9978 16.0594 17.9896 16.9868 18.7089 18.1137C19.8939 16.8133 20.6754 15.1965 20.9584 13.4601C21.2414 11.7237 21.0137 9.94245 20.3029 8.33309C19.5921 6.72372 18.429 5.3556 16.9549 4.39517C15.4809 3.43473 13.7595 2.92338 12.0001 2.92332ZM17.5713 19.1665C17.0055 18.1904 16.1929 17.3803 15.215 16.8175C14.2371 16.2547 13.1284 15.959 12.0001 15.9601C10.8719 15.959 9.76313 16.2547 8.78525 16.8175C7.80737 17.3803 6.99474 18.1904 6.42892 19.1665C8.02066 20.4074 9.98187 21.0799 12.0001 21.0769C14.0993 21.0769 16.0321 20.3633 17.5713 19.1665ZM8.24012 10.4081C8.24012 9.41091 8.63626 8.45454 9.3414 7.7494C10.0465 7.04426 11.0029 6.64812 12.0001 6.64812C12.9973 6.64812 13.9537 7.04426 14.6588 7.7494C15.364 8.45454 15.7601 9.41091 15.7601 10.4081C15.7601 11.4053 15.364 12.3617 14.6588 13.0668C13.9537 13.772 12.9973 14.1681 12.0001 14.1681C11.0029 14.1681 10.0465 13.772 9.3414 13.0668C8.63626 12.3617 8.24012 11.4053 8.24012 10.4081ZM12.0001 8.16812C11.706 8.16812 11.4147 8.22606 11.1429 8.33863C10.8711 8.4512 10.6242 8.6162 10.4162 8.8242C10.2082 9.03221 10.0432 9.27914 9.93063 9.55091C9.81806 9.82268 9.76012 10.114 9.76012 10.4081C9.76012 10.7023 9.81806 10.9936 9.93063 11.2653C10.0432 11.5371 10.2082 11.784 10.4162 11.992C10.6242 12.2 10.8711 12.365 11.1429 12.4776C11.4147 12.5902 11.706 12.6481 12.0001 12.6481C12.5942 12.6481 13.164 12.4121 13.584 11.992C14.0041 11.572 14.2401 11.0022 14.2401 10.4081C14.2401 9.81404 14.0041 9.24428 13.584 8.8242C13.164 8.40412 12.5942 8.16812 12.0001 8.16812Z"
                    fill="#636363"
                />
            </svg>
        </>
    );
};

export const DescriptionIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="18" viewBox="0 0 21 15" fill="none">
                <path
                    d="M0.75 0C0.551088 0 0.360322 0.0790178 0.21967 0.21967C0.0790176 0.360322 0 0.551088 0 0.75C0 0.948912 0.0790176 1.13968 0.21967 1.28033C0.360322 1.42098 0.551088 1.5 0.75 1.5H20.25C20.4489 1.5 20.6397 1.42098 20.7803 1.28033C20.921 1.13968 21 0.948912 21 0.75C21 0.551088 20.921 0.360322 20.7803 0.21967C20.6397 0.0790178 20.4489 0 20.25 0H0.75ZM0.75 4.5C0.551088 4.5 0.360322 4.57902 0.21967 4.71967C0.0790176 4.86032 0 5.05109 0 5.25C0 5.44891 0.0790176 5.63968 0.21967 5.78033C0.360322 5.92098 0.551088 6 0.75 6H20.25C20.4489 6 20.6397 5.92098 20.7803 5.78033C20.921 5.63968 21 5.44891 21 5.25C21 5.05109 20.921 4.86032 20.7803 4.71967C20.6397 4.57902 20.4489 4.5 20.25 4.5H0.75ZM0 9.75C0 9.55109 0.0790176 9.36032 0.21967 9.21967C0.360322 9.07902 0.551088 9 0.75 9H20.25C20.4489 9 20.6397 9.07902 20.7803 9.21967C20.921 9.36032 21 9.55109 21 9.75C21 9.94891 20.921 10.1397 20.7803 10.2803C20.6397 10.421 20.4489 10.5 20.25 10.5H0.75C0.551088 10.5 0.360322 10.421 0.21967 10.2803C0.0790176 10.1397 0 9.94891 0 9.75ZM0.75 13.5C0.551088 13.5 0.360322 13.579 0.21967 13.7197C0.0790176 13.8603 0 14.0511 0 14.25C0 14.4489 0.0790176 14.6397 0.21967 14.7803C0.360322 14.921 0.551088 15 0.75 15H14.25C14.4489 15 14.6397 14.921 14.7803 14.7803C14.921 14.6397 15 14.4489 15 14.25C15 14.0511 14.921 13.8603 14.7803 13.7197C14.6397 13.579 14.4489 13.5 14.25 13.5H0.75Z"
                    fill="#636363"
                />
            </svg>
        </>
    );
};

export const EyeIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                    d="M12 9C11.2044 9 10.4413 9.31607 9.87868 9.87868C9.31607 10.4413 9 11.2044 9 12C9 12.7956 9.31607 13.5587 9.87868 14.1213C10.4413 14.6839 11.2044 15 12 15C12.7956 15 13.5587 14.6839 14.1213 14.1213C14.6839 13.5587 15 12.7956 15 12C15 11.2044 14.6839 10.4413 14.1213 9.87868C13.5587 9.31607 12.7956 9 12 9ZM12 17C10.6739 17 9.40215 16.4732 8.46447 15.5355C7.52678 14.5979 7 13.3261 7 12C7 10.6739 7.52678 9.40215 8.46447 8.46447C9.40215 7.52678 10.6739 7 12 7C13.3261 7 14.5979 7.52678 15.5355 8.46447C16.4732 9.40215 17 10.6739 17 12C17 13.3261 16.4732 14.5979 15.5355 15.5355C14.5979 16.4732 13.3261 17 12 17ZM12 4.5C7 4.5 2.73 7.61 1 12C2.73 16.39 7 19.5 12 19.5C17 19.5 21.27 16.39 23 12C21.27 7.61 17 4.5 12 4.5Z"
                    fill="#007AFF"
                />
            </svg>
        </>
    );
};

export const UplaodIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="73" height="61" viewBox="0 0 73 61" fill="none">
                <g clip-path="url(#clip0_1076_10653)">
                    <path
                        d="M38.2705 28.8516C37.6064 28.3057 36.7136 28 35.7837 28C34.8537 28 33.9609 28.3057 33.2968 28.8516L22.6387 37.4906C22.2618 37.7514 21.953 38.0747 21.7318 38.4401C21.5106 38.8056 21.3817 39.2054 21.3532 39.6145C21.3247 40.0236 21.3972 40.4333 21.5661 40.8178C21.735 41.2024 21.9967 41.5536 22.3349 41.8495C22.673 42.1454 23.0804 42.3796 23.5314 42.5374C23.9824 42.6953 24.4674 42.7734 24.9561 42.7669C25.4448 42.7604 25.9266 42.6695 26.3715 42.4997C26.8163 42.33 27.2146 42.0851 27.5414 41.7803L32.1955 37.997V54.7985C32.1955 55.5886 32.5698 56.3463 33.236 56.9049C33.9023 57.4636 34.8059 57.7775 35.7481 57.7775C36.6904 57.7775 37.594 57.4636 38.2603 56.9049C38.9265 56.3463 39.3008 55.5886 39.3008 54.7985V38.146L43.8838 42.0187C44.2141 42.2979 44.607 42.5195 45.0399 42.6707C45.4728 42.822 45.9372 42.8998 46.4062 42.8998C46.8752 42.8998 47.3396 42.822 47.7725 42.6707C48.2054 42.5195 48.5983 42.2979 48.9286 42.0187C49.2616 41.7417 49.5259 41.4122 49.7063 41.0492C49.8866 40.6862 49.9795 40.2968 49.9795 39.9036C49.9795 39.5103 49.8866 39.1209 49.7063 38.7579C49.5259 38.3949 49.2616 38.0654 48.9286 37.7885L38.2705 28.8516Z"
                        fill="#D9D9D9"
                    />
                    <path
                        d="M55.7491 16.5491C54.305 12.5928 51.5961 9.16179 47.9994 6.73339C44.4027 4.30499 40.097 3 35.6816 3C31.2662 3 26.9606 4.30499 23.3639 6.73339C19.7672 9.16179 17.0583 12.5928 15.6141 16.5491C12.4343 16.9529 9.43005 18.1687 6.92279 20.0665C4.41553 21.9643 2.49957 24.4727 1.37986 27.3233C0.260153 30.1739 -0.0211717 33.2595 0.565979 36.25C1.15313 39.2405 2.58667 42.0234 4.71324 44.301C4.97011 44.73 5.32359 45.1001 5.7485 45.3848C6.1734 45.6695 6.65929 45.8618 7.17156 45.9482C7.68382 46.0345 8.20987 46.0127 8.71224 45.8843C9.2146 45.756 9.68093 45.5241 10.078 45.2053C10.4751 44.8866 10.7932 44.4887 11.0096 44.0401C11.226 43.5915 11.3355 43.1031 11.3301 42.6098C11.3248 42.1165 11.2048 41.6304 10.9787 41.1861C10.7526 40.7418 10.426 40.3502 10.0221 40.0392C8.65282 38.5877 7.75694 36.7909 7.44301 34.8663C7.12909 32.9418 7.41061 30.9723 8.25345 29.1964C9.0963 27.4205 10.4643 25.9146 12.1916 24.861C13.919 23.8074 15.9315 23.2515 17.9854 23.2605H18.3393C19.1673 23.2764 19.975 23.0164 20.6216 22.5259C21.2683 22.0353 21.7131 21.3453 21.8786 20.5759C22.5284 17.5416 24.2647 14.8135 26.7931 12.854C29.3216 10.8946 32.4868 9.82412 35.7524 9.82412C39.018 9.82412 42.1832 10.8946 44.7117 12.854C47.2401 14.8135 48.9764 17.5416 49.6262 20.5759C49.7917 21.3453 50.2365 22.0353 50.8832 22.5259C51.5299 23.0164 52.3375 23.2764 53.1655 23.2605H53.3778C55.4317 23.2515 57.4443 23.8074 59.1716 24.861C60.899 25.9146 62.2669 27.4205 63.1098 29.1964C63.9526 30.9723 64.2341 32.9418 63.9202 34.8663C63.6063 36.7909 62.7104 38.5877 61.3411 40.0392C61.0294 40.3707 60.7901 40.7576 60.6372 41.1774C60.4843 41.5972 60.4209 42.0416 60.4505 42.4849C60.4801 42.9281 60.6023 43.3614 60.8098 43.7596C61.0174 44.1577 61.3063 44.5129 61.6597 44.8044C62.3049 45.3439 63.1353 45.6421 63.9956 45.6433C64.4981 45.6427 64.9947 45.5406 65.4524 45.3439C65.9101 45.1472 66.3183 44.8603 66.65 44.5024C68.8356 42.2287 70.3198 39.429 70.9404 36.4095C71.561 33.3899 71.2941 30.2667 70.1688 27.3813C69.0436 24.4959 67.1034 21.9593 64.5603 20.0489C62.0171 18.1385 58.969 16.9277 55.7491 16.5491Z"
                        fill="#D9D9D9"
                    />
                </g>
                <defs>
                    <clipPath id="clip0_1076_10653">
                        <rect width="71.6292" height="61" fill="white" transform="translate(0.452637)" />
                    </clipPath>
                </defs>
            </svg>
        </>
    );
};

export const BarIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                <path
                    d="M12 2.16699C6.48 2.16699 2 6.64699 2 12.167C2 17.687 6.48 22.167 12 22.167C17.52 22.167 22 17.687 22 12.167C22 6.64699 17.52 2.16699 12 2.16699ZM13 17.167H11V11.167H13V17.167ZM13 9.16699H11V7.16699H13V9.16699Z"
                    fill="#993333"
                />
            </svg>
        </>
    );
};

export const SelectIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="8" viewBox="0 0 16 8" fill="none">
                <path d="M1 1.5L7.21905 6.83061C7.66844 7.2158 8.33156 7.2158 8.78095 6.83061L15 1.5" stroke="#2D2D2E" stroke-width="1.5" stroke-linecap="round" />
            </svg>
        </>
    );
};

export const DeletIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path
                    d="M16.0553 4.77079C16.2561 4.77079 16.4486 4.85054 16.5906 4.99249C16.7325 5.13445 16.8123 5.32697 16.8123 5.52773C16.8123 5.72848 16.7325 5.921 16.5906 6.06296C16.4486 6.20491 16.2561 6.28466 16.0553 6.28466H15.2984L15.2961 6.3384L14.5899 16.2322C14.5627 16.6142 14.3918 16.9716 14.1116 17.2326C13.8314 17.4935 13.4627 17.6386 13.0798 17.6386H6.91917C6.53627 17.6386 6.16759 17.4935 5.88739 17.2326C5.60718 16.9716 5.43628 16.6142 5.40909 16.2322L4.70288 6.33916L4.70136 6.28466H3.94443C3.74368 6.28466 3.55115 6.20491 3.4092 6.06296C3.26725 5.921 3.1875 5.72848 3.1875 5.52773C3.1875 5.32697 3.26725 5.13445 3.4092 4.99249C3.55115 4.85054 3.74368 4.77079 3.94443 4.77079H16.0553ZM13.7823 6.28466H6.2175L6.91993 16.1248H13.0798L13.7823 6.28466ZM11.5137 2.5C11.7145 2.5 11.907 2.57975 12.049 2.7217C12.1909 2.86365 12.2707 3.05618 12.2707 3.25693C12.2707 3.45768 12.1909 3.65021 12.049 3.79216C11.907 3.93411 11.7145 4.01386 11.5137 4.01386H8.48602C8.28527 4.01386 8.09274 3.93411 7.95079 3.79216C7.80884 3.65021 7.72909 3.45768 7.72909 3.25693C7.72909 3.05618 7.80884 2.86365 7.95079 2.7217C8.09274 2.57975 8.28527 2.5 8.48602 2.5H11.5137Z"
                    fill="#FF3B30"
                />
            </svg>
        </>
    );
};

export const MapIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M12 2C14.3869 2 16.6761 2.94821 18.364 4.63604C20.0518 6.32387 21 8.61305 21 11C21 14.074 19.324 16.59 17.558 18.395C16.6757 19.2871 15.7129 20.0958 14.682 20.811L14.256 21.101L14.056 21.234L13.679 21.474L13.343 21.679L12.927 21.921C12.6446 22.0822 12.3251 22.1669 12 22.1669C11.6749 22.1669 11.3554 22.0822 11.073 21.921L10.657 21.679L10.137 21.359L9.945 21.234L9.535 20.961C8.42283 20.2085 7.3869 19.3491 6.442 18.395C4.676 16.589 3 14.074 3 11C3 8.61305 3.94821 6.32387 5.63604 4.63604C7.32387 2.94821 9.61305 2 12 2ZM12 4C10.1435 4 8.36301 4.7375 7.05025 6.05025C5.7375 7.36301 5 9.14348 5 11C5 13.322 6.272 15.36 7.871 16.996C8.55853 17.692 9.30166 18.3308 10.093 18.906L10.551 19.232C10.699 19.3353 10.8413 19.4313 10.978 19.52L11.368 19.77L11.711 19.979L12 20.148L12.455 19.879L12.822 19.649C13.0173 19.525 13.2263 19.386 13.449 19.232L13.907 18.906C14.6983 18.3308 15.4415 17.692 16.129 16.996C17.728 15.361 19 13.322 19 11C19 9.14348 18.2625 7.36301 16.9497 6.05025C15.637 4.7375 13.8565 4 12 4ZM12 7C13.0609 7 14.0783 7.42143 14.8284 8.17157C15.5786 8.92172 16 9.93913 16 11C16 12.0609 15.5786 13.0783 14.8284 13.8284C14.0783 14.5786 13.0609 15 12 15C10.9391 15 9.92172 14.5786 9.17157 13.8284C8.42143 13.0783 8 12.0609 8 11C8 9.93913 8.42143 8.92172 9.17157 8.17157C9.92172 7.42143 10.9391 7 12 7ZM12 9C11.4696 9 10.9609 9.21071 10.5858 9.58579C10.2107 9.96086 10 10.4696 10 11C10 11.5304 10.2107 12.0391 10.5858 12.4142C10.9609 12.7893 11.4696 13 12 13C12.5304 13 13.0391 12.7893 13.4142 12.4142C13.7893 12.0391 14 11.5304 14 11C14 10.4696 13.7893 9.96086 13.4142 9.58579C13.0391 9.21071 12.5304 9 12 9Z"
                    fill="#636363"
                />
            </svg>
        </>
    );
};

export const UserIcon = ({ fillColor }: any) => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M1.40332 12.0001C1.40332 9.18967 2.51977 6.49434 4.50705 4.50705C6.49434 2.51977 9.18967 1.40332 12.0001 1.40332C14.8106 1.40332 17.5059 2.51977 19.4932 4.50705C21.4805 6.49434 22.5969 9.18967 22.5969 12.0001C22.5969 14.8106 21.4805 17.5059 19.4932 19.4932C17.5059 21.4805 14.8106 22.5969 12.0001 22.5969C9.18967 22.5969 6.49434 21.4805 4.50705 19.4932C2.51977 17.5059 1.40332 14.8106 1.40332 12.0001ZM12.0001 2.92332C10.2408 2.92338 8.51939 3.43473 7.04534 4.39517C5.57129 5.3556 4.40812 6.72372 3.69735 8.33309C2.98658 9.94245 2.75883 11.7237 3.04182 13.4601C3.32481 15.1965 4.10634 16.8133 5.29132 18.1137C6.01061 16.9868 7.00241 16.0594 8.17503 15.4173C9.34764 14.7752 10.6632 14.4391 12.0001 14.4401C13.337 14.4391 14.6526 14.7752 15.8252 15.4173C16.9978 16.0594 17.9896 16.9868 18.7089 18.1137C19.8939 16.8133 20.6754 15.1965 20.9584 13.4601C21.2414 11.7237 21.0137 9.94245 20.3029 8.33309C19.5921 6.72372 18.429 5.3556 16.9549 4.39517C15.4809 3.43473 13.7595 2.92338 12.0001 2.92332ZM17.5713 19.1665C17.0055 18.1904 16.1929 17.3803 15.215 16.8175C14.2371 16.2547 13.1284 15.959 12.0001 15.9601C10.8719 15.959 9.76313 16.2547 8.78525 16.8175C7.80737 17.3803 6.99474 18.1904 6.42892 19.1665C8.02066 20.4074 9.98187 21.0799 12.0001 21.0769C14.0993 21.0769 16.0321 20.3633 17.5713 19.1665ZM8.24012 10.4081C8.24012 9.41091 8.63626 8.45454 9.3414 7.7494C10.0465 7.04426 11.0029 6.64812 12.0001 6.64812C12.9973 6.64812 13.9537 7.04426 14.6588 7.7494C15.364 8.45454 15.7601 9.41091 15.7601 10.4081C15.7601 11.4053 15.364 12.3617 14.6588 13.0668C13.9537 13.772 12.9973 14.1681 12.0001 14.1681C11.0029 14.1681 10.0465 13.772 9.3414 13.0668C8.63626 12.3617 8.24012 11.4053 8.24012 10.4081ZM12.0001 8.16812C11.706 8.16812 11.4147 8.22606 11.1429 8.33863C10.8711 8.4512 10.6242 8.6162 10.4162 8.8242C10.2082 9.03221 10.0432 9.27914 9.93063 9.55091C9.81806 9.82268 9.76012 10.114 9.76012 10.4081C9.76012 10.7023 9.81806 10.9936 9.93063 11.2653C10.0432 11.5371 10.2082 11.784 10.4162 11.992C10.6242 12.2 10.8711 12.365 11.1429 12.4776C11.4147 12.5902 11.706 12.6481 12.0001 12.6481C12.5942 12.6481 13.164 12.4121 13.584 11.992C14.0041 11.572 14.2401 11.0022 14.2401 10.4081C14.2401 9.81404 14.0041 9.24428 13.584 8.8242C13.164 8.40412 12.5942 8.16812 12.0001 8.16812Z"
                    fill={fillColor ? fillColor : '#636363'}
                />
            </svg>
        </>
    );
};

export const DashIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="21" height="15" viewBox="0 0 21 15" fill="none">
                <path
                    d="M0.75 0C0.551088 0 0.360322 0.0790178 0.21967 0.21967C0.0790176 0.360322 0 0.551088 0 0.75C0 0.948912 0.0790176 1.13968 0.21967 1.28033C0.360322 1.42098 0.551088 1.5 0.75 1.5H20.25C20.4489 1.5 20.6397 1.42098 20.7803 1.28033C20.921 1.13968 21 0.948912 21 0.75C21 0.551088 20.921 0.360322 20.7803 0.21967C20.6397 0.0790178 20.4489 0 20.25 0H0.75ZM0.75 4.5C0.551088 4.5 0.360322 4.57902 0.21967 4.71967C0.0790176 4.86032 0 5.05109 0 5.25C0 5.44891 0.0790176 5.63968 0.21967 5.78033C0.360322 5.92098 0.551088 6 0.75 6H20.25C20.4489 6 20.6397 5.92098 20.7803 5.78033C20.921 5.63968 21 5.44891 21 5.25C21 5.05109 20.921 4.86032 20.7803 4.71967C20.6397 4.57902 20.4489 4.5 20.25 4.5H0.75ZM0 9.75C0 9.55109 0.0790176 9.36032 0.21967 9.21967C0.360322 9.07902 0.551088 9 0.75 9H20.25C20.4489 9 20.6397 9.07902 20.7803 9.21967C20.921 9.36032 21 9.55109 21 9.75C21 9.94891 20.921 10.1397 20.7803 10.2803C20.6397 10.421 20.4489 10.5 20.25 10.5H0.75C0.551088 10.5 0.360322 10.421 0.21967 10.2803C0.0790176 10.1397 0 9.94891 0 9.75ZM0.75 13.5C0.551088 13.5 0.360322 13.579 0.21967 13.7197C0.0790176 13.8603 0 14.0511 0 14.25C0 14.4489 0.0790176 14.6397 0.21967 14.7803C0.360322 14.921 0.551088 15 0.75 15H14.25C14.4489 15 14.6397 14.921 14.7803 14.7803C14.921 14.6397 15 14.4489 15 14.25C15 14.0511 14.921 13.8603 14.7803 13.7197C14.6397 13.579 14.4489 13.5 14.25 13.5H0.75Z"
                    fill="#636363"
                />
            </svg>
        </>
    );
};

export const CallNowIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64" fill="none">
                <path
                    d="M54.8224 3.39217C55.1959 3.19123 55.6339 3.14678 56.0401 3.26859C56.4463 3.3904 56.7875 3.66849 56.9888 4.04177C59.4977 8.69992 60.8075 13.9094 60.8 19.2002C60.8085 24.4907 59.4998 29.7001 56.992 34.3586C56.7909 34.7324 56.4495 35.0111 56.0429 35.1332C55.6363 35.2553 55.1979 35.2109 54.824 35.0098C54.4502 34.8086 54.1715 34.4672 54.0494 34.0606C53.9273 33.6541 53.9717 33.2156 54.1728 32.8418C56.4302 28.6497 57.608 23.9614 57.6 19.2002C57.608 14.4389 56.4302 9.75068 54.1728 5.55857C53.9719 5.18511 53.9274 4.74715 54.0492 4.34092C54.1711 3.9347 54.4492 3.59346 54.8224 3.39217ZM47.3824 9.70897C47.5764 9.62806 47.7843 9.58615 47.9945 9.58562C48.2046 9.58509 48.4128 9.62596 48.6071 9.70589C48.8015 9.78583 48.9782 9.90326 49.1271 10.0515C49.2761 10.1997 49.3943 10.3758 49.4752 10.5698C50.6152 13.3042 51.2015 16.2376 51.2 19.2002C51.2015 22.1627 50.6152 25.0961 49.4752 27.8306C49.4004 28.0328 49.2854 28.2179 49.1373 28.3746C48.9891 28.5313 48.8108 28.6564 48.613 28.7424C48.4153 28.8285 48.2022 28.8736 47.9865 28.8752C47.7709 28.8768 47.5572 28.8348 47.3582 28.7517C47.1592 28.6686 46.979 28.5462 46.8285 28.3917C46.6781 28.2372 46.5604 28.0539 46.4825 27.8528C46.4047 27.6517 46.3683 27.4369 46.3756 27.2214C46.3829 27.0058 46.4336 26.794 46.5248 26.5986C47.501 24.2543 48.0024 21.7396 48 19.2002C48 16.5762 47.4752 14.0802 46.5248 11.8018C46.3612 11.4105 46.3597 10.9703 46.5205 10.5779C46.6813 10.1855 46.9913 9.87293 47.3824 9.70897ZM16 19.2002C16 15.8054 17.3486 12.5497 19.7491 10.1492C22.1495 7.74874 25.4053 6.40017 28.8 6.40017C32.1948 6.40017 35.4505 7.74874 37.851 10.1492C40.2515 12.5497 41.6 15.8054 41.6 19.2002C41.6 22.5949 40.2515 25.8507 37.851 28.2511C35.4505 30.6516 32.1948 32.0002 28.8 32.0002C25.4053 32.0002 22.1495 30.6516 19.7491 28.2511C17.3486 25.8507 16 22.5949 16 19.2002ZM6.40002 41.6002C6.40002 38.0386 9.28962 35.2002 12.8288 35.2002H44.8C46.4974 35.2002 48.1253 35.8745 49.3255 37.0747C50.5257 38.2749 51.2 39.9028 51.2 41.6002C51.2 47.0114 48.5344 51.0914 44.368 53.7506C40.2656 56.365 34.736 57.6002 28.8 57.6002C22.864 57.6002 17.3344 56.365 13.232 53.7506C9.06563 51.0946 6.40002 47.0082 6.40002 41.6002Z"
                    fill="#2AB141"
                />
            </svg>
        </>
    );
};

export const SuccessfullyDeletedIcon = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="219" height="168" viewBox="0 0 219 168" fill="none">
                <path
                    d="M1.65088 57.9685C9.11959 55.4789 25.4313 54.3835 30.9283 69.9185C36.4253 85.4534 45.7662 87.1464 49.7496 86.0509"
                    stroke="#2AB141"
                    stroke-width="2.46922"
                    stroke-linecap="round"
                />
                <path d="M169.25 87.781C176.719 85.2914 193.03 84.196 198.527 99.731C204.024 115.266 213.365 116.959 217.349 115.863" stroke="#2AB141" stroke-width="2.46922" stroke-linecap="round" />
                <path
                    d="M101.714 35.0389C104.35 31.7787 109.32 31.7787 111.956 35.0389L117.495 41.8912C119.132 43.9168 121.81 44.7868 124.325 44.1105L132.834 41.8227C136.883 40.7342 140.904 43.6559 141.12 47.8425L141.573 56.6419C141.707 59.2431 143.362 61.5209 145.795 62.4522L154.023 65.6027C157.938 67.1017 159.474 71.8291 157.188 75.343L152.383 82.7284C150.962 84.9117 150.962 87.7271 152.383 89.9104L157.188 97.2958C159.474 100.81 157.938 105.537 154.023 107.036L145.795 110.187C143.362 111.118 141.707 113.396 141.573 115.997L141.12 124.796C140.904 128.983 136.883 131.905 132.834 130.816L124.325 128.528C121.81 127.852 119.132 128.722 117.495 130.748L111.956 137.6C109.32 140.86 104.35 140.86 101.714 137.6L96.1752 130.748C94.5377 128.722 91.8601 127.852 89.3447 128.528L80.8359 130.816C76.7875 131.905 72.7661 128.983 72.5503 124.796L72.0968 115.997C71.9627 113.396 70.3078 111.118 67.8753 110.187L59.6468 107.036C55.7317 105.537 54.1957 100.81 56.482 97.2958L61.2872 89.9104C62.7077 87.7271 62.7077 84.9117 61.2872 82.7284L56.482 75.343C54.1957 71.829 55.7317 67.1017 59.6468 65.6027L67.8753 62.4522C70.3078 61.5209 71.9627 59.2431 72.0968 56.6419L72.5503 47.8425C72.7661 43.6559 76.7875 40.7342 80.8359 41.8227L89.3447 44.1105C91.8601 44.7868 94.5377 43.9168 96.1752 41.8912L101.714 35.0389Z"
                    fill="#2AB141"
                />
                <path d="M121.202 76.4434L101.448 96.1971L92.4688 87.2182" stroke="white" stroke-width="4.11537" stroke-linecap="round" stroke-linejoin="round" />
                <path
                    d="M172.438 29.4953C172.696 27.9234 174.956 27.9234 175.215 29.4953L176.016 34.3574L180.878 35.1582C182.45 35.4171 182.45 37.6772 180.878 37.9361L176.016 38.7369L175.215 43.599C174.956 45.1709 172.696 45.1708 172.438 43.599L171.637 38.7369L166.775 37.9361C165.203 37.6772 165.203 35.4171 166.775 35.1582L171.637 34.3574L172.438 29.4953Z"
                    fill="#FFCC00"
                />
                <path
                    d="M40.6106 121.399C40.8565 119.906 43.0036 119.906 43.2495 121.399L44.0104 126.018L48.6293 126.779C50.1226 127.025 50.1226 129.172 48.6293 129.418L44.0104 130.179L43.2495 134.798C43.0036 136.291 40.8565 136.291 40.6106 134.798L39.8497 130.179L35.2308 129.418C33.7375 129.172 33.7375 127.025 35.2308 126.779L39.8497 126.018L40.6106 121.399Z"
                    fill="#FFCC00"
                />
                <path
                    d="M36.4025 33.419C36.5838 32.3187 38.1658 32.3187 38.3471 33.419L38.9077 36.8224L42.3111 37.383C43.4114 37.5642 43.4114 39.1463 42.3111 39.3275L38.9077 39.8881L38.3471 43.2916C38.1658 44.3919 36.5838 44.3919 36.4025 43.2916L35.8419 39.8881L32.4385 39.3275C31.3382 39.1463 31.3382 37.5642 32.4385 37.383L35.8419 36.8224L36.4025 33.419Z"
                    fill="#FFCC00"
                />
                <path
                    d="M180.351 137.567C180.533 136.467 182.115 136.467 182.296 137.567L182.856 140.971L186.26 141.531C187.36 141.713 187.36 143.295 186.26 143.476L182.856 144.037L182.296 147.44C182.115 148.54 180.533 148.54 180.351 147.44L179.791 144.037L176.387 143.476C175.287 143.295 175.287 141.713 176.387 141.531L179.791 140.971L180.351 137.567Z"
                    fill="#FFCC00"
                />
                <circle cx="175.831" cy="69.179" r="3.2923" fill="#2AB141" />
                <circle cx="92.4686" cy="3.2923" r="3.2923" fill="#2AB141" />
                <circle cx="138.372" cy="164.058" r="3.2923" fill="#2AB141" />
                <circle cx="74.8511" cy="153.999" r="2.05769" fill="#2AB141" />
            </svg>
        </>
    );
};
export const CloseIconModel = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M19.71 5.70905L18.2901 4.28906L12 10.5891L5.71002 4.28906L4.29004 5.70905L10.59 11.9991L4.29004 18.2891L5.71002 19.7091L12 13.4091L18.2901 19.7091L19.71 18.2891L13.41 11.9991L19.71 5.70905Z"
                fill="#636363"
            />
        </svg>
    );
};
export const DeleteMessageDashboradIcon = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
            <path
                d="M15 3.75C15.1989 3.75 15.3897 3.82902 15.5303 3.96967C15.671 4.11032 15.75 4.30109 15.75 4.5C15.75 4.69891 15.671 4.88968 15.5303 5.03033C15.3897 5.17098 15.1989 5.25 15 5.25H14.25L14.2477 5.30325L13.548 15.1065C13.5211 15.4849 13.3517 15.8391 13.0741 16.0977C12.7965 16.3563 12.4311 16.5 12.0518 16.5H5.9475C5.5681 16.5 5.2028 16.3563 4.92516 16.0977C4.64753 15.8391 4.47819 15.4849 4.45125 15.1065L3.7515 5.304L3.75 5.25H3C2.80109 5.25 2.61032 5.17098 2.46967 5.03033C2.32902 4.88968 2.25 4.69891 2.25 4.5C2.25 4.30109 2.32902 4.11032 2.46967 3.96967C2.61032 3.82902 2.80109 3.75 3 3.75H15ZM12.7477 5.25H5.25225L5.94825 15H12.0518L12.7477 5.25ZM10.5 1.5C10.6989 1.5 10.8897 1.57902 11.0303 1.71967C11.171 1.86032 11.25 2.05109 11.25 2.25C11.25 2.44891 11.171 2.63968 11.0303 2.78033C10.8897 2.92098 10.6989 3 10.5 3H7.5C7.30109 3 7.11032 2.92098 6.96967 2.78033C6.82902 2.63968 6.75 2.44891 6.75 2.25C6.75 2.05109 6.82902 1.86032 6.96967 1.71967C7.11032 1.57902 7.30109 1.5 7.5 1.5H10.5Z"
                fill={'#FF3B30'}
            />
        </svg>
    );
};

export const HelpIcon = ({ fillColor }: any) => {
    return (
        <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="SVGRepo_bgCarrier" stroke-width="0" />

            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />

            <g id="SVGRepo_iconCarrier">
                {' '}
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12ZM12 9C11.7015 9 11.4344 9.12956 11.2497 9.33882C10.8843 9.75289 10.2523 9.79229 9.83827 9.42683C9.4242 9.06136 9.3848 8.42942 9.75026 8.01535C10.2985 7.3942 11.1038 7 12 7C13.6569 7 15 8.34315 15 10C15 11.3072 14.1647 12.4171 13 12.829V13C13 13.5523 12.5523 14 12 14C11.4477 14 11 13.5523 11 13V12.5C11 11.6284 11.6873 11.112 12.2482 10.9692C12.681 10.859 13 10.4655 13 10C13 9.44772 12.5523 9 12 9ZM12 15C11.4477 15 11 15.4477 11 16C11 16.5523 11.4477 17 12 17H12.01C12.5623 17 13.01 16.5523 13.01 16C13.01 15.4477 12.5623 15 12.01 15H12Z"
                    fill={fillColor ? fillColor : '#636363'}
                />{' '}
            </g>
        </svg>
    );
};

export const LogoutIcon = ({ fillColor }: any) => {
    return (
        <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="SVGRepo_bgCarrier" stroke-width="0" />

            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />

            <g id="SVGRepo_iconCarrier">
                {' '}
                <path
                    d="M17.2929 14.2929C16.9024 14.6834 16.9024 15.3166 17.2929 15.7071C17.6834 16.0976 18.3166 16.0976 18.7071 15.7071L21.6201 12.7941C21.6351 12.7791 21.6497 12.7637 21.6637 12.748C21.87 12.5648 22 12.2976 22 12C22 11.7024 21.87 11.4352 21.6637 11.252C21.6497 11.2363 21.6351 11.2209 21.6201 11.2059L18.7071 8.29289C18.3166 7.90237 17.6834 7.90237 17.2929 8.29289C16.9024 8.68342 16.9024 9.31658 17.2929 9.70711L18.5858 11H13C12.4477 11 12 11.4477 12 12C12 12.5523 12.4477 13 13 13H18.5858L17.2929 14.2929Z"
                    fill={fillColor}
                />{' '}
                <path
                    d="M5 2C3.34315 2 2 3.34315 2 5V19C2 20.6569 3.34315 22 5 22H14.5C15.8807 22 17 20.8807 17 19.5V16.7326C16.8519 16.647 16.7125 16.5409 16.5858 16.4142C15.9314 15.7598 15.8253 14.7649 16.2674 14H13C11.8954 14 11 13.1046 11 12C11 10.8954 11.8954 10 13 10H16.2674C15.8253 9.23514 15.9314 8.24015 16.5858 7.58579C16.7125 7.4591 16.8519 7.35296 17 7.26738V4.5C17 3.11929 15.8807 2 14.5 2H5Z"
                    fill={fillColor}
                />{' '}
            </g>
        </svg>
    );
};

export const SendIcon = () => {
    return (
        <svg width="25" height="25" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M11.5003 12H5.41872M5.24634 12.7972L4.24158 15.7986C3.69128 17.4424 3.41613 18.2643 3.61359 18.7704C3.78506 19.21 4.15335 19.5432 4.6078 19.6701C5.13111 19.8161 5.92151 19.4604 7.50231 18.7491L17.6367 14.1886C19.1797 13.4942 19.9512 13.1471 20.1896 12.6648C20.3968 12.2458 20.3968 11.7541 20.1896 11.3351C19.9512 10.8529 19.1797 10.5057 17.6367 9.81135L7.48483 5.24303C5.90879 4.53382 5.12078 4.17921 4.59799 4.32468C4.14397 4.45101 3.77572 4.78336 3.60365 5.22209C3.40551 5.72728 3.67772 6.54741 4.22215 8.18767L5.24829 11.2793C5.34179 11.561 5.38855 11.7019 5.407 11.8459C5.42338 11.9738 5.42321 12.1032 5.40651 12.231C5.38768 12.375 5.34057 12.5157 5.24634 12.7972Z"
                stroke="#636363"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
        </svg>
    );
};

export const EyeIconPassword = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
                d="M3.70703 2.29297L2.29297 3.70703L5.06836 6.48242C4.52925 6.92191 4.04856 7.39001 3.61914 7.85938C1.78914 9.89937 1 12 1 12C1 12 4 20 12 20C13.2 20 14.2895 19.8198 15.2695 19.5098C15.9886 19.29 16.6486 19.0016 17.248 18.6621L20.293 21.707L21.707 20.293L3.70703 2.29297ZM12 4C10.8 4 9.71047 4.18023 8.73047 4.49023L10.3906 6.15039C10.8906 6.05039 11.43 6 12 6C17.28 6 19.9405 10.27 20.8105 12C20.4805 12.66 19.8709 13.7007 18.9609 14.7207L20.3809 16.1406C22.2109 14.1006 23 12 23 12C23 12 20 4 12 4ZM6.48828 7.90234L8.55859 9.97266C8.19945 10.5721 8 11.2609 8 12C8 12.08 7.99977 12.17 8.00977 12.25C8.12977 14.26 9.74 15.8702 11.75 15.9902C11.83 16.0002 11.92 16 12 16C12.7391 16 13.4279 15.8006 14.0273 15.4414L15.7578 17.1719C15.1084 17.4814 14.3885 17.7198 13.6094 17.8496C13.0994 17.9496 12.57 18 12 18C6.72 18 4.05945 13.73 3.18945 12C3.51945 11.34 4.11906 10.2993 5.03906 9.2793C5.44851 8.80992 5.93918 8.34184 6.48828 7.90234ZM12.25 8.00977L15.9902 11.75C15.8702 9.74 14.26 8.12977 12.25 8.00977ZM10.0703 11.4844L12.5156 13.9297C12.3565 13.9789 12.1787 14 12 14C10.9 14 10 13.1 10 12C10 11.8213 10.0211 11.6435 10.0703 11.4844Z"
                fill="#111011"
            />
        </svg>
    );
};

export const MultiUsersIcon = ({ fillColor }: any) => {
    return (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
                d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21"
                stroke={fillColor || '#1D7EB6'}
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z"
                stroke={fillColor || '#1D7EB6'}
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13"
                stroke={fillColor || '#1D7EB6'}
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88"
                stroke={fillColor || '#1D7EB6'}
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    );
};

export const ReferralsIcon = ({ fillColor }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
            <g fill="none" stroke={fillColor ? fillColor : '#636363'} stroke-linecap="round" stroke-linejoin="round" stroke-width="2">
                <path d="m16 11l2 2l4-4m-6 12v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                <circle cx="9" cy="7" r="4" />
            </g>
        </svg>
    );
};

export const LeadManagementIcon = ({ className, fillColor }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill={fillColor || '#000'} width="24px" height="24px" viewBox="0 0 32 32" version="1.1">
            <title>users</title>
            <path d="M16 21.916c-4.797 0.020-8.806 3.369-9.837 7.856l-0.013 0.068c-0.011 0.048-0.017 0.103-0.017 0.16 0 0.414 0.336 0.75 0.75 0.75 0.357 0 0.656-0.25 0.731-0.585l0.001-0.005c0.875-3.885 4.297-6.744 8.386-6.744s7.511 2.859 8.375 6.687l0.011 0.057c0.076 0.34 0.374 0.59 0.732 0.59 0 0 0.001 0 0.001 0h-0c0.057-0 0.112-0.007 0.165-0.019l-0.005 0.001c0.34-0.076 0.59-0.375 0.59-0.733 0-0.057-0.006-0.112-0.018-0.165l0.001 0.005c-1.045-4.554-5.055-7.903-9.849-7.924h-0.002zM9.164 10.602c0 0 0 0 0 0 2.582 0 4.676-2.093 4.676-4.676s-2.093-4.676-4.676-4.676c-2.582 0-4.676 2.093-4.676 4.676v0c0.003 2.581 2.095 4.673 4.675 4.676h0zM9.164 2.75c0 0 0 0 0 0 1.754 0 3.176 1.422 3.176 3.176s-1.422 3.176-3.176 3.176c-1.754 0-3.176-1.422-3.176-3.176v0c0.002-1.753 1.423-3.174 3.175-3.176h0zM22.926 10.602c2.582 0 4.676-2.093 4.676-4.676s-2.093-4.676-4.676-4.676c-2.582 0-4.676 2.093-4.676 4.676v0c0.003 2.581 2.095 4.673 4.675 4.676h0zM22.926 2.75c1.754 0 3.176 1.422 3.176 3.176s-1.422 3.176-3.176 3.176c-1.754 0-3.176-1.422-3.176-3.176v0c0.002-1.753 1.423-3.174 3.176-3.176h0zM30.822 19.84c-0.878-3.894-4.308-6.759-8.406-6.759-0.423 0-0.839 0.031-1.246 0.089l0.046-0.006c-0.049 0.012-0.092 0.028-0.133 0.047l0.004-0.002c-0.751-2.129-2.745-3.627-5.089-3.627-2.334 0-4.321 1.485-5.068 3.561l-0.012 0.038c-0.017-0.004-0.030-0.014-0.047-0.017-0.359-0.053-0.773-0.084-1.195-0.084-0.002 0-0.005 0-0.007 0h0c-4.092 0.018-7.511 2.874-8.392 6.701l-0.011 0.058c-0.011 0.048-0.017 0.103-0.017 0.16 0 0.414 0.336 0.75 0.75 0.75 0.357 0 0.656-0.25 0.731-0.585l0.001-0.005c0.737-3.207 3.56-5.565 6.937-5.579h0.002c0.335 0 0.664 0.024 0.985 0.070l-0.037-0.004c-0.008 0.119-0.036 0.232-0.036 0.354 0.006 2.987 2.429 5.406 5.417 5.406s5.411-2.419 5.416-5.406v-0.001c0-0.12-0.028-0.233-0.036-0.352 0.016-0.002 0.031 0.005 0.047 0.001 0.294-0.044 0.634-0.068 0.98-0.068 0.004 0 0.007 0 0.011 0h-0.001c3.379 0.013 6.203 2.371 6.93 5.531l0.009 0.048c0.076 0.34 0.375 0.589 0.732 0.59h0c0.057-0 0.112-0.007 0.165-0.019l-0.005 0.001c0.34-0.076 0.59-0.375 0.59-0.733 0-0.057-0.006-0.112-0.018-0.165l0.001 0.005zM16 18.916c-0 0-0 0-0.001 0-2.163 0-3.917-1.753-3.917-3.917s1.754-3.917 3.917-3.917c2.163 0 3.917 1.754 3.917 3.917 0 0 0 0 0 0.001v-0c-0.003 2.162-1.754 3.913-3.916 3.916h-0z" />
        </svg>
    );
};


export const CreateIcon = () => {
    return (
        <>
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                className="lucide lucide-plus mr-2 h-4 w-4"
                data-lov-id="src/components/blog/BlogHeader.tsx:27:10"
                data-lov-name="Plus"
                data-component-path="src/components/blog/BlogHeader.tsx"
                data-component-line="27"
                data-component-file="BlogHeader.tsx"
                data-component-name="Plus"
                data-component-content="%7B%22className%22%3A%22w-4%20h-4%20mr-2%22%7D"
            >
                <path d="M5 12h14"></path>
                <path d="M12 5v14"></path>
            </svg>
        </>
    );
};

export const BlogIcon = ({ fillColor }: any) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            className="lucide lucide-book-open"
        >
            <path d="M12 7v14"></path>
            <path d="M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z"></path>
        </svg>
    );
};

export const SettingsIcon = ({ fillColor }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
            <g fill={fillColor ? fillColor : '#636363'} fill-rule="evenodd" clip-rule="evenodd">
                <path d="M12 8.25a3.75 3.75 0 1 0 0 7.5a3.75 3.75 0 0 0 0-7.5M9.75 12a2.25 2.25 0 1 1 4.5 0a2.25 2.25 0 0 1-4.5 0" />
                <path d="M11.975 1.25c-.445 0-.816 0-1.12.02a2.8 2.8 0 0 0-.907.19a2.75 2.75 0 0 0-1.489 1.488c-.145.35-.184.72-.2 1.122a.87.87 0 0 1-.415.731a.87.87 0 0 1-.841-.005c-.356-.188-.696-.339-1.072-.389a2.75 2.75 0 0 0-2.033.545a2.8 2.8 0 0 0-.617.691c-.17.254-.356.575-.578.96l-.025.044c-.223.385-.408.706-.542.98c-.14.286-.25.568-.29.88a2.75 2.75 0 0 0 .544 2.033c.231.301.532.52.872.734a.87.87 0 0 1 .426.726a.87.87 0 0 1-.426.726c-.34.214-.64.433-.872.734a2.75 2.75 0 0 0-.545 2.033c.041.312.15.594.29.88c.135.274.32.595.543.98l.025.044c.222.385.408.706.578.96c.177.263.367.5.617.69a2.75 2.75 0 0 0 2.033.546c.376-.05.716-.2 1.072-.389a.87.87 0 0 1 .84-.005a.86.86 0 0 1 .417.731c.015.402.054.772.2 1.122a2.75 2.75 0 0 0 1.488 1.489c.************.907.188c.304.021.675.021 1.12.021h.05c.445 0 .816 0 1.12-.02c.318-.022.617-.069.907-.19a2.75 2.75 0 0 0 1.489-1.488c.145-.35.184-.72.2-1.122a.87.87 0 0 1 .415-.732a.87.87 0 0 1 .841.006c.356.188.696.339 1.072.388a2.75 2.75 0 0 0 2.033-.544c.25-.192.44-.428.617-.691c.17-.254.356-.575.578-.96l.025-.044c.223-.385.408-.706.542-.98c.14-.286.25-.569.29-.88a2.75 2.75 0 0 0-.544-2.033c-.231-.301-.532-.52-.872-.734a.87.87 0 0 1-.426-.726c0-.278.152-.554.426-.726c.34-.214.64-.433.872-.734a2.75 2.75 0 0 0 .545-2.033a2.8 2.8 0 0 0-.29-.88a18 18 0 0 0-.543-.98l-.025-.044a18 18 0 0 0-.578-.96a2.8 2.8 0 0 0-.617-.69a2.75 2.75 0 0 0-2.033-.546c-.376.05-.716.2-1.072.389a.87.87 0 0 1-.84.005a.87.87 0 0 1-.417-.731c-.015-.402-.054-.772-.2-1.122a2.75 2.75 0 0 0-1.488-1.489c-.29-.12-.59-.167-.907-.188c-.304-.021-.675-.021-1.12-.021zm-1.453 1.595c.077-.032.194-.061.435-.078c.247-.017.567-.017 1.043-.017s.796 0 1.043.017c.241.017.358.046.435.078c.307.127.55.37.677.677c.04.096.073.247.086.604c.03.792.439 1.555 1.165 1.974s1.591.392 2.292.022c.316-.167.463-.214.567-.227a1.25 1.25 0 0 1 .924.247c.066.051.15.138.285.338c.139.206.299.483.537.895s.397.69.506.912c.107.217.14.333.15.416a1.25 1.25 0 0 1-.247.924c-.064.083-.178.187-.48.377c-.672.422-1.128 1.158-1.128 1.996s.456 1.574 1.128 1.996c.302.19.416.294.48.377c.202.263.29.595.247.924c-.01.083-.044.2-.15.416c-.109.223-.268.5-.506.912s-.399.689-.537.895c-.135.2-.219.287-.285.338a1.25 1.25 0 0 1-.924.247c-.104-.013-.25-.06-.567-.227c-.7-.37-1.566-.398-2.292.021s-1.135 1.183-1.165 1.975c-.013.357-.046.508-.086.604a1.25 1.25 0 0 1-.677.677c-.077.032-.194.061-.435.078c-.247.017-.567.017-1.043.017s-.796 0-1.043-.017c-.241-.017-.358-.046-.435-.078a1.25 1.25 0 0 1-.677-.677c-.04-.096-.073-.247-.086-.604c-.03-.792-.439-1.555-1.165-1.974s-1.591-.392-2.292-.022c-.316.167-.463.214-.567.227a1.25 1.25 0 0 1-.924-.247c-.066-.051-.15-.138-.285-.338a17 17 0 0 1-.537-.895c-.238-.412-.397-.69-.506-.912c-.107-.217-.14-.333-.15-.416a1.25 1.25 0 0 1 .247-.924c.064-.083.178-.187.48-.377c.672-.422 1.128-1.158 1.128-1.996s-.456-1.574-1.128-1.996c-.302-.19-.416-.294-.48-.377a1.25 1.25 0 0 1-.247-.924c.01-.083.044-.2.15-.416c.109-.223.268-.5.506-.912s.399-.689.537-.895c.135-.2.219-.287.285-.338a1.25 1.25 0 0 1 .924-.247c.104.013.25.06.567.227c.7.37 1.566.398 2.292-.022c.726-.419 1.135-1.182 1.165-1.974c.013-.357.046-.508.086-.604c.127-.307.37-.55.677-.677" />
            </g>
        </svg>
    );
};

export const DiscountIcon = ({ className, fillColor }: any) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" className={className || 'h-5 w-5'}  width="24" height="24" viewBox="0 0 24 24">
            <path fill="currentColor" d="M12.79 21L3 11.21v2c0 .53.21 1.04.59 1.41l7.79 7.79c.78.78 2.05.78 2.83 0l6.21-6.21c.78-.78.78-2.05 0-2.83z" />
            <path
                fill={fillColor ? fillColor : '#636363'}
                d="M11.38 17.41c.39.39.9.59 1.41.59s1.02-.2 1.41-.59l6.21-6.21c.78-.78.78-2.05 0-2.83L12.62.58C12.25.21 11.74 0 11.21 0H5C3.9 0 3 .9 3 2v6.21c0 .53.21 1.04.59 1.41zM5 2h6.21L19 9.79L12.79 16L5 8.21z"
            />
            <circle cx="7.25" cy="4.25" r="1.25" fill={fillColor ? fillColor : '#636363'} />
        </svg>
    );
};
