'use client';
import SearchInput from '@/components/reusable/SearchBar';
import SearchDropDown from '@/components/reusable/SearchDropDown';
import { leadTypeOptions, statusOptions } from '@/store/utils.ts/functions';
import React from 'react';

interface FiltersProps {
    onSearchChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onStatusChange?: (status: string) => void;
    onTypeChange?: (type: string) => void;
    selectedStatus?: string;
    selectedType?: string;
}

export const Filters: React.FC<FiltersProps> = ({ onSearchChange, onStatusChange, onTypeChange, selectedStatus, selectedType }) => {
    return (
        <div className="mx-5 my-5 mb-8 rounded border border-gray-200 px-5 py-5">
            <div className="pb-3 text-2xl font-semibold">Filters</div>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                <div className="w-full">
                    <SearchInput placeholder="Search documents..." onChange={(e) => onSearchChange?.(e)} />
                </div>
                <div className="w-full">
                    <SearchDropDown classes="!h-14 w-full" dropdownOptions={statusOptions} initail={selectedStatus} setSelectedStatus={(val: any) => onStatusChange?.(val)} />
                </div>
                <div className="w-full">
                    <SearchDropDown classes="!h-14 w-full" dropdownOptions={leadTypeOptions} initail={selectedType} setSelectedStatus={(val: any) => onTypeChange?.(val)} />
                </div>
            </div>
        </div>
    );
};

// LeadCard Component
interface LeadCardProps {
    name: string;
    company?: string;
    type: 'Agent' | 'Agency';
    email: string;
    phone: string;
    addedDate: string;
    lastContactDate?: string;
    source: string;
}

export const LeadCard = ({ name, company, type, email, phone, addedDate, lastContactDate, source }: LeadCardProps) => {
    return (
        <div className="mb-4 rounded-lg border border-gray-200 p-4 transition-shadow hover:shadow-md">
            <div className="flex items-start justify-between">
                <div>
                    <h3 className="text-lg font-medium text-gray-900">{name}</h3>
                    {company && <p className="text-gray-700">{company}</p>}
                </div>
                <span className={`rounded-full px-2 py-1 text-xs ${type === 'Agent' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}`}>{type}</span>
            </div>

            <div className="mt-3 grid grid-cols-1 gap-2 sm:grid-cols-2">
                <div className="text-sm">
                    <p className="text-gray-500">Email</p>
                    <p className="text-gray-900">{email}</p>
                </div>
                <div className="text-sm">
                    <p className="text-gray-500">Phone</p>
                    <p className="text-gray-900">{phone}</p>
                </div>
                <div className="text-sm">
                    <p className="text-gray-500">Added</p>
                    <p className="text-gray-900">{addedDate}</p>
                </div>
                {lastContactDate && (
                    <div className="text-sm">
                        <p className="text-gray-500">Last contact</p>
                        <p className="text-gray-900">{lastContactDate}</p>
                    </div>
                )}
            </div>

            <div className="mt-3 border-t border-gray-100 pt-3">
                <p className="text-sm text-gray-500">
                    Source: <span className="text-gray-900">{source}</span>
                </p>
            </div>
        </div>
    );
};

// Example usage in your page
/*
<Filters />
<LeadCard
  name="Robert Martinez"
  company="Maritinez Real Estate"
  type="Agent"
  email="<EMAIL>"
  phone="******-0101"
  addedDate="1/20/2025"
  source="Website Form"
/>
<LeadCard
  name="Premium Properties LLC"
  type="Agency"
  email="<EMAIL>"
  phone="******-0102"
  addedDate="1/19/2025"
  lastContactDate="1/20/2025"
  source="Cold Outreach"
/>
*/
