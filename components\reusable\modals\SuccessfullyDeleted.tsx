'use client';
import { CloseIconModel, SuccessfullyDeletedIcon } from '@/components/icon/Icon';

const SuccessfullyDeleted = ({ onClose ,title ,desc,actionButtonText }: any) => {
    const handleDelete = () => {
        onClose(true);
    };
    return (
        <>
            <div className=" w-full">
                <div className=" relative flex items-center justify-between ">
                    <div className=" relative flex items-start justify-start "></div>
                    <div className=" relative flex items-end justify-end ">
                        <span className="cursor-pointer   " onClick={handleDelete}>
                            <CloseIconModel />
                        </span>
                    </div>
                </div>
                <div className="h-100 flex flex-col justify-between">
                    <div className=" ">
                        <div className='flex justify-center items-center pb-5'>

                        <SuccessfullyDeletedIcon />
                        </div>
                        <div className=" py-2 text-center">
                            <p className="font-inter text-[26px] font-semibold  text-[#993333]">{title}
                            </p>
                            <p className="justify-start  self-stretch pt-5 text-center font-inter text-base font-normal leading-normal text-[#636363]">{desc}</p>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default SuccessfullyDeleted;
