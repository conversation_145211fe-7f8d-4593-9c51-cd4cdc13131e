'use client';
import React from 'react';
import { useRouter } from 'next/navigation';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import BreadCrumButton from '@/components/reusable/BreadCrumButton';
import UserManagementTable from './UserManagementTable';

const UserTableLayout = () => {
    const { push } = useRouter();

    return (
        <DefaultPageLayout>
            <BreadCrums
                mainHeading="User Management"
                breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'User Management' }]}
                ButonComponent={
                    <BreadCrumButton
                        onClick={() => {
                            push('/user-management');
                        }}
                    />
                }
            />
            <div className="px-4">
                <UserManagementTable />
            </div>
        </DefaultPageLayout>
    );
};

export default UserTableLayout;
