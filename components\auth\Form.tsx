'use client';

import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import * as z from 'zod';
import IconEye from '../icon/icon-eye';
import ReusebleButton from '../reusable/Button';
import { CloseIcon, LogoIcon } from '../icon/Icon';
import { useRouter } from 'next/navigation';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import { showMessage } from '@/app/lib/Alert';
import { useDispatch } from 'react-redux';
import { AppDispatch, IRootState } from '@/store';
import { storeCookies } from '@/app/lib/cookies';
import { useSelector } from 'react-redux';
import { fetchUserProfile } from '@/store/utils.ts/userActions';

const loginSchema = z.object({
    email: z
        .string()
        .min(1, 'Email is required')
        .email('Invalid email address'),

    password: z
        .string()
        .min(1, 'Password is required'),
});

export default function LoginForm() {
    const [showPassword, setShowPassword] = useState(false);
    const { push } = useRouter();

    const formik = useFormik({
        initialValues: {
            email: '',
            password: '',
        },
        validate: (values) => {
            try {
                loginSchema.parse(values);
                return {};
            } catch (error: any) {
                const fieldErrors = error.formErrors?.fieldErrors;

                if (fieldErrors) {
                    // Only return the first error encountered
                    const firstFieldWithError = Object.keys(fieldErrors)[0];
                    return {
                        [firstFieldWithError]: fieldErrors[firstFieldWithError]?.[0],
                    };
                }

                return {};
            }
        },
        onSubmit: (values) => {
            handleLogin(values.email, values.password);
        },
    });

    const dispatch = useDispatch<AppDispatch>();

    const handleLogin = (email: string, password: string) => {
        try {
            const myHeaders = new Headers();
            myHeaders.append('Content-Type', 'application/json');

            const raw = JSON.stringify({ email, password });

            const requestOptions: RequestInit = {
                method: 'POST',
                headers: myHeaders,
                body: raw,
                redirect: 'follow',
                credentials: 'include',
            };

            fetch(API_ENDPOINTS.LOGIN, requestOptions)
                .then((response) => response.json())
                .then((result) => {
                    if (result.success) {
                        showMessage(result?.message, 'success');
                        const dataJson = result?.data;
                        storeCookies('adminUserId', dataJson?.user_id.toString());
                        //@ts-ignore
                        dispatch(fetchUserProfile(() => router.push('/login')));
                        push('/');
                    } else {
                        showMessage(result?.message, 'error');
                    }
                })
                .catch((error) => {
                    console.error(error);
                    showMessage('Something went wrong', 'error');
                });
        } catch (error) {
            console.log(error);
            showMessage('Something went wrong', 'error');
        }
    };
    const { userSession } = useSelector((state: IRootState) => state.user);
    useEffect(() => {
        if (userSession) {
            push('');
        }
    }, []);

    return (
        <div className="mx-auto h-full w-full max-w-md p-6 font-inter">
            <div className="mb-8 text-center">
                <div className="flex items-center justify-center pb-5 md:pb-10">
                    <LogoIcon />
                </div>
                <h1 className="mb-2 text-3xl   text-[26px] font-semibold ">
                    <span className="text-[#993333]">Log In </span>

                    <span className="text-[#2d2d2e]">to Your Account</span>
                </h1>
                <p className="text-center text-base font-normal leading-normal text-[#636363]">Please enter your email and password to access the admin panel.</p>
            </div>

            <form onSubmit={formik.handleSubmit} className="space-y-6" noValidate>
                <div className="space-y-1">
                    <input
                        type="email"
                        name="email"
                        placeholder="Email"
                        value={formik.values.email}
                        onChange={formik.handleChange}
                        className={`w-full rounded-lg px-4 py-3 ${formik.errors.email ? 'bg-[#FFF5F5]' : 'bg-white'} border border-gray-200 outline-none transition-colors focus:border-blue-500`}
                    />
                    {formik.errors.email && <div className="mt-1 text-sm text-[#E53E3E]">{formik.errors.email}</div>}
                </div>

                <div className="space-y-1">
                    <div className="relative">
                        <input
                            type={showPassword ? 'text' : 'password'}
                            name="password"
                            placeholder="Password"
                            value={formik.values.password}
                            onChange={formik.handleChange}
                            className={`w-full rounded-lg px-4 py-3 ${formik.errors.password ? 'bg-[#FFF5F5]' : 'bg-white'
                                } border border-gray-200 outline-none transition-colors focus:border-blue-500`}
                        />
                        <button type="button" onClick={() => setShowPassword(!showPassword)} className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600">
                            {!showPassword ? <IconEye className="h-5 w-5" /> : <CloseIcon />}
                        </button>
                    </div>
                    {formik.errors.password && <div className="mt-1 text-sm text-[#E53E3E]">{formik.errors.password}</div>}
                </div>

                {/* <div className="flex items-end justify-end">
                    <Link href="/forgot-password" className="inline-flex   items-center justify-center gap-2.5 rounded p-[3px]">
                        <span className="text-center   text-lg font-medium leading-normal text-[#993333]">Forgot Password?</span>
                    </Link>
                </div> */}

                <ReusebleButton text="Login" className="w-full" onClick={formik.handleSubmit} />
            </form>
        </div>
    );
}
