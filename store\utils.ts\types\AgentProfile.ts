export enum AGENT_ACCOUNT_TYPE {
    INDIVIDUAL = "Individual",
    COMPANY_OR_AGENCY = "Company/Agency/PropertyDeveloper",
}

export interface Industry {
    id: number;
    group: string | null;
    name: string;
    description: string;
    typeId: number;
    statusId: number;
    createdBy: number;
    createdOn: string;
    modifiedOn: string | null;
    parentId: number | null;
}

export interface AgentProfile {
    id: number;
    code: string | null;
    profileAccountType: string;
    firstName: string;
    middleName: string;
    lastName: string;
    localName: string | null;
    emiratesId: any[]; // Replace `any` if you know the structure
    nationalityId: number | null;
    locationId: number | null;
    address: string | null;
    email: string;
    phone: string;
    typeId: number | null;
    statusId: number | null;
    createdBy: number | null;
    createdOn: string;
    modifiedBy: number | null;
    modifiedOn: string | null;
    designation: string | null;
    shortDescription: string | null;
    description: string | null;
    specialization: string | null;
    experience: string | null;
    issuingAuthority: string | null;
    issuingAuthorityOther: string | null;
    languages: string | null;
    industry: string | null;
    certified: boolean;
    certificateNumber: string | null;
    expiryDate: string | null;
    contactNumber: string | null;
    whatsappContact: string | null;
    contactEmail: string | null;
    cardHolderName: string | null;
    cardType: string | null;
    cardNumber: string | null;
    accountType: string;
    association: boolean;
    issuedBy: string | null;
    profileImage: string | null;
    isProfileCompleted: boolean;
    profileId: number;
    username: string;
    lastLogin: string | null;
    loginCount: number;
    isActivated: boolean;
    otp: string | null;
    expireOn: string | null;
    accountId: number | null;
    agent_id: number | null;
    profile_id: number;
    industryMission: string | null;
    operationArea: any[];
    industrySubCategory: string | null;
    specializationMission: string | null;
    yearOfExperience: string | null;
    otherForFreelancer: string | null;
    employerName: string | null;
    position: string | null;
    employedLocation: string | null;
    nationality: string;
    summary: string | null;
    workType: string;
    profilePhotos: string[];
    licenseDocs: string[] | null;
    freelancePermitDocs: string[];
    tradeLicenseDocs: string[] | null;
    employmentLetters: string[] | null;
    certifications: string[] | null;
    areaCovered: string | null;
    created_at: string;
    updated_at: string;
    workTypeCategory: string | null;
    servicesOffered: string | null;
    gender: string;
    personalWebsite: string | null;
    facebook: string | null;
    instagram: string | null;
    linkedin: string | null;
    twitter: string | null;
    youtube: string | null;
    personalIdDoc: string[];
    passportDoc: string[];
    supportingDocsDoc: string[];
    companyEmail: string | null;
    companyPhone: string | null;
    hasFreelancerPermit: boolean;
    freelancerLicenseNumber: string;
    freelancerLicenseNumberExpiryDate: string;
    freelancerLicenseAuthority: string;
    freelancerLicenseAuthorityOther: string | null;
    agentRole: string;
    hasLicense: boolean;
    termsAgree: boolean;
    inviteAgents?: string;
    accuracyConfirm: boolean;
    communicationConsent: boolean;
    licenseAuthority: string | null;
    employmentProof: string[];
    visa: string[] | null;
    passport: string[];
    name: string | null;
    accountTypeId: number | null;
    verified: boolean;
    badge: string | null;
    packageTypeId: number | null;
    serviceId: number | null;
    licenseTypeId: number | null;
    licenseNo: string;
    licenseIssueDate: string | null;
    licenseExpiredDate: string;
    dldAdm: string | null;
    industry_mission?: Industry[];
    industry_subcategory?: Industry[];
    licenseDoc: string[];
    visaDoc: string[];
    currentStatusName: string;
    isLicensed: boolean;
    agentlicenses: any[];
    positionOther: string | null;
    companyrole: any[];
    referenceded: string | null;
    licenseTag: string;
    industryMissionOther:string
}

export const IndividualAgentFieldLabels: Record<string, string> = {
    // Personal Info
    firstName: "First Name",
    middleName: "Middle Name",
    lastName: "Last Name",
    gender: "Gender",
    phone: "Phone Number",
    nationality: "Nationality",
    profilePhotos: "Profile Image",

    // Work Status
    // workType: "Work Status",

    // Company / Freelancer Info
    // employerName: "Company Name",
    // companyEmail: "Company Email",
    // companyPhone: "Company Phone",
    // hasFreelancerPermit: "Has Freelancer Permit",
    // freelancerLicenseAuthority: "Freelancer License Authority",
    // freelancerLicenseAuthorityOther: "Freelancer License Authority Other",
    // expiryDate: "Freelancer License Expiry Date",
    // freelancerLicenseNumber: "Freelancer License Number",
    // employmentProof: "Employment Proof",
    // freelancePermitDocs: "Freelance Permit",

    // Industry & Specialization
    industry_mission: "Primary Industry",
    industry_subcategory: "Specialization",

    // Agent Role
    // agentRole: "Agent Type",

    // Licensing & Credentials
    hasLicense: "Has license or permit or broker card",
    licenseAuthority: "License Authority",
    licenseExpiredDate: "License Expiry Date",
    licenseNo: "License Number",
    licenseDocs: "Licence Doc",

    // Final Documents
    emiratesId: "National ID Card",
    passport: "Passport",
    visa: "Visa",
    accuracyConfirm: "Accuracy Confirmation",
    communicationConsent: "Communication Consent",
    termsAgree: "Terms Agreement",
};

export const CompanyAgentFieldLabels: Record<string, string> = {
    // Business Info
    name: "Company Name",
    companyEmail: "Company Email",
    companyPhone: "Company Phone",
    companyLogo: "Company Logo",
    operationArea: "Emirate of Operation",
    nationality: "Nationality",
    // Industry & Specialization
    industry_mission: "Primary Industry",
    industry_mission_other: "Primary Industry Other",
    tradeLicenseActivities: "Trade License Activities",

    // Business Registration
    licenseNo: "License Number",
    licenseFile: "License File",
    // licenseIssueDate: "License Issue Date",
    licenseExpiredDate: "License Expiry Date",
    // issuingAuthority: "Issuing Authority",
    // issuingAuthorityOther: "Issuing Authority Other",
    // tradeLicenseDocs: "License Documents",

    // Representative Info
  
    // middleName: "Middle Name",
    // lastName: "Last Name",
    // phone: "Phone",
    designation: "Designation",
    // representativeEmail: "Representative Email", // Assuming it's in the data model but not rendered yet
    // profilePhotos: "Profile Image",

    emiratesId: "Emirates ID",
    passportDoc: "Passport Doc",
    visaDoc: "Visa",
    supportingDocsDoc: "Supportive Docs",

    // Team
    // inviteAgents: "Invite Agents",
    invitedAgents: "Invited Agents",
    accuracyConfirm: "Accuracy Confirmation", 
    termsAgree: "Terms Agreement",
    referalId:"Referral ID"
};

export interface DocumentCards {
    id: number;
    type: string;
    count: string;
    color?: string
}
