import React from 'react';

const Heading = ({ title1, title2, description, reverse ,classes}: any) => {
    return (
        <div className={`py-3  ${classes}`}>
            <div className="inline-flex flex-col items-start justify-start font-inter">
                {reverse ? (
                    <h4>
                        <span className="text-[26px] font-semibold md:leading-[58px] leading-[40px] text-[#993333] ">{title1} </span>
                        <span className="  text-[26px] font-semibold md:leading-[58px] leading-[40px] text-[#2d2d2e]">{title2}</span>
                    </h4>
                ) : (
                    <h4>
                        <span className="text-[26px] font-semibold md:leading-[58px] leading-[40px] text-[#2d2d2e]">{title1} </span>
                        <span className="  text-[26px] font-semibold md:leading-[58px] leading-[40px] text-[#993333]">{title2}</span>
                    </h4>
                )}
                <p className="text-base font-normal leading-7   text-[#636363]">{description}</p>
            </div>
        </div>
    );
};

export default Heading;
