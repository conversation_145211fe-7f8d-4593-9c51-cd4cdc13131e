import { FC } from 'react';

interface CursorIconProps {
    className?: string;
    fill?: boolean;
}

const CursorIcon: FC<CursorIconProps> = ({ className, fill = false }) => {
    return (
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
            <g id="fluent:cursor-20-filled">
                <path id="Vector" d="M3.88889 1.80862C3.80938 1.74309 3.71288 1.70152 3.61064 1.68876C3.5084 1.67599 3.40464 1.69256 3.31146 1.73653C3.21828 1.7805 3.13953 1.85006 3.08438 1.93709C3.02923 2.02412 2.99997 2.12504 3 2.22807V9.83365C3 10.3373 3.62483 10.5699 3.95409 10.1895L5.8688 7.97654C5.94531 7.88819 6.03993 7.81733 6.14624 7.76877C6.25255 7.72021 6.36806 7.69508 6.48494 7.69509H9.52326C10.0329 7.69509 10.2622 7.05613 9.86881 6.7323L3.88889 1.80862Z" fill="#EDF5F9" />
            </g>
        </svg>
    );
};
export default CursorIcon;