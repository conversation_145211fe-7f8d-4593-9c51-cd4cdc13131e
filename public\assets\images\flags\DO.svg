<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>DO</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#083D7A" offset="0%"></stop>
            <stop stop-color="#032F61" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#DF1E35" offset="0%"></stop>
            <stop stop-color="#CC162C" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="DO">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-2)" x="0" y="0" width="9" height="6"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-2)" x="12" y="9" width="9" height="6"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-3)" x="0" y="9" width="9" height="6"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-3)" x="12" y="0" width="9" height="6"></rect>
            <path d="M9,6 L0,6 L0,9 L9,9 L9,15 L12,15 L12,9 L21,9 L21,6 L12,6 L12,0 L9,0 L9,6 Z" id="Rectangle-2" fill="url(#linearGradient-1)"></path>
            <path d="M10.5,6 C9.67157288,6 9,6.67157288 9,7.5 C9,8.32842712 9.67157288,9 10.5,9 L10.5,9 C11.3284271,9 12,8.32842712 12,7.5" id="Oval-85" fill-opacity="0.2" fill="#C93127" transform="translate(10.500000, 7.500000) rotate(-45.000000) translate(-10.500000, -7.500000) "></path>
            <circle id="Oval-85" fill="#042F60" cx="10.5" cy="7.5" r="1"></circle>
            <path d="M11.5606602,8.56066017 C10.9748737,9.14644661 10.0251263,9.14644661 9.43933983,8.56066017 C8.85355339,7.97487373 8.85355339,7.02512627 9.43933983,6.43933983 L9.79289322,6.79289322 C9.40236893,7.18341751 9.40236893,7.81658249 9.79289322,8.20710678 C10.1834175,8.59763107 10.8165825,8.59763107 11.2071068,8.20710678 C11.5976311,7.81658249 11.5976311,7.18341751 11.2071068,6.79289322 L11.5606602,6.43933983 C12.1464466,7.02512627 12.1464466,7.97487373 11.5606602,8.56066017 Z M11.5606602,8.56066017 C10.9748737,9.14644661 10.0251263,9.14644661 9.43933983,8.56066017 C8.85355339,7.97487373 8.85355339,7.02512627 9.43933983,6.43933983 L9.79289322,6.79289322 C9.40236893,7.18341751 9.40236893,7.81658249 9.79289322,8.20710678 C10.1834175,8.59763107 10.8165825,8.59763107 11.2071068,8.20710678 C11.5976311,7.81658249 11.5976311,7.18341751 11.2071068,6.79289322 L11.5606602,6.43933983 C12.1464466,7.02512627 12.1464466,7.97487373 11.5606602,8.56066017 Z" id="Oval-85" fill="#0F6D1A" fill-rule="nonzero"></path>
        </g>
    </g>
</svg>