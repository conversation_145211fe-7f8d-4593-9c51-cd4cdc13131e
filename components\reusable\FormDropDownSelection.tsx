'use client'; // Mark as client component to use hooks

import React, { useState, useEffect, useRef } from 'react';

interface Option {
    label: string;
    value: string;
}

interface SearchDropDownProps {
    options: Option[];
    classes?: string;
    placeholder?: string;
    onSelect?: (value: string) => void;
    defaultValue?: any;
}

const FormDropDownSelection = ({ options, classes = '', placeholder = 'Select Option', onSelect, defaultValue }: SearchDropDownProps) => {
    const [isOpen, setIsOpen] = useState(false);
    const [selected, setSelected] = useState(defaultValue);
    const dropdownRef = useRef<HTMLDivElement>(null);

    const toggleDropdown = () => setIsOpen(!isOpen);

    const selectOption = (value: string) => {
        setSelected(value);
        setIsOpen(false);
        if (onSelect) onSelect(value);
    };

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);
    useEffect(() => {
        const data = options.find((option) => option.value === defaultValue);
        if (data) {
            setSelected(data.value);
        }
    });
    return (
        <div ref={dropdownRef} className={`relative inline-block w-full ${classes}`}>
            <div
                onClick={toggleDropdown}
                className={`flex h-14 cursor-pointer items-center justify-between gap-2 rounded-md border border-[#E4E4E4] bg-white p-2 font-inter ${isOpen ? 'border-lightBlue' : ''}`}
            >
                <p className={`ps-3 ${selected ? 'text-sm font-normal text-black md:text-base capitalize' : 'text-sm font-normal text-neutral-400 md:text-base'}`}>
                    {selected ? options.find((option) => option.value === selected)?.label : placeholder}
                </p>
                <svg
                    className={`h-4 w-4 transform transition-transform ${isOpen ? 'rotate-180' : 'rotate-0'}`}
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
            </div>

            {isOpen && (
                <div className="border-borderColor shadow-shadowcustom absolute top-[50px] z-10 mt-2 w-full rounded border bg-white">
                    <ul className="scrollbar-main-22 max-h-40 overflow-y-scroll py-2">
                        {options.map((option, index) => (
                            <li
                                key={index}
                                className={`hover:text-lightBlue cursor-pointer px-4 py-2 capitalize hover:bg-gray-200 ${selected === option.value ? 'bg-gray-100' : ''}`}
                                onClick={() => selectOption(option.value)}
                            >
                                {option.label}
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
};

export default FormDropDownSelection;
