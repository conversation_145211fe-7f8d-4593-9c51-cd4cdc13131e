'use client';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import React from 'react';
import UserTableData from './UserTableData';

const UserTable = () => {

    return (
        <>
            <DefaultPageLayout>
                <BreadCrums mainHeading="Leads" breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Leads' }]}
                />
                <div className="px-4">
                    <UserTableData />
                </div>
            </DefaultPageLayout>
        </>
    );
};

export default UserTable;
