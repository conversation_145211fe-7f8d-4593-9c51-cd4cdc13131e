import { showMessage } from '@/app/lib/Alert';
import React, { useState } from 'react';

import Select from 'react-select';

const MultiSelection = ({ handleSelectProps, options, placeholder, selectedValuesList, disabled }: any) => {
    const [selectedValues, setSelectedValues] = useState(selectedValuesList ? selectedValuesList : []);

    const handleSelect = (selectedOptions: any) => {
        if (disabled && selectedValuesList.length > 3) {
            showMessage('You can select maximum 3 options', 'warning');
            return;
        }
        const values = selectedOptions ? selectedOptions.map((option: any) => option.value) : [];
        setSelectedValues(values);
        handleSelectProps(values);
    };
    return (
        <div>
            {' '}
            <Select
                isMulti
                name="test"
                value={options.filter((option: any) => selectedValues.includes(option.value))}
                onChange={handleSelect}
                styles={customStylesStudy}
                options={options}
                className="basic-multi-select rounded-[10px] shadow-sm"
                classNamePrefix="select"
                placeholder={placeholder}
                isOptionDisabled={() => disabled}
            />
        </div>
    );
};

export default MultiSelection;

export const customStylesStudy = {
    control: (provided: any) => ({
        ...provided,
        borderRadius: '0.4rem',
        padding: '0.5rem 0.5rem',

        boxShadow: 'none',
        '&:hover': {
            borderColor: '#e8eef2',
        },
        cursor: 'pointer',
    }),
    valueContainer: (provided: any) => ({
        ...provided,
        paddingLeft: '0.5rem',
    }),
    placeholder: (provided: any) => ({
        ...provided,
        color: '#9ca3af',
    }),
    singleValue: (provided: any) => ({
        ...provided,
        color: '#111827',
    }),
    dropdownIndicator: (provided: any) => ({
        ...provided,
        color: '#6b7280',
        '&:hover': {
            color: '#6b7280',
        },
    }),
    menu: (provided: any) => ({
        ...provided,
        borderRadius: '0.75rem',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    }),
    option: (provided: any, state: any) => ({
        ...provided,
        padding: '0.5rem 1rem',
        backgroundColor: state.isSelected ? '#f3f4f6' : state.isFocused ? '#f9fafb' : '#ffffff',
        color: '#111827',
        '&:hover': {
            backgroundColor: '#f9fafb',
        },
    }),
};
