'use client'

import Image from "next/image"
import InputField from "@/components/reusable/InputField"
import { ButtonBorder } from "@/components/reusable/ButtonBorder"
import LabelInput from "@/components/reusable/LabelInput"
import { InfoIcon, RedInfoIcon } from "@/components/icon/Icon"
import images from "@/public/assets/images/main/agent-image.png"
import { useState } from "react"
import { AgentProfile } from "@/store/utils.ts/types/AgentProfile"
import { MultiSelectDropdown } from "@/components/reusable/MultiSelectDropdown"

const ProfileInfo = ({ getProfileData }: any) => {

    const [profleData, setProfileData] = useState<AgentProfile>(getProfileData);

    const industryOptions = profleData?.industry_mission?.map(industry => ({
        label: industry.name,
        value: industry.id,
    }));

    const typeOptions = profleData?.industry_subcategory?.map(type => ({
        label: type.name,
        value: type.id,
    }));

    // These can also be your default selected values:
    const selectedIndustries = industryOptions;
    const selectedTypes = typeOptions;

    return (
        <>
            <div className="flex p-5 md:flex-row flex-col gap-4 md:gap-0">
                <div className="md:w-2/3 flex flex-col gap-6">
                    <div>
                        <p className="text-lg font-bold pl-3 pb-3">Profile Information</p>

                        <div className="flex flex-wrap">
                            <div className="w-full px-2 py-2">
                                <InputField
                                    id="account-name"
                                    label="Account Name*"
                                    value={profleData?.firstName? profleData?.firstName + " " + profleData?.middleName + " " + profleData?.lastName:""}
                                    placeholder="Input"
                                    disable={true}
                                />
                            </div>
                            <div className="md:w-1/2 w-full px-2 py-2">
                                <InputField
                                    id="username"
                                    label="Username*"
                                    value={profleData?.username}
                                    placeholder="Input"
                                    disable={true}
                                />
                            </div>
                            {/* <div className="md:w-1/2 w-full px-2 py-2">
                                <InputField
                                    id="account-type"
                                    label="Account Type*"
                                    value={profleData.accountType === AGENT_ACCOUNT_TYPE.INDIVIDUAL ? 'Agent' : 'Agency'}
                                    placeholder="Input"
                                    disable={true}
                                />
                            </div> */}
                            <div className="md:w-1/2 w-full px-2 py-2">
                                <InputField
                                    id="contact"
                                    label="Contact*"
                                    value={profleData?.phone}
                                    placeholder="Input"
                                    disable={true}
                                />
                            </div>
                            <div className="md:w-1/2 w-full px-2 py-2">
                                <InputField
                                    id="email"
                                    label="Email*"
                                    value={profleData?.email}
                                    placeholder="Input"
                                    disable={true}
                                />
                            </div>
                            <div className="md:w-1/2 w-full px-2 py-2">
                                <InputField
                                    id="location"
                                    label="Location*"
                                    value={profleData?.nationality}
                                    placeholder="Input"
                                />
                            </div>
                            <div className="w-full px-2 py-2">
                                <InputField
                                    id="short-description"
                                    label="Short Description"
                                    value={profleData?.shortDescription || ""}
                                    placeholder="Input"
                                    disable={true}
                                />
                            </div>
                            <div className="w-full px-2 py-2">
                                <InputField
                                    id="address-line-1"
                                    label="Address Line 1*"
                                    value={profleData?.address || ""}
                                    placeholder="Input"
                                    disable={true}
                                />
                            </div>
                        </div>
                    </div>
                    <div>
                        <p className="text-lg font-bold pl-3 pb-3">Account Type and Association</p>
                        <div className="flex flex-wrap">
                            <div className="md:w-1/2 w-full px-2 py-2">
                                <MultiSelectDropdown
                                    label="Select industry*"
                                    data={industryOptions}
                                    value={selectedIndustries}
                                    disabled={true}
                                />
                            </div>
                            <div className="md:w-1/2 w-full px-2 py-2">
                                <MultiSelectDropdown
                                    label="Select Type*"
                                    data={typeOptions}
                                    value={selectedTypes}
                                    disabled={true}
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div className="md:w-1/3 pl-5">
                    <div className="md:max-w-[80%] ml-auto md:block flex flex-col gap-3 items-center">
                        <Image
                            src={profleData.profilePhotos[0] || images}
                            alt="agent-image"
                            className="h-full"
                            width={300}
                            height={300}
                            layout="intrinsic"
                        />
                        {/* <div className="pt-3 md:w-full w-auto">
                            <ButtonBorder value="Upload Photo" />
                        </div>
                        <div className="flex gap-2 text-sm items-center text-grayText pl-2 py-3">
                            <RedInfoIcon className="w-5 h-5" />
                            <p>Upload 500x500 pixel images.</p>
                        </div> */}
                    </div>
                </div>
            </div>
            <div className="p-5">
                <p className="text-lg font-bold pl-3 pb-3">License/Compliance Information</p>
                <div className="flex flex-wrap">
                    <div className="md:w-1/3 w-full px-2 py-2">
                        <LabelInput
                            id="username"
                            placeholder="License Number*"
                            value={profleData?.licenseNo || profleData.freelancerLicenseNumber || ""}
                            icon={<InfoIcon className="w-5 h-5" />}
                            disable={true}
                        />
                    </div>
                    <div className="md:w-1/3 w-full px-2 py-2">
                        <LabelInput
                            id="username"
                            placeholder="License Authority*"
                            value={profleData?.freelancerLicenseAuthority || profleData?.licenseAuthority ||""}
                            icon={<InfoIcon className="w-5 h-5" />}
                            disable={true}
                        />
                    </div>
                    <div className="md:w-1/3 w-full px-2 py-2">
                        <LabelInput
                            id="username"
                            placeholder="License Issue Date*"
                            value={
                                profleData?.licenseIssueDate
                                    ? new Date(profleData.licenseIssueDate).toLocaleDateString('en-GB', {
                                        day: '2-digit',
                                        month: 'short',
                                        year: 'numeric'
                                    })
                                    : ""
                            }
                            disable={true}
                            icon={<InfoIcon className="w-5 h-5" />}
                        />
                    </div>
                    <div className="md:w-1/3 w-full px-2 py-2">
                        <LabelInput
                            id="username"
                            placeholder="License Expiry Date*"
                            value={
                                profleData?.licenseExpiredDate || profleData?.freelancerLicenseNumberExpiryDate
                                    ? new Date(profleData.licenseExpiredDate || profleData.freelancerLicenseNumberExpiryDate).toLocaleDateString('en-GB', {
                                        day: '2-digit',
                                        month: 'short',
                                        year: 'numeric'
                                    })
                                    : ""
                            }
                            disable={true}
                            icon={<InfoIcon className="w-5 h-5" />}
                        />
                    </div>
                </div>
            </div>
        </>
    )
}

export default ProfileInfo;
