import React, { useEffect, useState } from 'react';

interface MarkAsLostPopupProps {
    isOpen: boolean;
    onClose: () => void;
    onMarkAsLost: (id: number, reason: string) => void; // reuse for reopen too
    companyName: string;
    id: number;
    isReopen?: boolean;
}

const MarkAsLostPopup: React.FC<MarkAsLostPopupProps> = ({ isOpen, onClose, onMarkAsLost, companyName, id, isReopen = false }) => {
    /* ───────────────────────── state & reset ───────────────────────── */
    const [isSubmitted, setIsSubmitted] = useState(false);

    useEffect(() => {
        if (isOpen) setIsSubmitted(false);
    }, [isOpen]);

    /* ───────────────────────── constants that depend on mode ───────────────────────── */
    const actionLabel = isReo<PERSON> ? 'Re-open Lead' : 'Mark as Lost';
    const confirmColor = isReopen ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700';
    const confirmDisabledColor = isReopen ? 'bg-green-400' : 'bg-red-400';
    const bannerClass = isReopen ? 'bg-green-50 text-green-600' : 'bg-red-50 text-red-600';
    const bannerText = isReopen ? 'This will move the lead back to an re-opened status.' : 'This will change the lead status to "Lost". You can re-open it later if needed.';

    /* ───────────────────────── submit ───────────────────────── */
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitted(true);

        const reason = isReopen
            ? '' // reason ignored when reopening
            : (new FormData(e.target as HTMLFormElement).get('reason') as string);

        onMarkAsLost(id, reason);
    };

    /* ───────────────────────── render ───────────────────────── */
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 font-inter">
            <div className="relative w-full max-w-md rounded-lg bg-white">
                {/* close button */}
                <button onClick={onClose} className="absolute right-3 top-3 text-gray-400 hover:text-gray-600">
                    ×
                </button>

                <div className="p-6">
                    <h2 className="mb-2 text-lg font-medium">{actionLabel}</h2>
                    <p className="mb-6 text-sm text-gray-600">{isReopen ? `Re-open ${companyName} lead?` : `Mark ${companyName} as a lost lead?`}</p>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        {/* Reason textarea — only for “lost” */}
                        {!isReopen && (
                            <div>
                                <label htmlFor="reason" className="mb-1 block text-sm font-medium text-gray-700">
                                    Reason (optional)
                                </label>
                                <textarea
                                    id="reason"
                                    name="reason"
                                    placeholder="Why is this lead being marked as lost?"
                                    rows={3}
                                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                                />
                            </div>
                        )}

                        {/* Info / warning banner */}
                        <div className={`rounded-md p-3 ${bannerClass}`}>
                            <p className="text-sm">{bannerText}</p>
                        </div>

                        {/* Action buttons */}
                        <div className="mt-6 flex justify-end gap-3">
                            <button type="button" onClick={onClose} className="rounded-md border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                                Cancel
                            </button>
                            <button
                                type="submit"
                                disabled={isSubmitted}
                                className={`rounded-md px-4 py-2 text-sm font-medium text-white ${isSubmitted ? `cursor-not-allowed ${confirmDisabledColor}` : confirmColor}`}
                            >
                                {isSubmitted ? 'Submitting…' : actionLabel}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default MarkAsLostPopup;
