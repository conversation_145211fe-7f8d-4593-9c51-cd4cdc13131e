'use client';
import { CloseIconModel } from '@/components/icon/Icon';

const WarningModal = ({ onClose, onSubmit, title, desc, actionButtonText = 'Yes' }: any) => {
    const handleConfirm = () => {
        onSubmit(true);
    };

    return (
        <div className="mx-auto w-full max-w-md">
            {/* Modal Header */}
            <div className="flex justify-end p-2">
                <button onClick={() => onClose(false)} className="text-gray-500 hover:text-gray-700">
                    <CloseIconModel />
                </button>
            </div>

            {/* Modal Content */}
            <div className="flex flex-col items-center justify-center px-6 pb-6 text-center">
                {/* Warning Icon */}
                <div>
                    <svg className="h-14 w-14 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-.01-8a9 9 0 110 18 9 9 0 010-18z" />
                    </svg>
                </div>

                {/* Title */}
                <h2 className="mt-1 break-words text-lg font-semibold text-[#CC6600]">{title}</h2>

                {/* Description */}
                <p className="mt-3 text-sm leading-relaxed text-[#555]">{desc}</p>

                {/* Action Button */}
                <button onClick={handleConfirm} className="mt-6 w-32 rounded bg-[#CC6600] px-4 py-2 text-white hover:bg-[#b35300]">
                    {actionButtonText}
                </button>
            </div>
        </div>
    );
};

export default WarningModal;
