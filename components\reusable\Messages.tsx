import React from 'react';
import { BellIcon } from '../icon/Icon';

const Messages = () => {
    return (
        <div  >
            <div className="  inline-flex items-center justify-start gap-4 font-inter">
                <div className="flex items-center justify-start gap-1  ">
                    <div className="flex items-center justify-center gap-2.5 p-0.5">
                        <div className="inline-flex h-6 w-6 flex-col items-center justify-center rounded-[100px] bg-[#993333]">
                            <span className="text-center text-xs font-normal leading-none  text-white">2</span>
                        </div>
                    </div>
                    <span className="text-base font-normal leading-relaxed  text-white">Messages</span>
                </div>
                <div className="h-[0px] w-[23px]  origin-top-left rotate-90  -mt-4  border border-[#e4e4e4]" />
                <div className="relative h-6 w-6  overflow-hidden  -ms-2.5  me-2">
                    <BellIcon />
                    <div className="absolute left-[15px] top-[2px] h-[7px] w-[7px] rounded-full bg-[#ff3b30]" />
                </div>
                <div className="h-[0px] w-[23px] origin-top-left rotate-90 border -mt-4  border-[#e4e4e4]" />
            </div>
        </div>
    );
};

export default Messages;
