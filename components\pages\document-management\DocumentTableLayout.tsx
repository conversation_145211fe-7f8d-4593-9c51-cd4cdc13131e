'use client';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import React from 'react';
import { useRouter } from 'next/navigation';
import BreadCrumButton from '@/components/reusable/BreadCrumButton';
import DocumentTable from './DocumentTable';

const DocumentTableLayout = () => {
    const { push } = useRouter();

    return (
        <DefaultPageLayout>
            <BreadCrums
                mainHeading="Document Management"
                breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Document Management' }]}
                ButonComponent={
                    <BreadCrumButton
                        onClick={() => {
                            push('/documents-management');
                        }}
                    />
                }
            />
            <div className="px-4">
                <DocumentTable/>

            </div>
        </DefaultPageLayout>
    );
};

export default DocumentTableLayout;
