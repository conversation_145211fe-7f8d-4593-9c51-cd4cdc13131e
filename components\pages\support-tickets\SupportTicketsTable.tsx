import React, { useState, useEffect, useRef } from 'react';
import { Ticket, TicketCheck, Clock, XCircle, Eye, MessageSquare, MoreHorizontal, X, Search, Loader2, CheckCircle } from 'lucide-react';
import { supportTicketsService, Ticket as TicketType, TicketSummary, TicketMeta } from '@/lib/supportTicketsService';
import { AGENT_SEARCH_API } from '@/app/lib/apiRoutes';

const statusOptions = [
  { value: 'all', label: 'All Tickets' },
  { value: 'open', label: 'Open' },
  { value: 'in-progress', label: 'In Progress' },
  { value: 'resolved', label: 'Resolved' },
  { value: 'closed', label: 'Closed' }
];

// Function to get status options with disabled logic for status updates
const getStatusUpdateOptions = (currentStatus: string) => [
  { 
    value: 'open', 
    label: 'Open', 
    disabled: currentStatus === 'open'
  },
  { 
    value: 'in-progress', 
    label: 'In Progress', 
    disabled: currentStatus === 'in-progress'
  },
  { 
    value: 'resolved', 
    label: 'Resolved', 
    disabled: currentStatus === 'resolved'
  },
  { 
    value: 'closed', 
    label: 'Closed', 
    disabled: currentStatus === 'closed'
  }
];

const priorityOptions = ['Low', 'Medium', 'High', 'Urgent'];
const categoryOptions = ['General', 'Technical', 'Payment', 'Account', 'Feature Request', 'Bug Report'];

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleDateString('en-US', { month: 'long' });
  const year = date.getFullYear();
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const ampm = hours >= 12 ? 'pm' : 'am';
  const displayHours = hours % 12 || 12;
  const displayMinutes = minutes.toString().padStart(2, '0');
  
  return `${day} ${month} ${year} ${displayHours}:${displayMinutes}${ampm}`;
}

const statusBadge = (status: string): string => {
  const statusStr = String(status || '').toLowerCase();
  const map: Record<string, string> = {
    open: 'bg-red-100 text-red-700',
    'in-progress': 'bg-yellow-100 text-yellow-800',
    resolved: 'bg-green-100 text-green-700',
    closed: 'bg-gray-100 text-gray-700',
  };
  return map[statusStr] || 'bg-gray-100 text-gray-700';
};

const priorityBadge = (priority: string): string => {
  const priorityStr = String(priority || '').toLowerCase();
  const map: Record<string, string> = {
    urgent: 'bg-red-600 text-white',
    high: 'bg-black text-white',
    medium: 'bg-gray-200 text-gray-800',
    low: 'bg-blue-100 text-blue-700',
  };
  return map[priorityStr] || 'bg-gray-100 text-gray-700';
};

export default function SupportTicketsTable() {
  // States
  const [tickets, setTickets] = useState<TicketType[]>([]);
  const [selectedTickets, setSelectedTickets] = useState<string[]>([]);
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetails, setShowDetails] = useState<TicketType | null>(null);
  const [createForm, setCreateForm] = useState({
    agent_name: '',
    agent_email: '',
    agent_id: '',
    title: '',
    priority: 'medium',
    category: 'General',
    description: '',
  });
  const [agentSearchLoading, setAgentSearchLoading] = useState(false);
  const [responseMessage, setResponseMessage] = useState('');

  // Function to fetch agent ID from email
  const fetchAgentIdFromEmail = async (email: string) => {
    if (!email) return;
    
    try {
      setAgentSearchLoading(true);
      
      // API to get agent profile by email
      const response = await fetch(`${AGENT_SEARCH_API}?email=${encodeURIComponent(email)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });
      
      if (response.ok) {
        const agentData = await response.json();
        
        // The backend responseData function wraps data in a 'data' property
        if (agentData.data && agentData.data.id) {
          setCreateForm(prev => ({ ...prev, agent_id: agentData.data.id.toString(), agent_name: agentData.data.name || '' }));
        } else if (agentData.id) {
          // Fallback for direct response structure
          setCreateForm(prev => ({ ...prev, agent_id: agentData.id.toString(), agent_name: agentData.name || '' }));
        }
      }
    } catch (err) {
      console.error('Failed to fetch agent ID:', err);
    } finally {
      setAgentSearchLoading(false);
    }
  };
  const [openActionMenuId, setOpenActionMenuId] = useState<string | null>(null);
  const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>('bottom');
  const [detailsTab, setDetailsTab] = useState('details');
  
  // Function to get dropdown positioning
  const getDropdownPosition = (event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const dropdownHeight = 160; // Approximate height of dropdown
    
    // Check if dropdown would be cut off at bottom
    if (rect.bottom + dropdownHeight > viewportHeight) {
      return 'top'; // Position above the button
    }
    return 'bottom'; // Position below the button
  };

  const [loading, setLoading] = useState(true);
  const [ticketSummary, setTicketSummary] = useState<TicketSummary>({ open: 0, 'in-progress': 0, resolved: 0, closed: 0 });
  const [ticketMeta, setTicketMeta] = useState<TicketMeta>({ status: [], priority: [], category: [] });
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const hasLoadedRef = useRef(false);

  const loadData = async () => {
    try {
      setLoading(true);
      const [ticketsData, summaryData, metaData] = await Promise.all([
        supportTicketsService.getTickets(),
        supportTicketsService.getTicketSummary(),
        supportTicketsService.getTicketMeta()
      ]);
      // Ensure ticketsData is an array    
      setTickets(Array.isArray(ticketsData) ? ticketsData : []);
      setTicketSummary(summaryData || { open: 0, 'in-progress': 0, resolved: 0, closed: 0 });
      setTicketMeta(metaData || { status: [], priority: [], category: [] });
    } catch (err) {
      console.error('Failed to load tickets:', err);
      setError(err instanceof Error ? err.message : 'Failed to load tickets');
      setTickets([]);
      setTicketSummary({ open: 0, 'in-progress': 0, resolved: 0, closed: 0 });
      setTicketMeta({ status: [], priority: [], category: [] });
    } finally {
      setLoading(false);
    }
  };

  // Load initial data
  useEffect(() => {
    if (!hasLoadedRef.current) {
      hasLoadedRef.current = true;
      loadData();
    }
  }, []);



  // Stats from API
  const openTickets = ticketSummary.open || 0;
  const inProgressTickets = ticketSummary['in-progress'] || 0;
  const resolvedTickets = ticketSummary.resolved || 0;
  const closedTickets = ticketSummary.closed || 0;

  React.useEffect(() => {
    function handleClickOutside(e: MouseEvent) {
      if (!(e.target as Element).closest('.ticket-action-menu')) setOpenActionMenuId(null);
    }
    if (openActionMenuId !== null) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [openActionMenuId]);

  // Client-side filtering
  const filteredTickets = React.useMemo(() => {
    let filtered = Array.isArray(tickets) ? tickets : [];
    
    // Filter by status
    if (filterStatus !== 'all') {
      filtered = filtered.filter(ticket => ticket.status === filterStatus);
    }
    
    // Filter by search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(ticket => 
        ticket.title?.toLowerCase().includes(searchLower) ||
        ticket.description?.toLowerCase().includes(searchLower) ||
        ticket.agent_name?.toLowerCase().includes(searchLower) ||
        ticket.agent_email?.toLowerCase().includes(searchLower) ||
        ticket.category?.toLowerCase().includes(searchLower)
      );
    }
    
    return filtered;
  }, [tickets, filterStatus, searchTerm]);



  // Bulk select
  const handleSelectAll = (checked: boolean) => {
    setSelectedTickets(checked ? tickets.map(t => String(t.id)) : []);
  };
  const handleSelectTicket = (ticketId: string, checked: boolean) => {
    setSelectedTickets(checked ? [...selectedTickets, ticketId] : selectedTickets.filter(id => id !== ticketId));
  };

  // Load ticket details with responses
  const loadTicketDetails = async (ticketId: string) => {
    try {
      const [ticketDetails, responses] = await Promise.all([
        supportTicketsService.getTicket(ticketId),
        supportTicketsService.getTicketResponses(ticketId)
      ]);
      

      
      setShowDetails({ ...ticketDetails, responses });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load ticket details');
    }
  };

  // Ticket actions
  const handleStatusUpdate = async (ticketId: string, newStatus: string) => {
    try {
      setError(null); // Clear any existing errors
      await supportTicketsService.updateTicket(ticketId, { status: newStatus });
      
      const currentTime = new Date().toISOString();
      
      // Find the ticket being updated to get its old status
      const ticketToUpdate = tickets.find(ticket => String(ticket.id) === String(ticketId));
      const oldStatus = ticketToUpdate?.status;
      
      // Update tickets list
      const updatedTickets = tickets.map(ticket =>
        String(ticket.id) === String(ticketId) ? { ...ticket, status: newStatus as any, updated_at: currentTime } : ticket
      );
      setTickets(updatedTickets);
      
      // Update ticket summary counts
      if (oldStatus && oldStatus !== newStatus) {
        setTicketSummary(prevSummary => {
          const newSummary = { ...prevSummary };
          
          // Decrease count for old status
          if (oldStatus === 'open') newSummary.open = Math.max(0, (newSummary.open || 0) - 1);
          else if (oldStatus === 'in-progress') newSummary['in-progress'] = Math.max(0, (newSummary['in-progress'] || 0) - 1);
          else if (oldStatus === 'resolved') newSummary.resolved = Math.max(0, (newSummary.resolved || 0) - 1);
          else if (oldStatus === 'closed') newSummary.closed = Math.max(0, (newSummary.closed || 0) - 1);
          
          // Increase count for new status
          if (newStatus === 'open') newSummary.open = (newSummary.open || 0) + 1;
          else if (newStatus === 'in-progress') newSummary['in-progress'] = (newSummary['in-progress'] || 0) + 1;
          else if (newStatus === 'resolved') newSummary.resolved = (newSummary.resolved || 0) + 1;
          else if (newStatus === 'closed') newSummary.closed = (newSummary.closed || 0) + 1;
          
          return newSummary;
        });
      }
      
      // Update details modal if open
      if (showDetails && String(showDetails.id) === String(ticketId)) {
        setShowDetails({ ...showDetails, status: newStatus as any, updated_at: currentTime });
      }
      
      // Show success message
      const statusDisplayName = newStatus.charAt(0).toUpperCase() + newStatus.slice(1).replace('-', ' ');
      setSuccessMessage(`Ticket status successfully updated to "${statusDisplayName}"`);
      
      // Clear success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update ticket status');
    }
  };

  // Create ticket
  const handleCreateTicket = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!createForm.agent_email || !createForm.title || !createForm.description) {
      setError('Please fill in all required fields: Agent Email, Title, and Description');
      return;
    }
    
    try {
      // If agent_id is empty, try to fetch it from email first
      let formData = { ...createForm };
      
      if (!formData.agent_id) {
        try {
          const response = await fetch(`${AGENT_SEARCH_API}?email=${encodeURIComponent(formData.agent_email)}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
          });
          
          if (response.ok) {
            const agentData = await response.json();
            
            // The backend responseData function wraps data in a 'data' property
            if (agentData.data && agentData.data.id) {
              formData.agent_id = agentData.data.id.toString();
              formData.agent_name = agentData.data.name || formData.agent_name;
            } else if (agentData.id) {
              // Fallback for direct response structure
              formData.agent_id = agentData.id.toString();
              formData.agent_name = agentData.name || formData.agent_name;
            } else {
              throw new Error('Agent not found with this email');
            }
          } else {
            throw new Error('Failed to find agent with this email');
          }
        } catch (err) {
          setError('Agent not found with this email. Please check the email address.');
          return;
        }
      }
      
      const newTicket = await supportTicketsService.createTicket(formData);
      setTickets([newTicket, ...tickets]);
      
      // Update ticket summary counts for new ticket
      setTicketSummary(prevSummary => {
        const newSummary = { ...prevSummary };
        if (newTicket.status === 'open') newSummary.open = (newSummary.open || 0) + 1;
        else if (newTicket.status === 'in-progress') newSummary['in-progress'] = (newSummary['in-progress'] || 0) + 1;
        else if (newTicket.status === 'resolved') newSummary.resolved = (newSummary.resolved || 0) + 1;
        else if (newTicket.status === 'closed') newSummary.closed = (newSummary.closed || 0) + 1;
        return newSummary;
      });
      
      setShowCreateModal(false);
      setError(null); // Clear error on success
      setSuccessMessage('Ticket created successfully!');
      setCreateForm({ agent_name: '', agent_email: '', agent_id: '', title: '', priority: 'medium', category: 'General', description: '' });
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create ticket');
    }
  };

  // Details modal: response
  const handleSendResponse = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!responseMessage || !showDetails) return;
    
    try {
      const newResponse = await supportTicketsService.addTicketResponse(showDetails.id, {
        response: responseMessage
      });
      
      const updatedTicket = {
        ...showDetails,
        responses: [...(showDetails.responses || []), newResponse],
        updated_at: new Date().toISOString(),
      };
      setTickets(tickets.map(ticket => ticket.id === showDetails.id ? updatedTicket : ticket));
      setShowDetails(updatedTicket);
      setResponseMessage('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send response');
    }
  };

  // UI
  if (loading) {
    return (
      <div className="py-6 flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>Loading tickets...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <div className="flex items-center gap-2 text-red-800">
            <XCircle className="w-5 h-5" />
            <span className="font-medium">Error: {error}</span>
          </div>
          <button 
            className="mt-2 text-red-600 hover:text-red-800 underline"
            onClick={() => {
              setError(null);
              loadData();
            }}
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="py-6">
      {/* Header and Create Ticket Button */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Support Tickets</h1>
        <button className="bg-[#111827] text-white px-6 py-2 rounded-md font-semibold" onClick={() => {
          setShowCreateModal(true);
          setError(null); // Clear any previous errors
          setSuccessMessage(null); // Clear any previous success messages
        }}>
          + Create Ticket
        </button>
      </div>
      

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div className="rounded-xl border p-6 bg-white flex flex-col items-start">
          <div className="flex items-center gap-2 text-3xl font-bold"><Ticket className="w-6 h-6 text-red-500" />{openTickets}</div>
          <div className="text-base font-medium text-gray-600 mt-2">Open Tickets</div>
        </div>
        <div className="rounded-xl border p-6 bg-white flex flex-col items-start">
          <div className="flex items-center gap-2 text-3xl font-bold"><Clock className="w-6 h-6 text-yellow-500" />{inProgressTickets}</div>
          <div className="text-base font-medium text-gray-600 mt-2">In Progress</div>
        </div>
        <div className="rounded-xl border p-6 bg-white flex flex-col items-start">
          <div className="flex items-center gap-2 text-3xl font-bold"><TicketCheck className="w-6 h-6 text-green-600" />{resolvedTickets}</div>
          <div className="text-base font-medium text-gray-600 mt-2">Resolved</div>
        </div>
        <div className="rounded-xl border p-6 bg-white flex flex-col items-start">
          <div className="flex items-center gap-2 text-3xl font-bold"><XCircle className="w-6 h-6 text-gray-500" />{closedTickets}</div>
          <div className="text-base font-medium text-gray-600 mt-2">Closed</div>
        </div>
      </div>
      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4 mb-6 items-center">
        <div className="flex items-center gap-2 w-full md:w-auto">
          <Search className="w-4 h-4 text-gray-400" />
          <input
            className="w-full md:w-64 border rounded-md px-4 py-2"
            placeholder="Search tickets..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
        </div>
        <select
          className="border rounded-md px-4 py-2"
          value={filterStatus}
          onChange={e => setFilterStatus(e.target.value)}
        >
          {statusOptions.map(opt => (
            <option key={opt.value} value={opt.value}>{opt.label}</option>
          ))}
        </select>
      </div>
      {/* Table */}
      <div className="overflow-x-auto rounded-lg border bg-white max-h-[600px] overflow-y-auto">
        <table className="min-w-full">
          <thead className="bg-gray-50 sticky top-0 z-10">
            <tr>
              <th className="px-4 py-3"><input type="checkbox" checked={selectedTickets.length === tickets.length && tickets.length > 0} onChange={e => handleSelectAll(e.target.checked)} /></th>
              <th className="px-4 py-3 text-left">Title</th>
              <th className="px-4 py-3 text-left">Agent</th>
              <th className="px-4 py-3 text-left">Status</th>
              <th className="px-4 py-3 text-left">Priority</th>
              <th className="px-4 py-3 text-left">Category</th>
              <th className="px-4 py-3 text-left">Created</th>
              <th className="px-4 py-3 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredTickets.length === 0 && (
              <tr><td colSpan={8} className="text-center py-8 text-gray-400">No support tickets found</td></tr>
            )}
            {filteredTickets.map(ticket => (
              <tr key={ticket.id} className="border-t hover:bg-gray-50">
                <td className="px-4 py-3"><input type="checkbox" checked={selectedTickets.includes(String(ticket.id))} onChange={e => handleSelectTicket(String(ticket.id), e.target.checked)} /></td>
                <td className="px-4 py-3">
                  <div className="font-semibold">{ticket.title.toUpperCase()}</div>
                  <div className="text-xs text-gray-500 truncate max-w-xs">{ticket.description}</div>
                </td>
                <td className="px-4 py-3">
                  <div className="font-medium">{ticket.agent_name || 'N/A'}</div>
                  <div className="text-xs text-gray-500">{ticket.agent_email || 'N/A'}</div>
                </td>
                <td className="px-4 py-3"><span className={`inline-block px-3 py-1 rounded-full text-xs font-semibold ${statusBadge(String(ticket.status || ''))}`}>{(String(ticket.status || '')).charAt(0).toUpperCase() + (String(ticket.status || '')).slice(1)}</span></td>
                <td className="px-4 py-3"><span className={`inline-block px-3 py-1 rounded-full text-xs font-semibold ${priorityBadge(String(ticket.priority || ''))}`}>{(String(ticket.priority || '')).charAt(0).toUpperCase() + (String(ticket.priority || '')).slice(1)}</span></td>
                <td className="px-4 py-3"><span className="inline-block px-3 py-1 rounded-full text-xs border border-gray-300">{ticket.category}</span></td>
                <td className="px-4 py-3">{formatDate(ticket.created_at)}</td>
                <td className="px-4 py-3">
                  <div className="flex items-center gap-2">
                    <button className="text-gray-500 hover:text-blue-600" title="Responses" onClick={() => { loadTicketDetails(String(ticket.id)); setDetailsTab('responses'); }}><MessageSquare className="h-5 w-5" /></button>
                    <div className="relative ticket-action-menu">
                      <button 
                        className="text-gray-500 hover:text-blue-600" 
                        onClick={(e) => {
                          const position = getDropdownPosition(e);
                          setDropdownPosition(position);
                          setOpenActionMenuId(String(ticket.id));
                        }}
                      >
                        <MoreHorizontal className="h-5 w-5" />
                      </button>
                      {openActionMenuId === String(ticket.id) && (
                        <div className={`absolute right-0 z-20 bg-white border rounded shadow-lg min-w-[160px] ${
                          dropdownPosition === 'top' ? 'bottom-full mb-2' : 'top-full mt-2'
                        }`}>
                          <button className="flex items-center w-full px-4 py-2 text-sm hover:bg-gray-100" onClick={() => { loadTicketDetails(String(ticket.id)); setDetailsTab('details'); setOpenActionMenuId(null); }}><Eye className="h-4 w-4 mr-2" />View Details</button>
                          {getStatusUpdateOptions(ticket.status).map((option) => (
                            <button 
                              key={option.value}
                              className={`flex items-center w-full px-4 py-2 text-sm ${
                                option.disabled 
                                  ? 'text-gray-400 cursor-not-allowed bg-gray-50' 
                                  : 'hover:bg-gray-100'
                              }`}
                              onClick={() => { 
                                if (!option.disabled) {
                                  handleStatusUpdate(String(ticket.id), option.value); 
                                  setOpenActionMenuId(null);
                                }
                              }}
                              disabled={option.disabled}
                            >
                              Mark as {option.label}
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {/* Create Ticket Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
          <div className="bg-white rounded-xl p-8 w-full max-w-2xl relative">
            <button className="absolute top-4 right-4 text-gray-500 text-2xl" onClick={() => {
              setShowCreateModal(false);
              setError(null); // Clear error when modal is closed
              setSuccessMessage(null); // Clear success message when modal is closed
            }}>&times;</button>
            <h2 className="text-xl font-bold mb-6 flex items-center gap-2"><span className="inline-block text-2xl">👤</span> Create Support Ticket for Agent</h2>
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
                <div className="flex items-center gap-2 text-red-800">
                  <XCircle className="w-5 h-5" />
                  <span className="font-medium">Error: {error}</span>
                </div>
              </div>
            )}
            <form className="space-y-6" onSubmit={handleCreateTicket}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Agent Name *</label>
                  <input 
                    className="w-full border rounded-md px-4 py-2" 
                    placeholder="Enter agent's full name" 
                    value={createForm.agent_name} 
                    onChange={e => setCreateForm(f => ({ ...f, agent_name: e.target.value }))} 
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Agent Email *</label>
                  <div className="relative">
                    <input 
                      className="w-full border rounded-md px-4 py-2 pr-10" 
                      placeholder="<EMAIL>" 
                      value={createForm.agent_email} 
                      onChange={e => setCreateForm(f => ({ ...f, agent_email: e.target.value }))}
                      onBlur={() => fetchAgentIdFromEmail(createForm.agent_email)}
                    />
                    {agentSearchLoading && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <Loader2 className="w-4 h-4 animate-spin" />
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Ticket Title *</label>
                <input className="w-full border rounded-md px-4 py-2" placeholder="Brief description of the issue" value={createForm.title} onChange={e => setCreateForm(f => ({ ...f, title: e.target.value }))} />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Priority</label>
                  <select className="w-full border rounded-md px-4 py-2" value={createForm.priority} onChange={e => setCreateForm(f => ({ ...f, priority: e.target.value }))}>
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="urgent">Urgent</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Category</label>
                  <select className="w-full border rounded-md px-4 py-2" value={createForm.category} onChange={e => setCreateForm(f => ({ ...f, category: e.target.value }))}>
                    <option value="General">General</option>
                    <option value="Technical">Technical</option>
                    <option value="Payment">Payment</option>
                    <option value="Account">Account</option>
                    <option value="Feature Request">Feature Request</option>
                    <option value="Bug Report">Bug Report</option>
                  </select>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Issue Description *</label>
                <textarea className="w-full border rounded-md px-4 py-2 min-h-[80px]" placeholder="Detailed description of the agent's issue..." value={createForm.description} onChange={e => setCreateForm(f => ({ ...f, description: e.target.value }))} />
              </div>
              <div className="flex justify-end gap-2 mt-6">
                <button type="button" className="bg-gray-200 px-4 py-2 rounded" onClick={() => setShowCreateModal(false)}>Cancel</button>
                <button type="submit" className="bg-[#111827] text-white px-4 py-2 rounded">Create Ticket & Notify Agent</button>
              </div>
            </form>
          </div>
        </div>
      )}
      {/* Ticket Details Modal */}
      {showDetails && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
          <div className="bg-white rounded-xl p-8 w-full max-w-4xl relative overflow-y-auto max-h-[90vh]">
            <button className="absolute top-4 right-4 text-gray-500 text-2xl" onClick={() => setShowDetails(null)}>&times;</button>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-8">
              <div>
                <h2 className="text-3xl font-bold mb-4 text-gray-800">{showDetails.title.toUpperCase()}</h2>
                <div className="flex gap-3 items-center">
                  <span className={`inline-block px-4 py-2 rounded-full text-sm font-semibold ${priorityBadge(showDetails.priority)}`}>{showDetails.priority.charAt(0).toUpperCase() + showDetails.priority.slice(1)}</span>
                  <select className="border rounded-lg px-3 py-2 text-sm bg-white" value={showDetails.status} onChange={e => handleStatusUpdate(showDetails.id, e.target.value)}>
                    {getStatusUpdateOptions(showDetails.status).map((option) => (
                      <option 
                        key={option.value} 
                        value={option.value}
                        disabled={option.disabled}
                      >
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
            <div className="border-b mb-4 flex gap-4">
              <button className={`py-2 px-4 font-semibold ${detailsTab === 'details' ? 'border-b-2 border-[#111827]' : 'text-gray-500'}`} onClick={() => setDetailsTab('details')}>Details</button>
              <button className={`py-2 px-4 font-semibold ${detailsTab === 'responses' ? 'border-b-2 border-[#111827]' : 'text-gray-500'}`} onClick={() => setDetailsTab('responses')}>Responses</button>
            </div>
            {detailsTab === 'details' && (
              <div className="space-y-8">
                {/* Ticket Details Section */}
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">Ticket Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600 mb-1">Created by</p>
                      <p className="font-medium">{showDetails.admin_name || 'Admin'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 mb-1">Category</p>
                      <span className="inline-block px-3 py-1 rounded-full text-sm border border-gray-300 bg-white">{showDetails.category}</span>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 mb-1">Created at</p>
                      <p className="font-medium">{formatDate(showDetails.created_at)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 mb-1">Last updated</p>
                      <p className="font-medium">{formatDate(showDetails.updated_at)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 mb-1">Status</p>
                      <span className={`inline-block px-3 py-1 rounded-full text-sm font-semibold ${statusBadge(showDetails.status)}`}>{showDetails.status.charAt(0).toUpperCase() + showDetails.status.slice(1)}</span>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 mb-1">Priority</p>
                      <span className={`inline-block px-3 py-1 rounded-full text-sm font-semibold ${priorityBadge(showDetails.priority)}`}>{showDetails.priority.charAt(0).toUpperCase() + showDetails.priority.slice(1)}</span>
                    </div>
                  </div>
                </div>

                {/* Description Section */}
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">Description</h3>
                  <p className="text-gray-700 leading-relaxed">{showDetails.description}</p>
                </div>

                {/* Agent Information Section */}
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">Agent Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600 mb-1">Name</p>
                      <p className="font-medium">{showDetails.agent_name || 'N/A'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 mb-1">Email</p>
                      <p className="font-medium">{showDetails.agent_email || 'N/A'}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {detailsTab === 'responses' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold mb-2">Responses</h3>
                {showDetails.responses && showDetails.responses.length > 0 ? (
                  <div className="space-y-4">
                    {showDetails.responses.map((response, index) => (
                      <div key={response.id} className="bg-gray-50 p-4 rounded-md">
                        <p className="text-sm font-medium mb-1">Response {index + 1}</p>
                        <p>{response.response}</p>
                        <p className="text-xs text-gray-500 mt-1">By {response.admin_name || response.agent_name || 'Admin'} on {formatDate(response.created_at)}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p>No responses yet for this ticket.</p>
                )}
                <form onSubmit={handleSendResponse} className="mt-6">
                  <h3 className="text-lg font-semibold mb-2">Send New Response</h3>
                  <textarea
                    className="w-full border rounded-md px-4 py-2 min-h-[100px] mb-4"
                    placeholder="Write your response here..."
                    value={responseMessage}
                    onChange={e => setResponseMessage(e.target.value)}
                  />
                  <button type="submit" className="bg-[#111827] text-white px-4 py-2 rounded">Send Response</button>
                </form>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Success Message Modal */}
      {successMessage && (
        <div className="fixed bottom-4 right-4 z-50 bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg flex items-center gap-3 animate-in slide-in-from-right duration-300">
          <CheckCircle className="w-5 h-5" />
          <span className="font-medium">{successMessage}</span>
          <button 
            onClick={() => setSuccessMessage(null)}
            className="ml-2 text-white hover:text-green-100"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}
    </div>
  );
}