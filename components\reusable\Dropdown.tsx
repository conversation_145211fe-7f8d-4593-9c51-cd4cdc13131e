"use client";
import React, { useEffect, useState } from "react";
import Select, { type StylesConfig } from "react-select";

interface SimpleDropdownProps {
  data: { label: string; value: string }[];
  label: string;
  onHandleClick?: (item: { label: string; value: string }) => void;
  name?: string;
  value?: { label: string; value: string };
  disabled?: boolean;
  notSort?: boolean;
  width?: string; // New prop for width
}

export const Dropdown    = ({
  data,
  label,
  onHandleClick,
  name,
  value,
  disabled,
  width = " w-full", // Default width if not provided
}:any) => {
  const [selectedValue, setSelectedValue] = useState(
    value || { label: "", value: "" }
  );
  const [searchedData, setSearchedData] = useState<
    { label: string; value: string }[]
  >([]);

  useEffect(() => {
    if (typeof value !== "string") {
      setSelectedValue(value || { label: "", value: "" });
    }
  }, [value]);

  useEffect(() => {
    setSearchedData(data);
  }, [data]);

  const colourStyles: StylesConfig<any> = {
    control: (styles) => ({
      ...styles,
      maxWidth: "100%",
      width: "100%",
      backgroundColor: "none",
      color: "#292929",
      borderRadius: "8px",
      borderColor: "lightGray",
      boxShadow: "none",
      borderWidth: "1px",
      outline: "none",
      fontSize: "14px",
      fontWeight: "normal",
      padding: "",
      paddingTop: "8px",
      paddingBottom: "8px",
      cursor:"pointer"
    }),
    input: (styles) => ({
      ...styles,
      color: "#292929",
      fontSize: "14px",
    }),
    placeholder: (styles) => ({
      ...styles,
      color: "#989898 ",
      fontSize: "14px",
    }),
    singleValue: (styles) => ({
      ...styles,
      color: "#292929",
      border: "none",
      ":hover": {
        backgroundColor: "#292929",
      },
      fontSize: "14px",
      paddingTop: "10px",
    }),
    menu: (styles) => ({
      ...styles,
      backgroundColor: "#F1F6F5",
      borderRadius: "25px",
      border: "none",
      padding: "0px",
      boxShadow: "none",
      color: "#292929",
      margin: "0px",
      left: "1px",
      zIndex: "9999",
      width: "100%",
    }),
    menuList: (styles) => ({
      ...styles,
      borderRadius: "4px",
      color: "#292929",
      paddingTop: "0px",
      backgroundColor: "#EEEDF4",
      minWidth: "230px",
      outline: "none",
      fontSize: "14px",
      fontWeight: "normal",
    }),
    indicatorSeparator: () => ({
      display: "none",
    }),
    option: (styles, { isSelected }) => ({
      ...styles,
      backgroundColor: isSelected ? "#cacace" : "#EEEDF4",
      borderBottom: "1px solid #cacace",
      color: isSelected ? "" : "",
      paddingLeft: "22px",
      paddingRight: "22px",
      paddingTop: "14px",
      paddingBottom: "14px",
      ":last-child": {
        // borderBottom: "none",
        marginBottom: "10px"
      },
      ":hover": {
        backgroundColor: "#cacace",
      },
    }),
    menuPortal: (base) => ({
      ...base,
      zIndex: 99999, // Ensure dropdown is above modal
    }),
  };

  return (        
    <div className={`relative ${width}`}>
      {label && (
        <label className="text-[#555555] text-xs font-normal  px-1 absolute left-2 top-2 z-10">
          {label}
        </label>
      )}
      <Select
        options={searchedData}
        placeholder={label}
        styles={colourStyles}
        value={selectedValue} //@ts-ignore
        onChange={(selectedOption) => {
          setSelectedValue(selectedOption);
          if (onHandleClick) {
            onHandleClick(selectedOption);
          }
        }}
        name={name}
        isDisabled={disabled}
        menuPortalTarget={typeof window !== "undefined" ? document.body : null}
      />
    </div>
  );
};