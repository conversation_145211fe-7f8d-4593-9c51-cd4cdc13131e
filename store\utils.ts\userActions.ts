import API_ENDPOINTS from '@/app/lib/apiRoutes';
import { clearAllCookies, getCookies } from '@/app/lib/cookies';
import Cookies from 'js-cookie';
import { setUserSession, setUserDetails } from '@/store/slice/user';
import type { Dispatch } from '@reduxjs/toolkit';
import { showMessage } from '@/app/lib/Alert';

// Pass navigate function (e.g. router.push or a wrapper) as argument
export const fetchUserProfile = (navigateToLogin: () => void) => async (dispatch: Dispatch) => {
    const userId = getCookies('adminUserId');

    if (!userId) {
        navigateToLogin();
        return;
    }

    try {
        const response = await fetch(API_ENDPOINTS.GET_SESSION + userId, {
            credentials: 'include',
        });
        const result = await response.json();

        if (result.success) {
            dispatch(setUserSession(true));
            dispatch(setUserDetails(result?.data));
        } else {
            showMessage(result.message, 'error');
            dispatch(setUserSession(false));
            clearAllCookies();
            Cookies.remove('adminUserId');
            navigateToLogin();
        }
    } catch (error) {
        console.error('Error fetching profile:', error);
        showMessage('Something went wrong, please try again later.', 'error');
        dispatch(setUserSession(false));
        navigateToLogin();
    }
};
