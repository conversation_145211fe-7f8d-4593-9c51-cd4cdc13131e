import IconEdit from '@/components/icon/icon-edit';
import IconPlus from '@/components/icon/icon-plus';
import React, { useEffect, useState } from 'react';
import Modal from '@/components/reusable/modals/modal';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

interface DiscountPopupProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (data: any, isEdit: boolean) => void;
    initialValues?: any;
    isEdit?: boolean;
    isSubmitting?: boolean;
}

const AddAndUpdateDiscountPopup: React.FC<DiscountPopupProps> = ({ isOpen, onClose, onSubmit, initialValues, isEdit = false, isSubmitting }) => {
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const [formValues, setFormValues] = useState({
        name: '',
        description: '',
        type: 'percentage',
        value: '',
        code: '',
    });

    useEffect(() => {
        if (initialValues) {
            setFormValues({
                name: initialValues.name || '',
                description: initialValues.description || '',
                type: initialValues.type || 'percentage',
                value: initialValues.value || '',
                code: initialValues.code || '',
            });
        } else {
            setFormValues({ name: '', description: '', type: 'percentage', value: '', code: '' });
        }
        setErrors({});
    }, [initialValues]);

    const handleClose = () => {
        setFormValues({ name: '', description: '', type: 'percentage', value: '', code: '' });
        setErrors({});
        onClose();
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        setFormValues({ ...formValues, [e.target.name]: e.target.value });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const newErrors: { [key: string]: string } = {};

        if (!formValues.name.trim()) newErrors.name = 'Discount name is required.';
        if (!formValues.type) newErrors.type = 'Discount type is required.';
        if (!formValues.value || isNaN(Number(formValues.value))) newErrors.value = 'Valid discount value is required.';

        setErrors(newErrors);

        if (Object.keys(newErrors).length === 0) {
            onSubmit(formValues, isEdit);
        }
    };

    const renderError = (field: string) => errors[field] && <p className="mt-1 text-sm text-red-500">{errors[field]}</p>;

    if (!isOpen) return null;

    return (
        <Modal isOpen={isOpen} onClose={onClose} classes="!max-w-1xl">
            <div className=" p-6">
                <button onClick={handleClose} className="absolute right-4 top-4 text-gray-400 hover:text-gray-600">
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>

                <h2 className="mb-6 flex items-center text-xl font-semibold text-gray-900">
                    <span className="mr-2">{isEdit ? <IconEdit /> : <IconPlus />}</span>
                    {isEdit ? 'Edit Discount' : 'Add New Discount'}
                </h2>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <label htmlFor="name" className="mb-1 block text-sm font-medium text-gray-700">
                            Name
                        </label>
                        <input
                            type="text"
                            id="name"
                            name="name"
                            value={formValues.name}
                            onChange={handleChange}
                            placeholder="Enter discount name"
                            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                        />
                        {renderError('name')}
                    </div>

                    <div>
                        <label htmlFor="type" className="mb-1 block text-sm font-medium text-gray-700">
                            Type
                        </label>
                        <select
                            id="type"
                            name="type"
                            value={formValues.type}
                            onChange={handleChange}
                            className="w-full appearance-none rounded-md border border-gray-300 bg-white px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                        >
                            <option value="percentage">Percentage</option>
                            <option value="fixed">Fixed</option>
                        </select>
                        {renderError('type')}
                    </div>

                    <div>
                        <label htmlFor="value" className="mb-1 block text-sm font-medium text-gray-700">
                            Value {formValues.type.toLowerCase() == 'percentage' && <span> in %</span>}
                        </label>
                        <input
                            type="number"
                            id="value"
                            name="value"
                            value={formValues.value}
                            onChange={handleChange}
                            placeholder="Enter value"
                            className="w-full appearance-none rounded-md border border-gray-300 bg-white px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                        />
                        {renderError('value')}
                    </div>

                    <div>
                        <ReactQuill
                            theme="snow"
                            value={formValues.description}
                            onChange={(value) =>
                                setFormValues({
                                    ...formValues,
                                    description: value,
                                })
                            }
                        />
                    </div>

                    <div className="mt-6 flex justify-end gap-3">
                        <button type="button" onClick={handleClose} className="rounded-md border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>

                        {isSubmitting ? (
                            <button
                                className="flex items-center gap-2 rounded bg-[#1D7EB6] px-4 py-2 text-sm font-medium text-white hover:bg-[#1D7EB6]/90 disabled:cursor-not-allowed disabled:opacity-50"
                                disabled
                            >
                                <svg className="h-5 w-5 animate-spin text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z" />
                                </svg>
                                Saving...
                            </button>
                        ) : (
                            <button type="submit" className="flex items-center gap-2 rounded bg-[#1D7EB6] px-4 py-2 text-sm font-medium text-white hover:bg-[#1D7EB6]/90">
                                {isEdit ? 'Update Discount' : 'Add Discount'}
                            </button>
                        )}
                    </div>
                </form>
            </div>
        </Modal>
    );
};

export default AddAndUpdateDiscountPopup;
