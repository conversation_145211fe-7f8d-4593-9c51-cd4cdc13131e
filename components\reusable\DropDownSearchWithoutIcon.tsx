import { useEffect, useRef, useState } from "react";

export  const DropDownSearchWithoutIcon = ({
    placeholder ,
    mainClass,
    classes
}:any) => {
    const [searchTerm, setSearchTerm] = useState("");
    const [isOpen, setIsOpen] = useState(false);
    const [selectedOption, setSelectedOption] = useState("");
    const dropdownRef = useRef<HTMLDivElement>(null); // Ref for dropdown container
  
    const options = [
      "Real Estate",
      "Insurance",
      "Marketing",
      "Technology",
      "Healthcare",
    ];
  
    const filteredOptions = options.filter((option) =>
      option.toLowerCase().includes(searchTerm.toLowerCase())
    );
  
    const handleSelectOption = (option: string) => {
      setSelectedOption(option);
      setSearchTerm(option);
      setIsOpen(false);
    };
  
    // Handle clicks outside of the dropdown
    useEffect(() => {
      const handleOutsideClick = (event: MouseEvent) => {
        if (
          dropdownRef.current &&
          !dropdownRef.current.contains(event.target as Node)
        ) {
          setIsOpen(false);
        }
      };
  
      document.addEventListener("mousedown", handleOutsideClick);
      return () => {
        document.removeEventListener("mousedown", handleOutsideClick);
      };
    }, []);
  
    return (
      <div
 
        className={"relative h-14  w-full " + mainClass}
 
        ref={dropdownRef}
      >
       
        <div
 
          className={`border-[1px] border-searchBorderColor rounded h-full flex items-center justify-between cursor-pointer ${classes}`}
 
          onClick={() => setIsOpen(!isOpen)}
        >
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setIsOpen(true);
            }}
            placeholder={placeholder}
            className={`w-full h-full font-inter ps-4 text-base focus:ring-1 cursor-pointer  rounded-[8px]  pt- outline-none ${
              searchTerm ? "   " : "  "
            }}`}
          />
        </div>
  
        {/* Dropdown Menu */}
        {isOpen && ( 
          <div className="absolute z-20 bg-white border border-searchBorderColor rounded shadow-lg mt-1 w-full">
            <ul className="max-h-40 overflow-y-auto scrollbar-main-22">
  
              {filteredOptions.length > 0 ? (
                filteredOptions.map((option, index) => (
                  <li
                    key={index}
                    onClick={() => handleSelectOption(option)}
                    className="p-3 text-base    cursor-pointer hover:bg-gray-200 hover:text-[#1D7EB6]"
                  >
                    {option}
                  </li>
                ))
              ) : (
                <li className="p-2 text-center text-gray-500">
                  No options found
                </li>
              )}
            </ul>
          </div>
        )}
      </div>
    );
  }; 