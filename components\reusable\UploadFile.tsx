import API_ENDPOINTS from '@/app/lib/apiRoutes';

export default async function uploadFile(file: File, keyName: string): Promise<any> {
    if (!file || !(file instanceof File)) return;
    const myHeaders = new Headers();

    const formData = new FormData();
    formData.append('document', file);
    formData.append('keyValue', keyName);

    const requestOptions: RequestInit = {
        method: 'POST',
        headers: myHeaders,
        body: formData,
        credentials: 'include',
        redirect: 'follow',
    };

    try {
        const response = await fetch(API_ENDPOINTS.UPLOAD_FILE, requestOptions);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();
        return process.env.NEXT_PUBLIC_DOCUMENTS_BASE_URL + result?.data[0]?.fileKey;
    } catch (error) {
        console.error('Upload failed:', error);
        throw error;
    }
}
