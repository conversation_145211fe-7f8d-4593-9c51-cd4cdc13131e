
import { DateIcon } from "@/components/icon/Icon";
import * as React from "react";
 

export function DateFilter({ onDateChange, classes }: any) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [startDate, setStartDate] = React.useState("");
  const [endDate, setEndDate] = React.useState("");
  const [error, setError] = React.useState(""); // Error state for validation
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  const handleDone = () => {
    // Validation: Check if start date is greater than end date
    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
      setError("Start date must be less than to the end date");
      return;
    }

    // Clear error and notify parent component
    setError("");
    if (onDateChange) {
      onDateChange(startDate, endDate);
    }
    setIsOpen(false);
  };

  React.useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative bg-white w-full  font-inter" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex justify-between font-inter px-4 w-full py-2.5 text-sm border border-[#732323] rounded-lg hover:bg-gray-50 ${classes}`}
      >
        <span>Select Date</span>
        <span>
          <DateIcon/>
        </span>
      </button>

      {isOpen && (
        <div className="absolute mt-2 z-50 bg-white right-0 border border-[#732323] rounded-lg shadow-lg w-96">
          <div className="p-0">
            <div className="flex justify-between items-start gap-4 p-4">
              <div>
                <p className="pb-4 text-start text-red_primary">From</p>
                <input
                  type="date"
                  placeholder="12-12-2024"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className={`p-2 w-40 cursor-pointer rounded-lg text-start outline-none border ${
                    error ? "border-red-500" : "border-[#E4E4E4]"
                  }`}
                />
              </div>

              <div>
                <p className="pb-4 text-start text-red_primary">To</p>
                <input
                  type="date"
                  placeholder="12-12-2024"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className={`p-2 w-40 rounded-lg cursor-pointer text-start outline-none border ${
                    error ? "border-red-500" : "border-[#E4E4E4]"
                  }`}
                />
              </div>
            </div>
            {error && (
              <p className="text-red-500 text-sm text-center">{error}</p>
            )}
          </div>

          <div className="flex justify-between gap-4 p-4">
            <button
              className="flex-1 px-4 py-2 text-sm border text-[#732323] border-[#732323] rounded-lg"
              onClick={() => {
                setStartDate("");
                setEndDate("");
                setError("");
              }}
            >
              Reset
            </button>
            <button
              onClick={handleDone}
              className="flex-1 px-4 py-2 text-sm border rounded-lg bg-[#732323] text-white hover:bg-white hover:text-[#732323] hover:border-[#732323]"
            >
              Done
            </button>
          </div>
        </div>
      )}
    </div>
  );
}