'use client';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import React, { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import BreadCrumButton from '@/components/reusable/BreadCrumButton';
import { Filters } from '../lead-management/Filters';
import { UsersIcon, AnalyticsIcon, ChecksIcon, CircleIcon, ActionIcon } from './Icons';
import AddLeadPopup from './AddLeadPopup';
import BulkUploadPopup from './BulkUploadPopup';
import Loading from '@/components/layouts/loading';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import { getPaginationRange, statusOptions, useDebouncedValue } from '@/store/utils.ts/functions';
import { PaginationDownIcon, PaginationLeftIcon, PaginationRightIcon } from '@/components/icon/Icon';
import { showMessage } from '@/app/lib/Alert';
import Link from 'next/link';
import LeadDetailsPopup from './LeadDetailsPopup';
import AddNotePopup from './AddNotePopup';
import { NotebookPen } from 'lucide-react';
import MarkAsLostPopup from './MarkAsLostPopup';
import { BulkEmailModal } from '../user-management/UserManagementTable';

const capitalizeFirst = (text: string) => (text ? text.charAt(0).toUpperCase() + text.slice(1) : '');

// Status styles (matching document management)
const statusStyles = {
    New: 'bg-blue-100 text-blue-600',
    Contacted: 'bg-yellow-100 text-yellow-800',
    Qualified: 'bg-orange-100 text-orange-600',
    Lost: 'bg-red-100 text-red-600',
    Converted: 'bg-green-100 text-green-600',
    'Re-opened': 'bg-teal-100 text-teal-600',
};

// Type styles
const typeStyles = {
    agent: 'text-green-600 bg-green-100',
    agency: 'text-purple-600 bg-purple-100',
};

const statusCardMeta: Record<string, { icon: React.ReactNode; description: string; status?: string }> = {
    new: {
        icon: <AnalyticsIcon />,
        description: 'Requires follow-up',
    },
    contacted: {
        icon: <UsersIcon />,
        description: 'Initial contact made',
    },
    qualified: {
        icon: <CircleIcon />,
        description: 'All contacted',
        status: 'Ready for conversion',
    },
    lost: {
        icon: <UsersIcon />,
        description: 'Opportunity dropped',
        status: 'No response',
    },
    converted: {
        icon: <ChecksIcon />,
        description: 'All contacted',
        status: 'Success rate',
    },
};

interface Lead {
    id: number;
    fullName: string;
    email: string;
    phone: string;
    licenseNumber: string;
    company: string;
    leadType: 'agent' | 'agency';
    created_at: string;
    source: string;
    statusName: 'New' | 'Contacted' | 'Qualified' | 'Lost' | 'Converted';
    isConverted?: boolean;
    last_contact?: string;
}

interface LeadCountCard {
    statusId: number;
    statusName: string;
    leadsCount: string; // it's a string in the response
}

const columns = [
    { key: 'checkbox', label: '', width: 'w-12' },
    { key: 'name', label: 'Name', width: 'w-48' },
    { key: 'contact', label: 'Contact', width: 'w-64' },
    { key: 'company', label: 'Company', width: 'w-48' },
    { key: 'type', label: 'Type', width: 'w-24' },
    { key: 'status', label: 'Status', width: 'w-32' },
    { key: 'source', label: 'Source', width: 'w-32' },
    { key: 'dateAdded', label: 'Date Added', width: 'w-32' },
    { key: 'lastContact', label: 'Last Contact', width: 'w-32' },
    { key: 'action', label: 'Actions', width: 'w-32' },
];

const LeadManagementLayout = () => {
    const { push } = useRouter();
    const tableContainerRef = useRef<HTMLDivElement>(null);
    const actionDropdownRef = useRef<HTMLDivElement>(null);
    const [isSubmitting, setIsSubmitting] = useState(false);

    // NEW — replaces:  page, itemsPerPage
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 10,
        total: 0,
    });

    const [isAddLeadPopupOpen, setIsAddLeadPopupOpen] = useState(false);
    const [isBulkUploadPopupOpen, setIsBulkUploadPopupOpen] = useState(false);
    const [isBulkEmailPopupOpen, setIsBulkEmailPopupOpen] = useState(false);
    const [leadsData, setLeadsData] = useState<Lead[]>([]);
    const [loader, setLoader] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [showDropdown, setShowDropdown] = useState(false);
    const [dropUp, setDropUp] = useState(false);
    const dropdownButtonRef = useRef<HTMLButtonElement>(null);
    const [documentCounts, setDocumentCounts] = useState<LeadCountCard[]>([]);

    const debouncedSearchTerm = useDebouncedValue(searchTerm, 300);
    const [selectedStatus, setSelectedStatus] = useState('');
    const [selectedType, setSelectedType] = useState('');
    const [isEditMode, setIsEditMode] = useState(false);
    const [activeDropdownId, setActiveDropdownId] = useState<number | null>(null);
    const [selectedLeadIds, setSelectedLeadIds] = useState<string[]>([]);
    const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
    const [dropdownOpen, setDropdownOpen] = useState<number | null>(null);
    const [showContactPopup, setShowContactPopup] = useState(false);
    const [isReOpenPopup, setIsReOpenPopup] = useState(false);
    const [showDetailsPopup, setShowDetailsPopup] = useState(false);
    const [showConvertPopup, setShowConvertPopup] = useState(false);
    const [showAddNotePopup, setShowAddNotePopup] = useState(false);
    const [showMarkAsLostPopup, setShowMarkAsLostPopup] = useState(false);
    const [emailRecipients, setEmailRecipients] = useState<Array<{ name: string; email: string }>>([]);
    const [isIndividualEmail, setIsIndividualEmail] = useState(false);
    const [allLeadsMap, setAllLeadsMap] = useState<{ [id: string]: any }>({});
    const [leads, setLeads] = useState<Lead[]>([]);

    const getSelectedRecipientsForEmail = () => {
        return selectedLeadIds
            .map((id) => allLeadsMap[id])
            .filter(Boolean)
            .map((lead) => ({ name: lead?.fullName, email: lead?.email }));
    };

    // Modal open handlers
    const openBulkEmail = () => {
        const selected = getSelectedRecipientsForEmail();
        if (selected.length === 0) {
            showMessage('Please select at least one lead to send bulk email.', 'error');
            return;
        }
        setEmailRecipients(selected);
        setIsIndividualEmail(false);
        setIsBulkEmailPopupOpen(true);
    };

    const closeEmailModal = () => {
        setSelectedLeadIds([]);
        setIsBulkEmailPopupOpen(false);
    };

    const handleToggleDropdown = (id: number | null) => {
        setActiveDropdownId((prevId) => (prevId === id ? null : id));
    };

    const toggleDropdown = () => {
        if (dropdownButtonRef.current) {
            const rect = dropdownButtonRef.current.getBoundingClientRect();
            const spaceBelow = window.innerHeight - rect.bottom;
            const estimatedDropdownHeight = 150;
            setDropUp(spaceBelow < estimatedDropdownHeight);
        }
        setShowDropdown((prev) => !prev);
    };

    const filteredLeads = leadsData?.filter((lead) => {
        const term = debouncedSearchTerm?.toLowerCase();
        return lead?.fullName?.toLowerCase()?.includes(term) || lead?.email?.toLowerCase()?.includes(term) || lead?.company?.toLowerCase()?.includes(term) || lead?.phone?.toLowerCase().includes(term);
    });

    const handleDownloadCSV = () => {
        // Create CSV headers
        const headers = ['Name', 'Company', 'Type', 'Email', 'Phone', 'Added Date', 'Last Contact', 'Source', 'Status'].join(',');

        // Convert lead data to CSV rows
        const csvRows = filteredLeads?.map((lead) => {
            return [lead.fullName, lead.company, lead.leadType, lead.email, lead.phone, lead.created_at, lead.last_contact || '', lead.source, lead.statusName].join(',');
        });

        // Combine headers and rows
        const csvContent = [headers, ...csvRows].join('\n');

        // Create blob and download
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `leads_${new Date().toISOString().split('T')[0]}.csv`);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
    };

    const getLeadseData = async () => {
        try {
            // setLoader(true);

            /* ─── build query-string ─── */
            const qp = new URLSearchParams();
            qp.append('page', pagination.page.toString());
            qp.append('pageSize', pagination.pageSize.toString());

            if (selectedStatus) qp.append('status', selectedStatus);
            if (selectedType) {
                qp.append('filterColumn', 'leadType');
                qp.append('filterValue', selectedType);
            }

            qp.append('search', debouncedSearchTerm);
            const url = `${API_ENDPOINTS.LEADS}?${qp}`;

            /* ─── fetch ─── */
            const res = await fetch(url, {
                method: 'GET',
                credentials: 'include',
            });

            const result = await res.json();

            if (!result.success) {
                console.error(result.message);
                return;
            }

            /* ─── unpack payload ─── */
            const {
                leads,
                leadsCounts,
                pagination: p, // page / pageSize / returned / total
            } = result.data;

            /* table rows */
            setLeadsData(leads);
            setLeads(leads);

            /* status-count chips */
            setDocumentCounts(leadsCounts ?? []);

            /* id → lead map (for bulk email etc.) */
            setAllLeadsMap((prev) => {
                const updated = { ...prev };
                leads?.forEach((l: any) => (updated[l.id] = l));
                return updated;
            });

            /* ─── keep page in range ─── */
            if (p) {
                const totalPages = Math.max(1, Math.ceil(p.total / p.pageSize));

                // if we asked for page 5 but only 4 pages now exist -> jump to 4
                const safePage = Math.min(p.page, totalPages);

                setPagination({
                    ...p,
                    page: safePage,
                });
            } else {
                // fallback (old API without pagination.total)
                setPagination((prev) => ({
                    ...prev,
                    returned: leads?.length,
                    total: leads?.length,
                }));
            }
        } catch (err) {
            console.error('getLeadseData failed:', err);
        } finally {
            // setLoader(false);
        }
    };

    useEffect(() => {
        getLeadseData();
    }, [selectedStatus, selectedType, pagination.page, pagination.pageSize, debouncedSearchTerm]);

    const handleAddOrEditLead = async (data: any, isEdit: boolean) => {
        setIsSubmitting(true);
        const formData = new FormData();
        formData.append('fullName', data.fullName);
        formData.append('email', data.email);
        formData.append('phone', data.phone);
        formData.append('licenseNumber', data.licenseNumber);
        formData.append('company', data.company);
        formData.append('leadType', data.leadType);
        formData.append('source', data.source);

        setSelectedLead(
            isEdit
                ? { ...selectedLead, ...data }
                : ({
                      id: 0, // Temporary ID for new lead
                      fullName: data.fullName,
                      email: data.email,
                      phone: data.phone,
                      licenseNumber: data.licenseNumber,
                      company: data.company,
                      leadType: data.leadType,
                      source: data.source,
                      statusName: 'New', // Default status for new leads
                      created_at: new Date().toISOString(), // Set current date as created_at
                  } as Lead)
        );

        const url =
            isEdit && selectedLead
                ? `${API_ENDPOINTS.LEADS}/${selectedLead.id}` // Ensure `id` exists on Lead
                : API_ENDPOINTS.LEADS;

        const method = isEdit ? 'PUT' : 'POST';

        const requestOptions: RequestInit = {
            method,
            headers: new Headers(),
            credentials: 'include',
            body: formData,
        };

        try {
            const response = await fetch(url, requestOptions);
            const result = await response.json();

            if (result.success) {
                showMessage(`Lead ${isEdit ? 'updated' : 'added'} successfully`, 'success');
                setIsAddLeadPopupOpen(false);
                setSelectedLead(null);
                setIsEditMode(false);
                handleToggleDropdown(null);
                getLeadseData();
                setIsSubmitting(false);
            } else {
                showMessage(result.message || 'Something went wrong', 'error');
                setIsSubmitting(false);
            }
        } catch (error) {
            console.error('Error:', error);
            showMessage('Request failed.', 'error');
            setIsAddLeadPopupOpen(false);
            setSelectedLead(null);
            setIsEditMode(false);
            handleToggleDropdown(null);
            setIsSubmitting(false);
        }
    };

    const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { checked } = e.target;
        const currentPageUserIds = leads?.map((lead) => lead.id.toString());
        if (checked) {
            // Union: add all current page user IDs to selectedUserIds
            setSelectedLeadIds((prev) => Array.from(new Set([...prev, ...currentPageUserIds])));
        } else {
            // Remove all current page user IDs from selectedUserIds
            setSelectedLeadIds((prev) => prev.filter((id) => !currentPageUserIds.includes(id)));
        }
    };

    const handleSelectLead = (userId: string, e: React.ChangeEvent<HTMLInputElement>) => {
        const { checked } = e.target;
        if (checked) {
            setSelectedLeadIds((prev) => [...prev, userId]);
        } else {
            setSelectedLeadIds((prev) => prev.filter((id) => id !== userId));
        }
    };

    const handleActionClick = (lead: Lead, action: string) => {
        setSelectedLead(lead);
        setDropdownOpen(null);

        switch (action) {
            case 'view':
                setShowDetailsPopup(true);
                break;
            case 'edit':
                setIsAddLeadPopupOpen(true);
                setIsEditMode(true);
                break;
            case 'convert':
                handleConvertLead(lead.id);
                break;
            case 'note':
                setShowAddNotePopup(true);
                break;
            case 'lost':
                setShowMarkAsLostPopup(true);
                break;
            case 'contact':
                setShowContactPopup(true);
                break;
            case 'reopen':
                setIsReOpenPopup(true);
                setShowMarkAsLostPopup(true);
                break;
        }
    };

    const handleConvertLead = async (lead: number) => {
        try {
            setLoader(true);
            const response = await fetch(API_ENDPOINTS.CONVERT_LEADS, {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ userIds: [lead] }),
            });
            const result = await response.json();
            if (result.success) {
                showMessage(result.message || 'Lead converted successfully', 'success');
                getLeadseData();
            } else {
                showMessage(result.message || 'Something went wrong', 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showMessage('Request failed.', 'error');
        } finally {
            setShowConvertPopup(false);
            setSelectedLead(null);
            setLoader(false);
        }
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
        setPagination((prev) => ({
            ...prev,
            page: 1,
        }));
    };

    const totalPages = Math.ceil(pagination.total / pagination.pageSize);

    const currentPage = pagination.page; // 1-based

    /* helper that bumps page, guarding the edges */
    const setPageSafe = (n: number) =>
        setPagination((prev) => ({
            ...prev,
            page: Math.min(Math.max(n, 1), totalPages || 1),
        }));

    const handleSaveNote = async (id: number, note: string) => {
        try {
            const formData = new FormData();
            formData.append('note', note);
            const res = await fetch(`${API_ENDPOINTS.LEADS}/${id}/notes`, {
                method: 'POST',
                headers: new Headers(),
                credentials: 'include',
                body: formData,
            });

            const data = await res.json();

            if (!res.ok) {
                console.error('Failed to save note:', data?.message || 'Unknown error');
                return;
            }

            showMessage('Note added successfully', 'success');
            console.log('Note saved:', data);
            // You could trigger a refresh or a toast here
        } catch (err) {
            showMessage('Something is wents wrong', 'error');

            console.error('Error saving note:', err);
        } finally {
            setShowAddNotePopup(false);
        }
    };

    const handleMarkAsLost = async (id: number, reason: string) => {
        try {
            const formData = new FormData();
            formData.append('reason', reason);
            if (isReOpenPopup) {
                formData.append('status', 'Re-opened');
            }
            const res = await fetch(`${API_ENDPOINTS.LEADS}/${id}/mark-lost`, {
                method: 'PUT',
                headers: new Headers(),
                credentials: 'include',
                body: formData,
            });

            const data = await res.json();

            if (!res.ok) {
                console.error('Failed to mark as lost:', data?.message || 'Unknown error');
                showMessage(data?.message || 'Failed to mark lead as lost', 'error');
                return;
            }

            showMessage(isReOpenPopup ? 'Lead re-opened successfully' : 'Lead marked as lost successfully', 'success');
            getLeadseData();
            // You might want to trigger a data refresh here
        } catch (err) {
            showMessage('Something went wrong while marking lead as lost', 'error');
            console.error('Error marking lead as lost:', err);
        } finally {
            setShowMarkAsLostPopup(false);
            setIsReOpenPopup(false);
        }
    };

    return (
        <>
            <DefaultPageLayout>
                <BreadCrums
                    mainHeading="Lead Management"
                    breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Lead Management' }]}
                    ButonComponent={
                        <BreadCrumButton
                            onClick={() => {
                                push('/lead-management');
                            }}
                        />
                    }
                />

                {/* Header Section - Made Responsive */}
                <div className="mb-5 flex flex-col justify-end gap-4 px-4 pt-5 lg:flex-row lg:items-center lg:px-5">
                    {/* <div className="font-inter">
                        <h1 className="mb-1 text-xl font-bold text-gray-800 lg:text-2xl">Lead Management</h1>
                        <p className="text-sm text-gray-600">Manage potential agents and agencies</p>
                    </div> */}

                    {/* Buttons Container */}
                    <div className="flex w-full flex-col gap-3 font-inter sm:w-auto sm:flex-row">
                        <div className="flex w-full flex-col gap-3 sm:w-auto sm:flex-row">
                            <button
                                onClick={handleDownloadCSV}
                                className="flex items-center justify-center rounded-md border border-[#1D7EB6] px-4 py-2.5 text-sm text-[#1D7EB6] lg:px-6 lg:py-3 lg:text-base"
                            >
                                <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                Download CSV ({filteredLeads?.length})
                            </button>
                            <button
                                onClick={() => openBulkEmail()}
                                className="flex items-center justify-center rounded-md border border-[#1D7EB6] px-4 py-2.5 text-sm text-[#1D7EB6] lg:px-6 lg:py-3 lg:text-base"
                            >
                                Bulk Email ({selectedLeadIds?.length})
                            </button>
                        </div>
                        <div className="flex w-full flex-col gap-3 sm:w-auto sm:flex-row">
                            <button
                                onClick={() => setIsBulkUploadPopupOpen(true)}
                                className="flex items-center justify-center rounded-md border border-[#1D7EB6] px-4 py-2.5 text-sm text-[#1D7EB6] lg:px-6 lg:py-3 lg:text-base"
                            >
                                Bulk Upload
                            </button>
                            <button
                                onClick={() => setIsAddLeadPopupOpen(true)}
                                className="flex items-center justify-center rounded-md bg-[#1D7EB6] px-4 py-2.5 text-sm text-white hover:bg-[#1D7EB6]/90 lg:px-6 lg:py-3 lg:text-base"
                            >
                                <span className="mr-1">+</span> Add Lead
                            </button>
                        </div>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 px-5 pt-5 sm:grid-cols-2 lg:grid-cols-4">
                    {/* Total Leads card */}
                    <Card
                        icon={<UsersIcon />}
                        value={documentCounts.reduce((sum, item) => sum + parseInt(item.leadsCount || '0', 10), 0).toString()}
                        label="Total Leads"
                        description="Overall pipeline"
                        status="All statuses"
                        onClick={() => setSelectedStatus('')}
                    />

                    {/* Dynamic cards from statusOptions */}
                    {statusOptions
                        .filter((s) => selectedStatus === '' || selectedStatus === s.value)
                        .map((status) => {
                            const statusKey = status.value.toLowerCase();
                            const meta = statusCardMeta[statusKey];

                            const match = documentCounts.find((item) => item.statusName.toLowerCase() === statusKey);

                            const value = match?.leadsCount || '0';

                            return (
                                <Card
                                    key={statusKey}
                                    icon={meta?.icon}
                                    value={value}
                                    label={status.label}
                                    description={meta?.description || ''}
                                    status={meta?.status}
                                    onClick={() => setSelectedStatus(status.value!)}
                                />
                            );
                        })}
                </div>

                {/* Filters Section */}
                <Filters onSearchChange={handleSearchChange} onStatusChange={setSelectedStatus} onTypeChange={setSelectedType} selectedStatus={selectedStatus} selectedType={selectedType} />

                {/* Grid Container - Made Responsive */}
                <div className="px-4 lg:px-5 ">
                    <div className="bg-white">
                        <div ref={tableContainerRef} className="relative  overflow-hidden rounded-lg rounded-tl-lg rounded-tr-lg border shadow-[0px_4px_20px_0px_rgba(21,32,70,0.07)]">
                            {/* Table wrapper with horizontal scroll */}
                            <div className="overflow-x-auto ">
                                <table className="w-full min-w-[1200px] border-collapse font-inter ">
                                    <thead className="sticky top-0 z-[5] bg-[#e4e4e4]">
                                        <tr className="w-full rounded-lg rounded-tl-lg rounded-tr-lg border-none">
                                            {columns.map((column) => (
                                                <th
                                                    key={column.key}
                                                    className={`h-[72px] cursor-pointer border-none px-4 py-4 text-left font-inter text-base font-medium leading-normal text-[#2d2d2e] ${column.width}`}
                                                >
                                                    {column.key === 'checkbox' ? (
                                                        <input
                                                            type="checkbox"
                                                            checked={leads?.length > 0 && leads.every((lead) => selectedLeadIds.includes(lead.id.toString()))}
                                                            onChange={handleSelectAll}
                                                            className="h-4 w-4 rounded border-gray-300"
                                                        />
                                                    ) : (
                                                        <span className="flex items-center">{column.label}</span>
                                                    )}
                                                </th>
                                            ))}
                                        </tr>
                                    </thead>
                                </table>
                            </div>

                            {/* Table body with vertical scroll */}
                            <div className="overflow-x-auto overflow-y-auto">
                                <table className={`w-full min-w-[1200px] border-collapse font-inter ${loader ? 'pointer-events-none opacity-50' : ''}`}>
                                    <tbody>
                                        {leads?.length === 0 && (
                                            <tr>
                                                <td colSpan={10}>
                                                    <div className="flex items-center justify-center py-10 text-center text-base font-medium text-[#888]">No Records to Show</div>
                                                </td>
                                            </tr>
                                        )}

                                        {leads?.map((lead, index) => {
                                            /* helpers ----------------------------------------------------------- */
                                            const globalIndex = (pagination.page - 1) * pagination.pageSize + index;

                                            const safeText: (v: any) => string = (v) => (v && String(v).trim() ? v : '—');

                                            const initials = safeText(lead.fullName)
                                                .split(' ')
                                                .filter(Boolean)
                                                .map((n: string) => n[0])
                                                .join('')
                                                .toUpperCase();

                                            return (
                                                <tr key={globalIndex} className="text-grayText border-b border-[#E4E4E4] text-center hover:bg-gray-50">
                                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[0].width}`}>
                                                        <input
                                                            type="checkbox"
                                                            checked={selectedLeadIds.includes(lead.id.toString())}
                                                            onChange={(e) => handleSelectLead(lead.id.toString(), e)}
                                                            className="h-4 w-4 rounded border-gray-300"
                                                        />
                                                    </td>
                                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[1].width}`}>
                                                        <div className="flex items-center">
                                                            <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-[#1D7EB6] text-sm font-medium text-white">
                                                                {initials || '—'}
                                                            </div>
                                                            <div className="text-sm font-medium text-gray-900">{safeText(lead.fullName)}</div>
                                                        </div>
                                                    </td>
                                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[2].width}`}>
                                                        <div className="text-sm text-gray-900">
                                                            <div className="mb-1 flex items-center">
                                                                <svg
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                    width="20"
                                                                    height="20"
                                                                    viewBox="0 0 24 24"
                                                                    fill="none"
                                                                    stroke="#000000"
                                                                    strokeWidth="2"
                                                                    strokeLinecap="round"
                                                                    strokeLinejoin="round"
                                                                    className="mr-2 h-5 w-5"
                                                                >
                                                                    <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                                                                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                                                                </svg>
                                                                {lead.email ? (
                                                                    <Link href={`mailto:${lead.email}`} className="ml-2 text-xs text-blue-600 hover:underline">
                                                                        {lead.email}
                                                                    </Link>
                                                                ) : (
                                                                    <span className="ml-2 text-xs text-gray-500">—</span>
                                                                )}
                                                            </div>
                                                            <div className="flex items-center">
                                                                <svg
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                    width="20"
                                                                    height="20"
                                                                    viewBox="0 0 24 24"
                                                                    fill="none"
                                                                    stroke="#000000"
                                                                    strokeWidth="2"
                                                                    strokeLinecap="round"
                                                                    strokeLinejoin="round"
                                                                    className="mr-2 h-5 w-5"
                                                                >
                                                                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                                                                </svg>
                                                                {lead.phone ? (
                                                                    <Link href={`tel:${lead.phone}`} className="ml-2 text-xs text-gray-600 hover:underline">
                                                                        {lead.phone}
                                                                    </Link>
                                                                ) : (
                                                                    <span className="ml-2 text-xs text-gray-500">—</span>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[3].width}`}>
                                                        {safeText(lead.company)}
                                                    </td>
                                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal ${columns[4].width}`}>
                                                        <div className="flex flex-col items-center space-y-1">
                                                            {lead.leadType ? (
                                                                <span className={`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ${typeStyles[lead.leadType]}`}>
                                                                    {capitalizeFirst(lead.leadType)}
                                                                </span>
                                                            ) : (
                                                                <span className="inline-flex items-center rounded-md px-2 py-1 text-xs text-gray-500">—</span>
                                                            )}
                                                        </div>
                                                    </td>
                                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal ${columns[5].width}`}>
                                                        <div className="flex flex-col items-center space-y-1">
                                                            {lead.statusName ? (
                                                                <span className={`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ${statusStyles[lead.statusName]}`}>
                                                                    {capitalizeFirst(lead.statusName)}
                                                                </span>
                                                            ) : (
                                                                <span className="inline-flex items-center rounded-md px-2 py-1 text-xs text-gray-500">—</span>
                                                            )}
                                                        </div>
                                                    </td>
                                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[6].width}`}>
                                                        {safeText(lead.source)}
                                                    </td>
                                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[7].width}`}>
                                                        {lead.created_at ? new Date(lead.created_at).toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }) : '—'}
                                                    </td>
                                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[8].width}`}>
                                                        {lead.last_contact
                                                            ? new Date(lead.last_contact).toLocaleString('en-GB', {
                                                                  day: '2-digit',
                                                                  month: 'short',
                                                                  year: 'numeric',
                                                                  hour: '2-digit',
                                                                  minute: '2-digit',
                                                              })
                                                            : '—'}
                                                    </td>
                                                    <td className={`relative h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 ${columns[9].width}`}>
                                                        <div className="flex items-center justify-center space-x-2">
                                                            <button
                                                                onClick={() => {
                                                                    setEmailRecipients([{ name: lead.fullName, email: lead.email }]);
                                                                    setIsIndividualEmail(true);
                                                                    setIsBulkEmailPopupOpen(true);
                                                                }}
                                                                className="text-blue-500"
                                                                title="Send Email"
                                                            >
                                                                <svg
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                    width="20"
                                                                    height="20"
                                                                    viewBox="0 0 24 24"
                                                                    fill="none"
                                                                    stroke="#000000"
                                                                    strokeWidth="2"
                                                                    strokeLinecap="round"
                                                                    strokeLinejoin="round"
                                                                    className="h-5 w-5"
                                                                >
                                                                    <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                                                                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                                                                </svg>
                                                            </button>
                                                            <button
                                                                className="text-blue-500"
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    setDropdownOpen(dropdownOpen === globalIndex ? null : globalIndex);
                                                                }}
                                                            >
                                                                <ActionIcon />
                                                            </button>
                                                        </div>
                                                        {dropdownOpen === globalIndex && (
                                                            <div ref={actionDropdownRef} className="absolute right-0 top-full !z-[10] mt-2 w-48 rounded-md border border-gray-200 bg-white shadow-lg">
                                                                <div className="py-1">
                                                                    <span
                                                                        className="flex w-full cursor-pointer items-center px-4 py-2 text-left text-sm hover:bg-gray-100"
                                                                        onClick={() => handleActionClick(lead, 'view')}
                                                                    >
                                                                        <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                                            <path
                                                                                strokeLinecap="round"
                                                                                strokeLinejoin="round"
                                                                                strokeWidth={2}
                                                                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                                                            />
                                                                        </svg>
                                                                        View Details
                                                                    </span>
                                                                    <span
                                                                        onClick={() => handleActionClick(lead, 'edit')}
                                                                        className="flex w-full cursor-pointer items-center px-4 py-2 text-left text-sm hover:bg-gray-100"
                                                                    >
                                                                        <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                            <path
                                                                                strokeLinecap="round"
                                                                                strokeLinejoin="round"
                                                                                strokeWidth={2}
                                                                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                                                            />
                                                                        </svg>
                                                                        Edit Lead
                                                                    </span>
                                                                    <span
                                                                        onClick={lead.statusName?.toLowerCase() === 'converted' ? undefined : () => handleActionClick(lead, 'convert')}
                                                                        className={`flex w-full items-center px-4 py-2 text-left text-sm
                                                                        ${
                                                                            lead.statusName?.toLowerCase() === 'converted'
                                                                                ? 'pointer-events-none cursor-not-allowed opacity-50'
                                                                                : 'cursor-pointer hover:bg-gray-100'
                                                                        }`}
                                                                        aria-disabled={lead.statusName?.toLowerCase() === 'converted'}
                                                                    >
                                                                        <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                            <path
                                                                                strokeLinecap="round"
                                                                                strokeLinejoin="round"
                                                                                strokeWidth={2}
                                                                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                                                                            />
                                                                        </svg>
                                                                        Convert to User
                                                                    </span>

                                                                    <span
                                                                        onClick={() => handleActionClick(lead, 'note')}
                                                                        className="flex w-full cursor-pointer items-center px-4 py-2 text-left text-sm hover:bg-gray-100"
                                                                    >
                                                                        <NotebookPen className="mr-2 h-4 w-4" />
                                                                        Add Note
                                                                    </span>

                                                                    <span
                                                                        onClick={lead.statusName?.toLowerCase() === 'lost' ? undefined : () => handleActionClick(lead, 'lost')}
                                                                        className={`flex w-full items-center px-4 py-2 text-left text-sm text-[#993333]
                                                                        ${
                                                                            lead.statusName?.toLowerCase() === 'lost'
                                                                                ? 'pointer-events-none cursor-not-allowed opacity-50'
                                                                                : 'cursor-pointer hover:bg-gray-100'
                                                                        }`}
                                                                        aria-disabled={lead.statusName?.toLowerCase() === 'lost'}
                                                                    >
                                                                        <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" width="24" height="24" viewBox="0 0 24 24">
                                                                            <path fill="currentColor" d="M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6z" />
                                                                        </svg>
                                                                        Mark as Lost
                                                                    </span>

                                                                    {lead.statusName?.toLowerCase() === 'lost' && (
                                                                        <span
                                                                            onClick={() => handleActionClick(lead, 'reopen')}
                                                                            className="flex w-full cursor-pointer items-center px-4 py-2 text-left text-sm text-green-600 hover:bg-gray-100"
                                                                        >
                                                                            {/* rotate-left / undo icon */}
                                                                            <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" width="16" height="16" viewBox="0 0 16 16">
                                                                                <g fill="currentColor">
                                                                                    <path
                                                                                        fill-rule="evenodd"
                                                                                        d="M5.28 5.656L2 7.006l-.66-.26L0 3.506l.92-.38l.81 1.95a6.48 6.48 0 0 1 12.48 1.93h-1a5.48 5.48 0 0 0-10.64-1.28l2.32-1zm8.86 2.68l1.34 3.23l-.92.44l-.82-2a6.49 6.49 0 0 1-12.5-2h1v-.5a5.49 5.49 0 0 0 10.64 1.89l-2.25.93l-.39-.92l3.25-1.35z"
                                                                                        clip-rule="evenodd"
                                                                                    />
                                                                                    <circle cx="7.74" cy="7.54" r="1" />
                                                                                </g>
                                                                            </svg>
                                                                            Re-open&nbsp;Lead
                                                                        </span>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        )}
                                                    </td>
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                </table>

                                {loader && (
                                    <div className="absolute inset-0 z-10 flex items-center justify-center bg-white bg-opacity-60">
                                        <Loading />
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* PAGINATION */}
                        {leads?.length !== 0 && !loader && (
                            <div className="flex flex-col items-center gap-4 border-t border-[#E4E4E4] p-4 md:flex-row md:justify-between">
                                {/* ← → & page numbers */}
                                <div className="flex items-center gap-2">
                                    {/* previous */}
                                    <button onClick={() => setPageSafe(currentPage - 1)} disabled={currentPage === 1} className="rounded-md p-2 disabled:opacity-50">
                                        <PaginationRightIcon />
                                    </button>

                                    {/* numeric buttons */}
                                    <div className="flex space-x-1">
                                        {getPaginationRange(currentPage - 1, totalPages).map((i) => (
                                            <button
                                                key={i}
                                                onClick={() => setPageSafe(i + 1)}
                                                className={`rounded-md px-3 py-1 ${currentPage === i + 1 ? 'bg-[#1D7EB6] text-white' : 'hover:bg-gray-100'}`}
                                            >
                                                {i + 1}
                                            </button>
                                        ))}
                                    </div>

                                    {/* next */}
                                    <button onClick={() => setPageSafe(currentPage + 1)} disabled={currentPage === totalPages} className="rounded-md p-2 disabled:opacity-50">
                                        <PaginationLeftIcon />
                                    </button>
                                </div>

                                {/* page-size selector */}
                                <div className="relative flex items-center gap-2">
                                    <span className="text-sm text-gray-500">Showing</span>

                                    <div className="relative">
                                        <button ref={dropdownButtonRef} onClick={toggleDropdown} className="flex items-center gap-1 rounded-md border border-gray-200 bg-[#EDF5F9] px-2 py-1 text-sm">
                                            {pagination.pageSize}
                                            <PaginationDownIcon />
                                        </button>

                                        {showDropdown && (
                                            <div className={`absolute left-0 z-10 w-16 rounded-md border border-gray-200 bg-white shadow-lg ${dropUp ? 'bottom-full mb-2' : 'top-full mt-2'}`}>
                                                <div className="py-1">
                                                    {[10, 20, 50].map((value) => (
                                                        <button
                                                            key={value}
                                                            className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                                            onClick={() => {
                                                                setPagination((prev) => ({ ...prev, page: 1, pageSize: value }));
                                                                setShowDropdown(false);
                                                            }}
                                                        >
                                                            {value}
                                                        </button>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    <span className="text-sm text-gray-500">Leads out of {pagination.total}</span>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                <AddLeadPopup
                    isOpen={isAddLeadPopupOpen}
                    onClose={() => {
                        setIsAddLeadPopupOpen(false);
                        setSelectedLead(null);
                        handleToggleDropdown(null);
                        setIsEditMode(false);
                    }}
                    isSubmitting={isSubmitting}
                    onSubmit={handleAddOrEditLead}
                    initialValues={selectedLead}
                    isEdit={isEditMode}
                />

                {selectedLead && (
                    <LeadDetailsPopup
                        isOpen={showDetailsPopup}
                        onClose={() => setShowDetailsPopup(false)}
                        name={selectedLead.fullName}
                        company={selectedLead.company}
                        type={selectedLead.leadType}
                        email={selectedLead.email}
                        phone={selectedLead.phone}
                        addedDate={selectedLead.created_at}
                        lastContact={selectedLead?.last_contact}
                        source={selectedLead.source}
                        status={selectedLead.statusName}
                    />
                )}

                <MarkAsLostPopup
                    isOpen={showMarkAsLostPopup}
                    isReopen={isReOpenPopup}
                    id={selectedLead?.id!}
                    onClose={() => {
                        setShowMarkAsLostPopup(false);
                    }}
                    onMarkAsLost={handleMarkAsLost}
                    companyName={selectedLead?.company!}
                />

                {showAddNotePopup && (
                    <AddNotePopup
                        isOpen={showAddNotePopup}
                        id={selectedLead?.id!}
                        onClose={() => {
                            setShowAddNotePopup(false);
                        }}
                        onSave={handleSaveNote}
                        companyName={selectedLead?.company!}
                    />
                )}

                {isBulkUploadPopupOpen && <BulkUploadPopup isOpen={isBulkUploadPopupOpen} onClose={() => setIsBulkUploadPopupOpen(false)} refreshLeads={getLeadseData} />}

                {/* Modals */}
                <BulkEmailModal isOpen={isBulkEmailPopupOpen} onClose={closeEmailModal} recipients={emailRecipients} isIndividual={isIndividualEmail} isLead={true} onSubmitSuccess={getLeadseData} />
            </DefaultPageLayout>
        </>
    );
};

interface CardProps {
    icon?: React.ReactNode;
    value: string;
    label: string;
    description: string;
    status?: string;
    onClick?: () => void;
}

const Card = ({ icon, value, label, description, status, onClick }: CardProps) => (
    <div
        className="flex cursor-pointer flex-col rounded-[10px] border-2 border-[#e4e4e4] 
        bg-neutral-100 p-5 shadow-[0px_4px_44px_-4px_rgba(12,12,13,0.05)] 
        transition-colors hover:bg-neutral-50"
        onClick={onClick}
    >
        <div className="flex flex-col gap-3">
            <div>
                <div className="flex justify-between">
                    <div>
                        <div className="text-lg font-medium text-[#636363]">{label}</div>
                        <div className="font-golosText text-4xl font-semibold text-[#1D7EB6]">{value}</div>
                    </div>
                    <div className="">{icon && <div className="flex justify-end rounded-[4px] bg-[#107cba42] p-2">{icon}</div>}</div>
                </div>

                <div className="text-sm text-[#636363]">{description}</div>
                {status && <div className="mt-2 text-xs font-medium text-[#1D7EB6]">{status}</div>}
            </div>
        </div>
    </div>
);

export default LeadManagementLayout;
