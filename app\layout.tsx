import ProviderComponent from '@/components/layouts/provider-component';
import 'react-perfect-scrollbar/dist/css/styles.css';
import '../styles/tailwind.css';
import { Metadata } from 'next';
import { <PERSON>uni<PERSON> } from 'next/font/google';

import localFont from 'next/font/local';

import { ToastProvider } from '@/components/reusable/Notify';
import { ThemeProvider } from '@/components/reusable/Theme-provide';

export const metadata: Metadata = {
    title: {
        template: 'Find Any Agent Dashboard',
        default: 'Find Any Agent Dashboard',
    },
};
const nunito = Nunito({
    weight: ['400', '500', '600', '700', '800'],
    subsets: ['latin'],
    display: 'swap',
    variable: '--font-nunito',
});

const inter = localFont({
    src: [
        {
            path: './fonts/Inter--Bold.ttf',
            weight: '700',
        },
        {
            path: './fonts/Inter-Medium.ttf',
            weight: '500',
        },
        {
            path: './fonts/Inter-Regular.ttf',
            weight: '400',
        },
    ],
    variable: '--inter',
});

const golos = localFont({
    src: [
        {
            path: './fonts/golos-text-bold.ttf',
            weight: '700',
        },
        {
            path: './fonts/GolosText-Medium.ttf',
            weight: '500',
        },
        {
            path: './fonts/golos-text-regular.ttf',
            weight: '400',
        },
    ],
    variable: '--golos',
});

export default function RootLayout({ children }: { children: React.ReactNode }) {
    return (
        <html lang="en">
            <body className={`  ${inter.variable} ${golos.variable} antialiased`}>
                <ProviderComponent>
                    <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
                        <ToastProvider>{children}</ToastProvider>
                    </ThemeProvider>
                </ProviderComponent>
            </body>
        </html>
    );
}
