export enum ColorTheme {
  Blue = '#3B82F6',     // Tailwind blue-500
  Orange = '#F97316',   // Tailwind orange-500
  Teal = '#14B8A6',     // Tailwind teal-500
  Purple = '#8B5CF6',   // Tailwind purple-500
  Green = '#22C55E',    // Tailwind green-500
  Red = '#EF4444',      // Tailwind red-500
  Indigo = '#6366F1',   // Tailwind indigo-500
  Pink = '#EC4899',     // Tailwind pink-500
}

export const ColorThemeOptions = [
  { value: ColorTheme.Blue, label: 'Blue', className: 'bg-blue-500' },
  { value: ColorTheme.Orange, label: 'Orange', className: 'bg-orange-500' },
  { value: ColorTheme.Teal, label: 'Teal', className: 'bg-teal-500' },
  { value: ColorTheme.Purple, label: 'Purple', className: 'bg-purple-500' },
  { value: ColorTheme.Green, label: 'Green', className: 'bg-green-500' },
  { value: ColorTheme.Red, label: 'Red', className: 'bg-red-500' },
  { value: ColorTheme.Indigo, label: 'Indigo', className: 'bg-indigo-500' },
  { value: ColorTheme.Pink, label: 'Pink', className: 'bg-pink-500' },
]; 

export const COLOR_THEMES: Record<ColorTheme | string, string> = {
  [ColorTheme.Blue]: 'bg-blue-500',
  [ColorTheme.Orange]: 'bg-orange-500',
  [ColorTheme.Teal]: 'bg-teal-500',
  [ColorTheme.Purple]: 'bg-purple-500',
  [ColorTheme.Green]: 'bg-green-500',
  [ColorTheme.Red]: 'bg-red-500',
  [ColorTheme.Indigo]: 'bg-indigo-500',
  [ColorTheme.Pink]: 'bg-pink-500',
}; 