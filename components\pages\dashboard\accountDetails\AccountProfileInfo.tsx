import React, { useState } from 'react';
import Image from 'next/image';

import InputField from '@/components/reusable/InputField';

import { ButtonBorder } from '@/components/reusable/ButtonBorder';

import { RedInfoIcon } from '@/components/icon/Icon';
import images from '@/public/assets/images/main/agent-image.png';
import { Dropdown } from '@/components/reusable/Dropdown';
import SelectDate from '@/components/reusable/DatePicker';
import NoExpiryCheckbox from '@/components/reusable/CheckboxEx';
import SpecializationForm from '../verificationDetails/SpecializationForm';

export default function AccountProfileInfo() {
    const [disabled, setDisabled] = useState(false);
    return (
        <div className="mb-5 rounded-lg border p-5 font-inter">
            <div className="flex flex-col gap-4 pt-4 md:flex-row md:gap-0">
                <div className="flex flex-col gap-6 md:w-2/3">
                    <div>
                        <p className="pb-3 pl-3 text-lg font-bold text-[#2d2d2e]">Profile Information</p>

                        <div className="flex flex-wrap">
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <InputField id="username" label="Username*" value="mabdullah231" placeholder="Input" />
                            </div>
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <InputField id="account-name" label="Account Name*" value="M Abdullah" placeholder="Input" />
                            </div>
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <InputField id="contact" label="Contact*" value="+12 3456 789" placeholder="Input" />
                            </div>
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <InputField id="email" label="Email*" value="<EMAIL>" placeholder="Input" />
                            </div>
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <InputField id="location" label="Location*" value="Dubai" placeholder="Input" />
                            </div>

                            <div className="w-full px-2 py-2">
                                <InputField id="short-description" label="Short Description" value="Deals in rent, sale, and purchase of residential properties." placeholder="Input" />
                            </div>
                            <div className="w-full px-2 py-2">
                                <InputField id="short-description" label="Description" value="Lorem ipsum dolor sit amet consectetur. " placeholder="Input" />
                            </div>
                            <div className="w-full px-2 py-2">
                                <InputField id="address-line-1" label="Address Line 1*" value="Building 123, 21a Street, Al Qusais 3, Dubai." placeholder="Input" />
                            </div>
                            <div className="w-full px-2 py-2">
                                <InputField id="address-line-2" label="" value="" placeholder="Address Line 2" />
                            </div>
                        </div>
                    </div>
                    <div>
                        <p className="pb-3 pl-3 text-lg font-bold">Account Type and Association</p>
                        <div className="flex flex-wrap">
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <Dropdown label="Select industry*" />
                            </div>
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <Dropdown label="Select Type*" />
                            </div>
                        </div>
                        <div className="flex flex-wrap">
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <Dropdown label="Association*" />
                            </div>
                            <div className="w-full px-2 py-2 md:w-1/2">
                                <Dropdown label="Associated Company Name*" />
                            </div>
                        </div>
                    </div>
                </div>
                <div className="pl-5 md:w-1/3">
                    <div className="ml-auto flex flex-col items-center gap-3 md:block md:max-w-[80%]">
                        <Image src={images} alt="agent-image" className="h-full w-full" width={300} height={300} />
                        <div className="w-auto pt-3 md:w-full">
                            <ButtonBorder value="Upload Photo" />
                        </div>
                        <div className="text-grayText flex items-center gap-2 py-3 pl-2 text-sm">
                            <RedInfoIcon className="h-5 w-5" />
                            <p>Upload 500x500 pixel images.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div className="pt-5">
                <p className="pb-3 pl-3 text-lg font-bold">Verification Details</p>
                <div className="flex flex-wrap">
                    <div className="w-full px-2 py-2 md:w-1/3">
                        <Dropdown label="Lorem Ipsum*" />
                    </div>
                    <div className="w-full px-2 py-2 md:w-1/3">
                        <Dropdown label="Lorem Ipsum*" />
                    </div>
                    <div className="w-full px-2 py-2 md:w-1/3">
                        <InputField id="username" label="Lorem Ipsum*" value="Lorem Ipsum" placeholder="Input" />
                    </div>
                </div>
            </div>

            <div className="pt-5">
                <p className="pb-3 pl-3 text-lg font-bold">License/Compliance Information</p>
                <div className="flex flex-wrap">
                    <div className="w-full px-2 py-2 md:w-1/3">
                        <Dropdown label="Select industry*" />
                    </div>
                    <div className="w-full px-2 py-2 md:w-1/3">
                        <InputField id="address-line-2" label="" value="" placeholder="Issued By*" />
                    </div>
                    <div className="w-full px-2 py-2 md:w-1/3">
                        <InputField id="address-line-2" label="" value="" placeholder="License/Certificate Number*" />
                    </div>
                    <div className="w-full px-2 py-2 md:w-1/3">

                        <SelectDate />
                    </div>
                    <div className="text-grayText flex w-full items-center justify-between px-4 md:w-1/3">
                        <NoExpiryCheckbox onChange={(checked) => setDisabled(checked)} />
                    </div>
                </div>
            </div>
            <div className="w-full py-5 pl-2 pt-10 font-inter">
                <div className="pb-3 pl-3">
                    <p className=" pb-2 text-lg font-bold">Specialization</p>
                    <p className="text-base text-[#636363]">
                        Add areas of expertise you want to highlight e.g., Digital Marketing, Financial Consulting, Graphic Design, Legal Services, Property Management.
                    </p>
                </div>

                <div className="md:p-3">
                    <SpecializationForm />
                </div>
            </div>
        </div>
    );
}
