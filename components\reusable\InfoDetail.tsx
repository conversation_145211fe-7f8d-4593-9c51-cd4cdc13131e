import Image from 'next/image';
import { CopyIcon, HeartIcon, InfoIcon, TypeIcon, MessageIcon, ReviewStarIcon } from '../icon/Icon';
import StatusBadge from './StatusBandage';
import { useToast } from './Notify';

export const InfoDetail = ({ infoData, title, copyLink }: any) => {
    const { showToast } = useToast();
    return (
        <div className=" flex  w-full flex-col overflow-hidden rounded-lg border  border-[#e4e4e4] font-inter">
            <div className="flex h-[60px] w-full items-center justify-between bg-[#e4e4e4] px-4">
                <span className="text-base font-medium text-[#2d2d2e] md:text-xl">{title}</span>
                {copyLink && (
                    <span onClick={() => showToast('Link copied! You can now paste it anywhere.', 'success', 5000)} className="cursor-pointer font-inter text-sm  font-medium   text-[#1D7EB6]">
                        {copyLink}
                    </span>
                )}
            </div>
            <div className="overflow-y-auto">
                {infoData.map((field: any, index: any) => (
                    <div key={index} className="flex border-t border-[#e4e4e4] bg-white">
                        {!field.no ? (
                            <>
                                <div className="flex w-1/3 items-start bg-neutral-100 py-2 pl-2 pr-2 md:py-3 md:pl-4">
                                    <span className="w-full break-words text-sm font-normal text-[#2d2d2e] md:text-base">{field.label}</span>
                                </div>

                                <div className={`flex w-2/3 items-center p-4 py-3 text-sm font-normal text-[#636363] md:text-base `}>
                                    {field.label === 'Status' ? (
                                        <span>
                                            <StatusBadge status={field.value} />{' '}
                                        </span>
                                    ) : (
                                        <>
                                            {field.reviews ? (
                                                <span className="flex flex-wrap items-center justify-start gap-2  pe-2">
                                                    <ReviewStarIcon />
                                                </span>
                                            ) : (
                                                ''
                                            )}
                                            {field.value}
                                        </>
                                    )}

                                    {field.label === 'Campain ID' && (
                                        <span className="cursor-pointer  ps-2">
                                            {' '}
                                            <CopyIcon />
                                        </span>
                                    )}

                                    {field.link && (
                                        <span className="h-32 w-32 cursor-pointer rounded-lg border p-2">
                                            <Image src={field.link} alt="Picture of the author" className="h-full w-full rounded-lg object-cover" width={1000} height={1000} />
                                        </span>
                                    )}

                                    {field.info ? (
                                        <span className="flex w-full items-center  justify-between gap-2">
                                            <div>
                                                <StatusBadge status={field.value} />{' '}
                                            </div>
                                            <div>
                                                <InfoIcon />
                                            </div>
                                        </span>
                                    ) : (
                                        ''
                                    )}
                                    {field.tag ? (
                                        <span className="flex w-full flex-wrap items-center justify-start  gap-2">
                                            {[3, 3, 4, 4].map((tag: any, index: any) => (
                                                <div
                                                    key={index}
                                                    className="inline-flex h-[26px] items-center justify-center gap-2.5 rounded  border border-[#959595] bg-neutral-100 p-2 text-center text-sm font-medium text-[#959595]"
                                                >
                                                    Tag {index + 1}
                                                </div>
                                            ))}
                                        </span>
                                    ) : (
                                        ''
                                    )}
                                    {field.badges ? (
                                        <span className="flex w-full flex-wrap items-center justify-start  gap-2">
                                            <TypeIcon />
                                            <HeartIcon />
                                        </span>
                                    ) : (
                                        ''
                                    )}
                                </div>
                            </>
                        ) : (
                            <div className="flex w-full items-start bg-white  py-2 pl-2 pr-2 md:py-3 md:pl-4">
                                <span className="w-full break-words text-sm font-medium text-[#2D2D2E] md:text-base">{field.label}</span>
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
};
