import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  isLoading: false,
};

const loadingSlice = createSlice({
  name: "loading",
  initialState,
  reducers: {
    setIsLoading(state, action) {
      state.isLoading = action.payload;
    },

    toggleIsLoading(state) {
      state.isLoading = !state.isLoading;
    },
  },
});

export const { setIsLoading, toggleIsLoading } = loadingSlice.actions;

export default loadingSlice.reducer;
