<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>BZ</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#094995" offset="0%"></stop>
            <stop stop-color="#074185" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#D5182F" offset="0%"></stop>
            <stop stop-color="#CC162C" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="BZ">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Rectangle-2-Copy-4" fill="url(#linearGradient-2)" x="0" y="2" width="21" height="11"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-3)" x="0" y="0" width="21" height="2"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-3)" x="0" y="13" width="21" height="2"></rect>
            <circle id="Oval-12" fill="url(#linearGradient-1)" cx="10.5" cy="7.5" r="4.5"></circle>
            <path d="M10.5,11 C10.2238576,11 10,10.7761424 10,10.5 C10,10.2238576 10.2238576,10 10.5,10 C10.783679,10 11.0602516,9.9529391 11.3219255,9.86189033 C11.5827313,9.77114362 11.8677209,9.90900372 11.9584676,10.1698095 C12.0492143,10.4306153 11.9113542,10.7156048 11.6505484,10.8063515 C11.2835669,10.9340418 10.8959367,11 10.5,11 Z M13.1511931,9.78506629 C13.406745,9.48879706 13.6113949,9.15160754 13.755598,8.78712579 C13.8571885,8.53034969 13.7313858,8.2398361 13.4746097,8.13824553 C13.2178336,8.03665496 12.92732,8.16245769 12.8257294,8.41923379 C12.7228617,8.67923811 12.576689,8.92007816 12.3939694,9.13191064 C12.2136055,9.34101219 12.2369019,9.65673635 12.4460034,9.83710031 C12.655105,10.0174643 12.9708291,9.99416783 13.1511931,9.78506629 Z M13.9681136,7.02598701 C13.9151393,6.63525776 13.7969147,6.25940768 13.6195772,5.91169522 C13.4941162,5.66569899 13.1929906,5.56798589 12.9469943,5.69344691 C12.7009981,5.81890792 12.603285,6.12003351 12.728746,6.36602974 C12.8551834,6.6139404 12.9393986,6.88167049 12.9771796,7.16033603 C13.014279,7.43397492 13.2661822,7.62572801 13.5398211,7.58862856 C13.81346,7.5515291 14.0052131,7.2996259 13.9681136,7.02598701 Z M12.3566317,4.53255799 C12.0253409,4.32490498 11.6610887,4.1738376 11.2774715,4.08677105 C11.0081779,4.02565162 10.7403252,4.19441011 10.6792058,4.46370368 C10.6180864,4.73299725 10.7868449,5.00084991 11.0561384,5.06196933 C11.3295087,5.12401401 11.5891154,5.23168146 11.8255367,5.37987023 C12.0595155,5.52652808 12.3680825,5.45574046 12.5147403,5.22176164 C12.6613982,4.98778282 12.5906105,4.67921584 12.3566317,4.53255799 Z M9.5575528,4.12848171 C9.17912609,4.23407333 8.82257836,4.40271264 8.5015931,4.62628761 C8.27499938,4.7841163 8.21925431,5.09575224 8.37708301,5.32234596 C8.5349117,5.54893969 8.84654764,5.60468475 9.07314136,5.44685606 C9.30235338,5.28720365 9.55662191,5.16694018 9.82631439,5.09168844 C10.0922966,5.01747198 10.2477534,4.74168648 10.173537,4.47570428 C10.0993205,4.20972209 9.823535,4.05426525 9.5575528,4.12848171 Z M7.34980403,5.9732204 C7.1794218,6.32421179 7.06864103,6.70226234 7.02331764,7.09421416 C6.99159725,7.36852863 7.18825862,7.6166189 7.46257309,7.64833928 C7.73688756,7.68005966 7.98497782,7.4833983 8.01669821,7.20908383 C8.04903512,6.9294377 8.12794951,6.6601344 8.24941219,6.40991841 C8.37000302,6.16149848 8.26637705,5.86235615 8.01795712,5.74176532 C7.76953719,5.6211745 7.47039486,5.72480046 7.34980403,5.9732204 Z M7.29030361,8.89755956 C7.44684558,9.25665095 7.66282722,9.58666982 7.92825784,9.874078 C8.11560955,10.0769424 8.43194217,10.0895179 8.63480653,9.90216614 C8.83767089,9.71481443 8.85024638,9.39848181 8.66289467,9.19561745 C8.47304982,8.99005351 8.31870545,8.75421609 8.20698559,8.49794206 C8.09663426,8.24480732 7.80197059,8.12905849 7.54883585,8.23940982 C7.29570111,8.34976114 7.17995228,8.64442482 7.29030361,8.89755956 Z M9.43580227,10.8351354 C9.17271015,10.7512477 9.02743627,10.469965 9.11132398,10.2068729 C9.19521168,9.94378077 9.47649441,9.79850688 9.73958653,9.88239459 C9.96870828,9.95545074 10.2085454,9.99517772 10.454027,9.99958744 C10.7301248,10.0045471 10.9499257,10.2323893 10.944966,10.5084871 C10.9400063,10.7845849 10.7121642,11.0043858 10.4360664,10.9994261 C10.0932451,10.9932679 9.75713989,10.9375949 9.43580227,10.8351354 Z" id="Oval-73" fill="#118014" fill-rule="nonzero"></path>
        </g>
    </g>
</svg>