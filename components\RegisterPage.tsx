'use client';
import React, { createContext, useContext, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { showMessage } from '@/app/lib/Alert';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import ImageComponent from './auth/ImageComponet';
import { EyeIcon, EyeIconPassword } from './icon/Icon';
import Link from 'next/link';

const labelBase = 'block text-sm font-medium text-gray-700 mb-2';
const inputBase = 'px-4 py-2 h-14 rounded-lg border text-black focus:outline-none font-normal w-full text-sm focus:ring-[#1d7eb6] focus:border-[#1d7eb6]';

interface FormContextType {
    userType: 'individual' | 'company' | null;
    setUserType: (type: 'individual' | 'company' | null) => void;
    currentStep: number;
    setCurrentStep: (step: number) => void;
    totalSteps: number;
}

const FormContext = createContext<FormContextType | undefined>(undefined);

const FormProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [currentStep, setCurrentStep] = useState(1);
    const [userType, setUserType] = useState<'individual' | 'company' | null>(null);
    const totalSteps = 2;

    return <FormContext.Provider value={{ userType, setUserType, currentStep, setCurrentStep, totalSteps }}>{children}</FormContext.Provider>;
};

const useForm = () => {
    const context = useContext(FormContext);
    if (!context) throw new Error('useForm must be used within a FormProvider');
    return context;
};

const ProgressBar: React.FC = () => {
    const { currentStep, totalSteps } = useForm();
    const progressPercentage = (currentStep / totalSteps) * 100;

    return (
        <div className="mb-6 w-full">
            <div className="flex mb-2 justify-end">
                <span className="text-sm font-medium text-gray-600">
                    {currentStep} of {totalSteps} steps
                </span>
            </div>
            <div className="h-2 w-full rounded-full bg-gray-200">
                <div className="h-2 rounded-full bg-[#1d7eb6] transition-all duration-300 ease-out" style={{ width: `${progressPercentage}%` }} />
            </div>
        </div>
    );
};

const UserTypeStep: React.FC = () => {
    const { setUserType, setCurrentStep, userType } = useForm();

    return (
        <div className="space-y-4">
            <h2 className="text-center text-2xl font-semibold">Who Are You Registering As?</h2>
            <div className="space-y-4">
                <button
                    type="button"
                    onClick={() => {
                        setUserType('individual');
                        setCurrentStep(2);
                    }}
                    className={` w-full rounded-lg border p-4 transition-colors hover:border-[#1d7eb6]
            ${userType === 'individual' ? 'border-[#1d7eb6] bg-[#1d7eb6] text-white' : ' '}
            `}
                >
                    Individual (e.g., Broker, Agent, Freelancer, Consultant, Manager)
                </button>
                <button
                    type="button"
                    onClick={() => {
                        setUserType('company');
                        setCurrentStep(2);
                    }}
                    className={`w-full rounded-lg border p-4 transition-colors hover:border-[#1d7eb6]
            ${userType === 'company' ? 'border-[#1d7eb6] bg-[#1d7eb6] text-white' : ''}
            `}
                >
                    Company / Agency / Property Developer
                </button>
            </div>

            <div className="pt-8 text-center md:pt-20 2xl:pt-40">
                <span className="  pe-2 text-base font-medium leading-tight text-[#636363]">Already have an account?</span>

                <Link href={'/login'} className="text-center   text-lg font-medium leading-normal text-[#1d7eb6]">
                    Login
                </Link>
            </div>
        </div>
    );
};

const AccountSetupStep: React.FC = () => {
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const { setCurrentStep, userType } = useForm();
    const router = useRouter();

    const validationSchema = Yup.object({
        email: Yup.string().email('Invalid email').required('Email is required'),
        password: Yup.string()
            .required('Password is required')
            .min(8, 'Password must be at least 8 characters')
            .matches(/[A-Z]/, 'Must contain an uppercase letter')
            .matches(/[a-z]/, 'Must contain a lowercase letter')
            .matches(/\d/, 'Must contain a number')
            .matches(/[@$!%*?&]/, 'Must contain a special character'),
        confirmPassword: Yup.string()
            .oneOf([Yup.ref('password')], 'Passwords must match')
            .required('Confirm Password is required'),
    });

    const handleSubmit = async (values: any) => {
        const userTypeData = userType === 'individual' ? 'INDIVIDUAL' : 'COMPANY_OR_AGENCY';

        const formData = new FormData();
        formData.append('accountType', userTypeData);
        formData.append('email', values.email);
        formData.append('password', values.password);
        formData.append('confirmPassword', values.confirmPassword);

        try {
            // const response = await fetch(API_ENDPOINTS.BASIC_REGISTRATION, {
            //     method: 'POST',
            //     body: formData,
            // });

            // const result = await response.json();

            // if (result.success) {
            //     localStorage.setItem('tempmail', values.email);
            //     showMessage(result?.message, 'success');
            //     setTimeout(() => {
            //         router.push('/otp-verification');
            //     }, 1500);
            // } else {
            //     showMessage(result?.message, 'error');
            // }
        } catch (error) {
            console.error(error);
            showMessage('An error occurred. Please try again.', 'error');
        }
    };

    return (
        <Formik initialValues={{ email: '', password: '', confirmPassword: '' }} validationSchema={validationSchema} onSubmit={handleSubmit}>
            {({ isValid, isSubmitting }) => (
                <Form className="space-y-4">
                    <h2 className="text-center text-2xl font-semibold">Account Setup</h2>

                    <div className="h-24">
                        <label className={labelBase}>Email*</label>
                        <Field name="email" type="email" className={inputBase} placeholder="Enter your email address" autoComplete="email" autoFocus />
                        <ErrorMessage name="email" component="div" className="text-sm text-red-500" />
                    </div>

                    <div className="relative h-24">
                        <label className={labelBase}>Password*</label>
                        <Field name="password" type={showPassword ? 'text' : 'password'} className={inputBase} placeholder="Enter your password" />
                        <button type="button" onClick={() => setShowPassword(!showPassword)} className="absolute right-3 top-12">
                            {showPassword ? <EyeIconPassword /> : <EyeIcon />}
                        </button>
                        <ErrorMessage name="password" component="div" className="text-sm text-red-500" />
                    </div>

                    <div className="relative h-24">
                        <label className={labelBase}>Confirm Password*</label>
                        <Field name="confirmPassword" type={showConfirmPassword ? 'text' : 'password'} className={inputBase} placeholder="Re-enter your password" />
                        <button type="button" onClick={() => setShowConfirmPassword(!showConfirmPassword)} className="absolute right-3 top-11">
                            {showConfirmPassword ? <EyeIconPassword /> : <EyeIcon />}
                        </button>
                        <ErrorMessage name="confirmPassword" component="div" className="text-sm text-red-500" />
                    </div>

                    <div className="flex sticky bottom-0 mt-6 justify-between border-t bg-white py-4">
                        <button type="button" onClick={() => setCurrentStep(1)} className="rounded-lg border px-4 py-2 hover:bg-gray-100">
                            Back
                        </button>
                        <button
                            type="submit"
                            disabled={!isValid || isSubmitting}
                            className={`rounded-lg bg-[#1d7eb6] px-4 py-2 text-white hover:bg-[#17648f] ${!isValid || isSubmitting ? 'cursor-not-allowed opacity-50' : ''}`}
                        >
                            Submit
                        </button>
                    </div>
                </Form>
            )}
        </Formik>
    );
};

const RegisterForm: React.FC = () => {
    const { currentStep } = useForm();

    return (
        <div className="mx-auto w-full max-w-2xl p-6">
            <ProgressBar />
            <div className="grid h-[calc(100vh-180px)] items-center overflow-y-auto rounded-lg bg-white p-6">
                <div className="space-y-6">{currentStep === 1 ? <UserTypeStep /> : <AccountSetupStep />}</div>
            </div>
        </div>
    );
};

export default function RegisterPage() {
    return (
        <FormProvider>
            <div className="flex w-full items-center justify-center overflow-hidden bg-white font-inter">
                <div className="flex min-h-screen w-full items-center md:w-1/2">
                    <RegisterForm />
                </div>
                <div className="hidden md:block md:w-1/2">
                    <ImageComponent />
                </div>
            </div>
        </FormProvider>
    );
}
