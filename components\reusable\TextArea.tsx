import React, { type FC, type ReactNode } from "react";

interface InputProps {
  id: string;
  label?: string;
  placeholder?: string;
  className?: string;
  onChange?: any;
  value?: string;
  disable?: boolean;
  required?: boolean;
  rows?: number;
}

const TextArea: FC<InputProps> = ({
  id,
  label,
  placeholder,
  onChange,
  value,
  className = "",
  disable,
  required,
  rows = 5
}) => {
  return (
    <div className="relative  ">
      <div className="relative">
        <textarea
          id={id}
          name={id}
          rows={rows}
          placeholder={placeholder}
          className={`px-4 py-4   rounded-lg border text-black focus:outline-none font-normal w-full text-sm ${className}   `}
          onChange={onChange}
          value={value}
          disabled={disable}
          required={required}
        />
      </div>
    </div>
  );
};

export default TextArea;
