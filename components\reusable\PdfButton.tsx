import React, { useState } from "react";
import { CrossIcon, PdfIcon } from "../icon/Icon";

interface CustomFileUploaderProps {
  onFileChange?: (file: File | null) => void;
}

const CustomFileUploader: React.FC<CustomFileUploaderProps> = ({ onFileChange }) => {
  const [file, setFile] = useState<File | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const selectedFile = event.target.files[0];

      // Allow only PDFs and images
      if (!selectedFile.type.includes("pdf") && !selectedFile.type.includes("image")) {
        alert("Only PDF and image files are allowed.");
        return;
      }

      setFile(selectedFile);
      if (onFileChange) {
        onFileChange(selectedFile);
      }
    }
  };

  const removeFile = () => {
    setFile(null);
    if (onFileChange) {
      onFileChange(null);
    }
  };

  return (
    <div className="flex items-center gap-3 pt-4">
      {/* Upload Button */}
      <label className="cursor-pointer border border-[#1D7EB6] px-5 py-3 flex items-center gap-2 text-[#1D7EB6] font-normal  rounded-lg hover:bg-[] transition">
        Upload
        <input type="file" className="hidden" accept=".pdf, image/*" onChange={handleFileChange} />
      </label>

      {/* File Preview */}
      {file && (
        <div className="flex items-center gap-2 font-inter bg-gray-100 px-3 py-4 rounded-lg border border-gray-300">
            <PdfIcon/>
          <span className="text-[#636363] text-sm">{file.name}</span>
          <button onClick={removeFile} className="text-gray-500 hover:text-red-500 transition">
            {/* Custom Close Icon */}
           <CrossIcon/>
          </button>
        </div>
      )}
    </div>
  );
};

export default CustomFileUploader;
