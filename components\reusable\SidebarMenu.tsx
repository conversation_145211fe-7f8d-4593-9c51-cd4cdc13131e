'use client';

import React, { useRef, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { BlogIcon, DashboardIcon, DocumentIcon, DownIcon, LeadManagementIcon, MultiUsersIcon, ReportIcon, SettingsIcon, UserIcon, HelpIcon } from '../icon/Icon';

interface MenuItem {
    title: string;
    icon?: any;
    activeIcon?: any;
    hasDropdown?: boolean;
    path?: string;
    children?: { title: string; path: string }[];
}

const menuItems: MenuItem[] = [
    {
        title: 'Dashboard',
        icon: <DashboardIcon />,
        activeIcon: <DashboardIcon fillColor="#ffffff" />,
        path: '/',
    },
    {
        title: 'Manage Agents/Agencies',
        icon: <UserIcon />,
        activeIcon: <UserIcon fillColor="#ffffff" />,
        hasDropdown: true,
        children: [{ title: 'Agents Applications', path: '/dashboard/agents' }],
    },
    {
        title: 'Manage Subscription',
        icon: <ReportIcon />,
        activeIcon: <ReportIcon fillColor="#ffffff" />,
        hasDropdown: true,
        children: [
            { title: 'Packages & Features', path: '/subscription/packages' },
            { title: 'Discounts', path: '/discounts' },
        ],
    },
    {
        title: 'Documents Management',
        icon: <DocumentIcon />,
        activeIcon: <DocumentIcon fillColor="#ffffff" />,
        path: '/documents-management',
    },
    {
        title: 'Support Tickets',
        icon: <HelpIcon />,
        activeIcon: <HelpIcon fillColor="#ffffff" />,
        path: '/support-tickets',
    },
    {
        title: 'Lead Management',
        icon: <LeadManagementIcon />,
        activeIcon: <LeadManagementIcon fillColor="#ffffff" />,
        path: '/dashboard/lead-management',
    },
    {
        title: 'User Management',
        icon: <MultiUsersIcon />,
        activeIcon: <MultiUsersIcon fillColor="#ffffff" />,
        path: '/user-management',
    },

    {
        title: 'Reviews Management',
        icon: <MultiUsersIcon />,
        activeIcon: <MultiUsersIcon fillColor="#ffffff" />,
        path: '/reviews-management',
    },
    {
        title: 'Settings',
        icon: <SettingsIcon />,
        activeIcon: <SettingsIcon fillColor="#ffffff" />,
        path: '/settings',
    },
    {
        title: 'Blog Management',
        icon: <BlogIcon />,
        activeIcon: <BlogIcon fillColor="#ffffff" />,
        path: '/blog-management',
    },
];

export function SidebarMenu({ setShowSidebar }: any) {
    const pathname = usePathname();
    const [openMenus, setOpenMenus] = React.useState<string[]>([]);
    const [hoveredMenu, setHoveredMenu] = React.useState<string | null>(null);
    const menuRef = useRef<HTMLDivElement>(null);

    const isActive = (item: MenuItem) => {
        if (item.path && pathname === item.path) return true;
        if (item.children?.some((child) => pathname === child.path)) return true;
        return false;
    };

    // Auto open dropdown if its child route is active
    useEffect(() => {
        const parent = menuItems.find((item) => item.hasDropdown && item.children?.some((child) => child.path === pathname));
        if (parent) {
            setOpenMenus([parent.title]);
        }
    }, [pathname]);

    // Only allow one dropdown to open at a time
    const toggleMenu = (title: string) => {
        setOpenMenus((prev) => (prev.includes(title) ? [] : [title]));
    };

    // Close on outside click
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                setOpenMenus([]);
                setHoveredMenu(null);
                setShowSidebar(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);
    const handleLinkClick = () => {
        if (setShowSidebar) {
          setShowSidebar(false)
        }
      }
    return (
        <div ref={menuRef}>
            {menuItems.map((item) => {
                const active = isActive(item);
                const isOpen = openMenus.includes(item.title);
                const isHovered = hoveredMenu === item.title;
                const showActiveIcon = active || isOpen || isHovered;

                return (
                    <div key={item.title}>
                        {item.hasDropdown ? (
                            <button
                                onClick={() => toggleMenu(item.title)}

                                onMouseEnter={() => setHoveredMenu(item.title)}
                                onMouseLeave={() => setHoveredMenu(null)}
                                className={`flex w-full items-center border-b border-[#e4e4e4] px-4 py-3 transition-colors ${
                                    showActiveIcon ? 'bg-[#8B3A3A] text-white' : 'text-[#636363]'
                                } hover:bg-[#8B3A3A] hover:text-white`}
                            >
                                <span className="flex flex-1 items-center gap-3">
                                    {showActiveIcon ? item.activeIcon : item.icon}
                                    <span className="text-sm">{item.title}</span>
                                </span>
                                <DownIcon className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} fillColor={showActiveIcon ? '#ffffff' : ''} />
                            </button>
                        ) : (
                            <div className="">
                                <Link
                                    href={item.path || '#'}
                                    onClick={handleLinkClick}
                                    onMouseEnter={() => setHoveredMenu(item.title)}
                                    onMouseLeave={() => setHoveredMenu(null)}
                                    className={`flex w-full items-center border-b border-[#e4e4e4] px-4 py-3 transition-colors ${
                                        active || isHovered ? 'bg-[#8B3A3A] text-white' : 'text-[#636363]'
                                    } hover:bg-[#8B3A3A] hover:text-white`}
                                >
                                    <span className="flex flex-1 items-center gap-3">
                                        {active || isHovered ? item.activeIcon : item.icon}
                                        <span className="text-sm">{item.title}</span>
                                    </span>
                                </Link>
                            </div>
                        )}

                        {/* Dropdown children */}
                        {item.hasDropdown && isOpen && (
                            <div className="bg-white">
                                {item.children?.map((child) => (
                                    <Link
                                        key={child.title}
                                        href={child.path}
                                        className={`block border-b border-[#e4e4e4] px-8 py-3 text-sm transition-colors hover:bg-[#8B3A3A] hover:text-white ${
                                            pathname === child.path ? 'bg-[#8B3A3A] text-white' : 'text-[#636363]'
                                        }`}
                                    >
                                        {child.title}
                                    </Link>
                                ))}
                            </div>
                        )}
                    </div>
                );
            })}
        </div>
    );
}
