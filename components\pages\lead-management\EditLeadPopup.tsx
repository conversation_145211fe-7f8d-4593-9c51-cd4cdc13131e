import React from 'react';

interface EditLeadPopupProps {
    isOpen: boolean;
    onClose: () => void;
    onUpdate: (data: {
        name: string;
        email: string;
        phone: string;
        licenseNumber: string;
        company: string;
        type: 'Agency' | 'Agent';
        status: 'New' | 'Contacted' | 'Qualified' | 'Lost' | 'Converted';
        source: string;
        notes: string;
    }) => void;
    initialData: {
        name: string;
        email: string;
        phone: string;
        licenseNumber: string;
        company: string;
        type: 'agency' | 'agent';
        status: 'New' | 'Contacted' | 'Qualified' | 'Lost' | 'Converted';
        source: string;
        notes?: string;
    };
}

const EditLeadPopup: React.FC<EditLeadPopupProps> = ({
    isOpen,
    onClose,
    onUpdate,
    initialData
}) => {
    if (!isOpen) return null;

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const formData = new FormData(e.target as HTMLFormElement);
        onUpdate({
            name: formData.get('name') as string,
            email: formData.get('email') as string,
            phone: formData.get('phone') as string,
            licenseNumber: formData.get('licenseNumber') as string,
            company: formData.get('company') as string,
            type: formData.get('type') as 'Agency' | 'Agent',
            status: formData.get('status') as 'New' | 'Contacted' | 'Qualified' | 'Lost' | 'Converted',
            source: formData.get('source') as string,
            notes: formData.get('notes') as string,
        });
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 font-inter">
            <div className="bg-white rounded-lg w-full max-w-md relative flex flex-col">
                {/* Close button */}
                <button 
                    onClick={onClose}
                    className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                >
                    ×
                </button>

                {/* Header - Fixed */}
                <div className="p-6 border-b">
                    <h2 className="text-lg font-medium">Edit Lead</h2>
                </div>

                {/* Form - Scrollable */}
                <form onSubmit={handleSubmit} className="flex flex-col flex-1 overflow-y-auto max-h-[90vh]">
                    <div className="p-6 space-y-4 overflow-y-auto">
                        {/* Name */}
                        <div>
                            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                                Name
                            </label>
                            <input
                                type="text"
                                id="name"
                                name="name"
                                defaultValue={initialData.name}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#1D7EB6] focus:border-[#1D7EB6]"
                                required
                            />
                        </div>

                        {/* Email */}
                        <div>
                            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                                Email
                            </label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                defaultValue={initialData.email}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#1D7EB6] focus:border-[#1D7EB6]"
                                required
                            />
                        </div>

                        {/* Phone */}
                        <div>
                            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                                Phone
                            </label>
                            <input
                                type="tel"
                                id="phone"
                                name="phone"
                                defaultValue={initialData.phone}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#1D7EB6] focus:border-[#1D7EB6]"
                                required
                            />
                        </div>

                        {/* License Number */}
                        <div>
                            <label htmlFor="licenseNumber" className="block text-sm font-medium text-gray-700 mb-1">
                                License Number
                            </label>
                            <input
                                type="text"
                                id="licenseNumber"
                                name="licenseNumber"
                                defaultValue={initialData.licenseNumber}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#1D7EB6] focus:border-[#1D7EB6]"
                            />
                        </div>

                        {/* Company */}
                        <div>
                            <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
                                Company
                            </label>
                            <input
                                type="text"
                                id="company"
                                name="company"
                                defaultValue={initialData.company}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#1D7EB6] focus:border-[#1D7EB6]"
                                required
                            />
                        </div>

                        {/* Type */}
                        <div>
                            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                                Type
                            </label>
                            <select
                                id="type"
                                name="type"
                                defaultValue={initialData.type}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#1D7EB6] focus:border-[#1D7EB6]"
                                required
                            >
                                <option value="Agency">Agency</option>
                                <option value="Agent">Agent</option>
                            </select>
                        </div>

                        {/* Status */}
                        <div>
                            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                                Status
                            </label>
                            <select
                                id="status"
                                name="status"
                                defaultValue={initialData.status}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#1D7EB6] focus:border-[#1D7EB6]"
                                required
                            >
                                <option value="New">New</option>
                                <option value="Contacted">Contacted</option>
                                <option value="Qualified">Qualified</option>
                                <option value="Lost">Lost</option>
                                <option value="Converted">Converted</option>
                            </select>
                        </div>

                        {/* Source */}
                        <div>
                            <label htmlFor="source" className="block text-sm font-medium text-gray-700 mb-1">
                                Source
                            </label>
                            <input
                                type="text"
                                id="source"
                                name="source"
                                defaultValue={initialData.source}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#1D7EB6] focus:border-[#1D7EB6]"
                                required
                            />
                        </div>

                        {/* Notes */}
                        <div>
                            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                                Notes
                            </label>
                            <textarea
                                id="notes"
                                name="notes"
                                defaultValue={initialData.notes}
                                rows={4}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#1D7EB6] focus:border-[#1D7EB6]"
                            />
                        </div>
                    </div>

                    {/* Action Buttons - Fixed */}
                    <div className="p-6 border-t bg-white">
                        <div className="flex justify-end gap-3">
                            <button
                                type="button"
                                onClick={onClose}
                                className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 border border-gray-300 rounded-md"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                className="px-4 py-2 text-sm font-medium text-white bg-[#1D7EB6] hover:bg-[#1D7EB6]/90 rounded-md"
                            >
                                Update Lead
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default EditLeadPopup; 