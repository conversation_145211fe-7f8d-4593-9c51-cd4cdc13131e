'use client';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import React from 'react';
import { useRouter } from 'next/navigation';
import BreadCrumButton from '@/components/reusable/BreadCrumButton';
import DocumentTable from '../document-management/DocumentTable';
import BlogTable from './BlogTable';
import { CreateIcon } from '@/components/icon/Icon';


const BlogTableLayout = () => {
    const { push } = useRouter();

    return (
        <DefaultPageLayout>
            <BreadCrums
                mainHeading="Blog Management"
                breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Blogs Management' }]}
                ButonComponent={
                    <BreadCrumButton
                        onClick={() => {
                            push('/blog-management');
                        }}
                    />
                }
            />
            
            <div className="px-4">
                <BlogTable/>

            </div>
        </DefaultPageLayout>
    );
};

export default BlogTableLayout;
