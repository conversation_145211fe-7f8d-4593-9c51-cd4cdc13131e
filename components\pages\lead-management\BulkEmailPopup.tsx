'use client';
import { useEffect, useState } from "react";
import { BulkEmailModalProps } from "../user-management/UserManagementTable";
import { showMessage } from "@/app/lib/Alert";
import { USERS_BULK_EMAIL_API, USERS_INDIVIDUAL_EMAIL_API } from "@/app/lib/apiRoutes";
import Modal from '@/components/reusable/modals/modal';
import { Mail } from "lucide-react";
export const BulkEmailModal = ({ isOpen, onClose, recipients = [], isIndividual = false }: BulkEmailModalProps): JSX.Element => {
    const [to, setTo] = useState('');
    const [bcc, setBcc] = useState('');
    const [subject, setSubject] = useState('');
    const [message, setMessage] = useState('');
    const [attachments, setAttachments] = useState<File[]>([]);
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        if (isOpen && recipients.length > 0) {
            if (isIndividual) {
                // Populate 'To' field for individual email
                setTo(recipients.map(r => r.email).join(', '));
                setBcc(''); // Ensure BCC is empty for individual
            } else {
                // Populate 'BCC' field for bulk email
                setBcc(recipients.map(r => r.email).join(', '));
                setTo(''); // Ensure To is empty for bulk
            }
        } else if (!isOpen) {
            // Clear form on close
            setTo('');
            setBcc('');
            setSubject('');
            setMessage('');
            setAttachments([]);
            setIsSubmitting(false);
        }
    }, [isOpen, recipients, isIndividual]);

    const handleSendEmail = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        let endpoint = '';
        let payload: any = {
            subject,
            message,
            // Attachments will be handled via FormData directly
        };

        if (isIndividual) {
            if (!to.trim()) {
                showMessage("Please enter an email address in the To field", "error");
                setIsSubmitting(false);
                return;
            }
            endpoint = USERS_INDIVIDUAL_EMAIL_API;
            payload.to = to.split(',').map(email => email.trim());
        } else {
            if (!bcc.trim()) {
                showMessage("Please enter email addresses in the BCC field", "error");
                setIsSubmitting(false);
                return;
            }
            endpoint = USERS_BULK_EMAIL_API;
            payload.bcc = bcc.split(',').map(email => email.trim());
        }

        const formData = new FormData();
        formData.append('subject', subject);
        formData.append('message', message);

        if (isIndividual) {
            formData.append('to', JSON.stringify(payload.to));
        } else {
            formData.append('bcc', JSON.stringify(payload.bcc));
        }

        attachments.forEach((file) => {
            formData.append('attachments', file);
        });

        console.log(`Sending ${isIndividual ? 'individual' : 'bulk'} email:`, payload);

        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                body: formData,
                credentials: 'include', // Important for sending cookies
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || `Failed to send ${isIndividual ? 'individual' : 'bulk'} email.`);
            }

            const result = await response.json();
            showMessage(result.message || `Email sent successfully!`, "success");
            onClose();
        } catch (error: any) {
            console.error(`Error sending ${isIndividual ? 'individual' : 'bulk'} email:`, error);
            showMessage(error.message || `Failed to send ${isIndividual ? 'individual' : 'bulk'} email.`, "error");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files) {
            const newFiles = Array.from(files);
            setAttachments(prev => [...prev, ...newFiles]);
        }
    };

    const removeAttachment = (index: number) => {
        setAttachments(prev => prev.filter((_, i) => i !== index));
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];

    };

    return (
        <Modal isOpen={isOpen} onClose={onClose} classes="!max-w-2xl">
            <div className="p-6">
                <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                    <Mail className="w-5 h-5 text-gray-600" />
                    {isIndividual ? 'Contact User' : 'Send Bulk Email'}
                </h2>
                <form onSubmit={handleSendEmail} className="space-y-4">
                    {isIndividual ? (
                        <div>
                            <label htmlFor="individual-to" className="block text-sm font-medium text-gray-700">To</label>
                            <input
                                id="individual-to"
                                value={to}
                                onChange={(e) => setTo(e.target.value)}
                                placeholder="Enter email address"
                                className="w-full mt-1 border rounded px-3 py-2 bg-gray-100 cursor-not-allowed"
                                readOnly
                                required
                            />
                        </div>
                    ) : (
                        <>
                            <div>
                                <label htmlFor="bulk-to" className="block text-sm font-medium text-gray-700">To</label>
                                <textarea
                                    id="bulk-to"
                                    value={to}
                                    onChange={(e) => setTo(e.target.value)}
                                    placeholder="Enter email addresses separated by commas (e.g., <EMAIL>, <EMAIL>)"
                                    rows={3}
                                    className="w-full mt-1 border rounded px-3 py-2"
                                    required={!bcc.trim()}
                                />
                                <p className="text-xs text-gray-500 mt-1">
                                    Separate multiple email addresses with commas
                                </p>
                            </div>

                            <div>
                                <label htmlFor="bulk-bcc" className="block text-sm font-medium text-gray-700">BCC</label>
                                <textarea
                                    id="bulk-bcc"
                                    value={bcc}
                                    onChange={(e) => setBcc(e.target.value)}
                                    placeholder="BCC recipients (automatically populated with selected recipients)"
                                    rows={3}
                                    className="w-full mt-1 border rounded px-3 py-2 bg-gray-100"
                                    // readOnly
                                    required={!to.trim()}
                                />
                                <p className="text-xs text-gray-500 mt-1">
                                    Recipients will be sent as BCC to protect privacy
                                </p>
                            </div>
                        </>
                    )}

                    {/* Subject field */}
                    <div>
                        <label htmlFor="bulk-subject" className="block text-sm font-medium text-gray-700">Subject</label>
                        <input
                            id="bulk-subject"
                            value={subject}
                            onChange={(e) => setSubject(e.target.value)}
                            placeholder="Enter email subject"
                            className="w-full mt-1 border rounded px-3 py-2"
                            required
                        />
                    </div>

                    {/* Message field */}
                    <div>
                        <label htmlFor="bulk-message" className="block text-sm font-medium text-gray-700">Message</label>
                        <textarea
                            id="bulk-message"
                            value={message}
                            onChange={(e) => setMessage(e.target.value)}
                            placeholder="Enter your message"
                            rows={8}
                            className="w-full mt-1 border rounded px-3 py-2"
                            required
                        />
                    </div>

                    {/* Attachments */}
                    {/* <div>
                        <label htmlFor="attachments" className="block text-sm font-medium text-gray-700">Attachments</label>
                        <div className="space-y-2">
                            <div className="flex items-center gap-2">
                                <input
                                    id="attachments"
                                    type="file"
                                    multiple
                                    onChange={handleFileUpload}
                                    className="hidden"
                                />
                                <button
                                    type="button"
                                    className="px-4 py-2 rounded border border-gray-300 text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                                    onClick={() => document.getElementById('attachments')?.click()}
                                >
                                    <Upload className="w-4 h-4" />
                                    Upload Files
                                </button>
                                <span className="text-xs text-gray-500">
                                    {attachments.length > 0 ? `${attachments.length} file(s) selected` : 'No files selected'}
                                </span>
                            </div>

                            {attachments.length > 0 && (
                                <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-200 rounded p-2 bg-white">
                                    {attachments.map((file, index) => (
                                        <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded-md">
                                            <div className="flex items-center gap-2 flex-1 min-w-0">
                                                <FileText className="w-4 h-4 text-gray-500 flex-shrink-0" />
                                                <span className="text-sm truncate">{file.name}</span>
                                                <span className="text-xs text-gray-400 flex-shrink-0">
                                                    ({formatFileSize(file.size)})
                                                </span>
                                            </div>
                                            <button
                                                type="button"
                                                className="p-1 rounded-full text-red-500 hover:bg-gray-100 hover:text-red-700 flex-shrink-0"
                                                onClick={() => removeAttachment(index)}
                                            >
                                                <X className="w-4 h-4" />
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                            You can upload multiple files. Common formats: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, Images
                        </p>
                    </div> */}

                    <div className="flex justify-end gap-3 pt-4">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 rounded border border-gray-300 text-gray-700 hover:bg-gray-100"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            className="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50"
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? 'Sending...' : 'Send Email'}
                        </button>
                    </div>
                </form>
            </div>
        </Modal>
    );
}