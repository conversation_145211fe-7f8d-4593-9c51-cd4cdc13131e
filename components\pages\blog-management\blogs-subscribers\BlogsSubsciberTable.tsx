'use client';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import React, { useEffect, useRef, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import BreadCrumButton from '@/components/reusable/BreadCrumButton';

import Loading from '@/components/layouts/loading';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import { getPaginationRange, statusOptions, useDebouncedValue } from '@/store/utils.ts/functions';
import { PaginationDownIcon, PaginationLeftIcon, PaginationRightIcon } from '@/components/icon/Icon';
import { showMessage } from '@/app/lib/Alert';
import Link from 'next/link';

import { Download, Mail } from 'lucide-react';
import { ActionIcon } from '../../lead-management/Icons';
import { Filters } from '../../lead-management/Filters';
import { BulkEmailModal } from '../../lead-management/BulkEmailPopup';
import SearchInput from '@/components/reusable/SearchBar';
import SearchDropDown from '@/components/reusable/SearchDropDown';
import { SendEmailToSubscriber } from './SendEmailToSubscriber';

const capitalizeFirst = (text: string) => (text ? text.charAt(0).toUpperCase() + text.slice(1) : '');

interface Lead {
    id: number;
    fullName: string;
    email: string;
    phone: string;
    licenseNumber: string;
    company: string;
    leadType: 'agent' | 'agency';
    created_at: string;

    statusName: 'New' | 'Contacted' | 'Qualified' | 'Lost' | 'Converted' | 'Re-opened';
    isConverted?: boolean;
    last_contact?: string;
    status: string;
    source: string;
}

interface LeadCountCard {
    statusId: number;
    statusName: string;
    leadsCount: string; // it's a string in the response
}

const statusDropdown = [
    { label: 'All Statuses' },
    { label: 'Active' },
    { label: 'Unsubscribed' },
    // { label: 'Bounced' }
];

const categoryDropdown = [{ label: 'All Sources' }, { label: 'Website' }, { label: 'Newsletter' }, { label: 'Social' }, { label: 'Direct' }];
const columns = [
    { key: 'checkbox', label: '', width: 'w-[2%]' },
    { key: 'company', label: 'Email', width: 'w-[15%]' },
    { key: 'status', label: 'Status', width: 'w-[6%]' },
    { key: 'source', label: 'Source', width: 'w-[6%]' },
    { key: 'dateAdded', label: 'Subscribed', width: 'w-[10%]' },
    { key: 'action', label: 'Actions', width: 'w-[10%]' },
];

const LeadManagementLayout = () => {
    const { push } = useRouter();
    const pathname = usePathname();
    const tableContainerRef = useRef<HTMLDivElement>(null);
    const actionDropdownRef = useRef<HTMLDivElement>(null);

    // NEW — replaces:  page, itemsPerPage
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 10,
        total: 0,
    });

    const [isAddLeadPopupOpen, setIsAddLeadPopupOpen] = useState(false);

    const [isBulkEmailPopupOpen, setIsBulkEmailPopupOpen] = useState(false);
    const [leadsData, setLeadsData] = useState<Lead[]>([]);
    const [loader, setLoader] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [showDropdown, setShowDropdown] = useState(false);
    const [dropUp, setDropUp] = useState(false);
    const dropdownButtonRef = useRef<HTMLButtonElement>(null);
    const [documentCounts, setDocumentCounts] = useState<LeadCountCard[]>([]);
    const [selectedCategory, setSelectedCategory] = useState('');
    const [seletedItem, setSeletedItem] = useState<any>({
        subscribedcount: 0,
        unsubscribedcount: 0,
        othercount: 0,
        totalcount: 0,
    });

    const debouncedSearchTerm = useDebouncedValue(searchTerm, 300);
    const [selectedStatus, setSelectedStatus] = useState('');
    const [selectedType, setSelectedType] = useState('');

    const [selectedLeadIds, setSelectedLeadIds] = useState<string[]>([]);

    const [dropdownOpen, setDropdownOpen] = useState<number | null>(null);

    const [emailRecipients, setEmailRecipients] = useState<Array<{ name: string; email: string }>>([]);
    const [isIndividualEmail, setIsIndividualEmail] = useState(false);
    const [allLeadsMap, setAllLeadsMap] = useState<{ [id: string]: any }>({});
    const [leads, setLeads] = useState<Lead[]>([]);

    const getSelectedRecipientsForEmail = () => {
        return selectedLeadIds
            .map((id) => allLeadsMap[id])
            .filter(Boolean)
            .map((lead) => ({ name: lead?.fullName, email: lead?.email }));
    };

    // Modal open handlers
    const openBulkEmail = () => {
        const selected = getSelectedRecipientsForEmail();
        // console.log('selected   >>>', selected);
        if (selected.length === 0) {
            showMessage('Please select at least one user to send newsletter.', 'error');
            return;
        }
        setEmailRecipients(selected);
        setIsIndividualEmail(false);
        setIsBulkEmailPopupOpen(true);
    };

    const closeEmailModal = () => {
        setSelectedLeadIds([]);
        setIsBulkEmailPopupOpen(false);
    };

    const toggleDropdown = () => {
        if (dropdownButtonRef.current) {
            const rect = dropdownButtonRef.current.getBoundingClientRect();
            const spaceBelow = window.innerHeight - rect.bottom;
            const estimatedDropdownHeight = 150;
            setDropUp(spaceBelow < estimatedDropdownHeight);
        }
        setShowDropdown((prev) => !prev);
    };

    const getStatusId = (status: string) => {
        if (status === 'Activated') return '1';
        if (status === 'Unsubscribed') return ' 28';
        if (status === 'Subscribed') return '27';
        return '';
    };
    const getSelectedCategory = (status: string) => {
        if (status === 'All Sources') return '';
        return status.toLowerCase() || '';
    };

    const getLeadseData = async () => {
        try {
            const queryParams = new URLSearchParams({
                status: getStatusId(selectedStatus) || '',
                source: getSelectedCategory(selectedCategory) || '',
                search: searchTerm || '',
                page: pagination.page?.toString() || '1',
                pageSize: pagination.pageSize?.toString() || '10',
            });

            const url = `${API_ENDPOINTS.GET_ALL_NEWSLETTER}?${queryParams.toString()}`;

            const res = await fetch(url, {
                method: 'GET',
                credentials: 'include',
            });

            const result = await res.json();

            if (!result.success) {
                console.error(result.message);
                return;
            }

            const { leads = [], leadsCounts = [], pagination: p = null } = result.data;

            setLeadsData(leads);
            setLeads(leads);
            setDocumentCounts(leadsCounts);

            // Build a map of all leads by id
            setAllLeadsMap((prev) => {
                const updated = { ...prev };
                leads.forEach((l: any) => (updated[l.id] = l));
                return updated;
            });

            if (p) {
                const totalPages = Math.max(1, Math.ceil(p.total / p.pageSize));
                const safePage = Math.min(p.page, totalPages);
                setPagination({ ...p, page: safePage });
            } else {
                setPagination((prev) => ({
                    ...prev,
                    returned: leads.length,
                    total: leads.length,
                }));
            }

            await getFetchCount();
        } catch (err) {
            console.error('getLeadseData failed:', err);
        } finally {
            setLoader(false);
        }
    };

    useEffect(() => {
        getLeadseData();
    }, [selectedStatus, selectedType, pagination.page, pagination.pageSize, debouncedSearchTerm, selectedStatus, selectedCategory]);

    const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { checked } = e.target;
        const currentPageUserIds = leads?.map((lead) => lead.id.toString());
        if (checked) {
            // Union: add all current page user IDs to selectedUserIds
            setSelectedLeadIds((prev) => Array.from(new Set([...prev, ...currentPageUserIds])));
        } else {
            // Remove all current page user IDs from selectedUserIds
            setSelectedLeadIds((prev) => prev.filter((id) => !currentPageUserIds.includes(id)));
        }
    };

    const handleSelectLead = (userId: string, e: React.ChangeEvent<HTMLInputElement>) => {
        const { checked } = e.target;
        if (checked) {
            setSelectedLeadIds((prev) => [...prev, userId]);
        } else {
            setSelectedLeadIds((prev) => prev.filter((id) => id !== userId));
        }
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
        setPagination((prev) => ({
            ...prev,
            page: 1,
        }));
    };

    const totalPages = Math.ceil(pagination.total / pagination.pageSize);

    const currentPage = pagination.page; // 1-based

    /* helper that bumps page, guarding the edges */
    const setPageSafe = (n: number) =>
        setPagination((prev) => ({
            ...prev,
            page: Math.min(Math.max(n, 1), totalPages || 1),
        }));

    const handleUnsubscribe = async (lead: any) => {
        try {
            const res = await fetch(API_ENDPOINTS.UPDATE_NEWSLETTER, {
                method: 'PUT',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                },

                body: JSON.stringify({ statusId: lead.statusId === 1 ? 29 : 1, id: lead.id }),
            });
            const result = await res.json();
            if (result.success) {
                showMessage(result.message, 'success');
            } else {
                console.error(result.message);
                showMessage(result.message, 'error');
                return;
            }
            setSelectedLeadIds([]);
            getLeadseData();
        } catch (err) {
            console.error('Error unsubscribing leads:', err);
        }
    };

    const handleDelete = async (id: any) => {
        try {
            const res = await fetch(API_ENDPOINTS.DLETE_NEWSLETTER + id, {
                method: 'DELETE',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                },
            });
            const result = await res.json();
            if (result.success) {
                showMessage(result.message, 'success');
            } else {
                console.error(result.message);
                showMessage(result.message, 'error');
                return;
            }
            setSelectedLeadIds([]);
            getLeadseData();
        } catch (err) {
            console.error('Error unsubscribing leads:', err);
        }
    };

    const handleDownloadCSV = () => {
        // Create CSV headers
        const headers = ['Email', 'Status', 'Source', 'Subscribed'].join(',');

        // Convert lead data to CSV rows
        const csvRows = leads?.map((lead) => {
            return [lead.email, lead.status, lead.source, lead.created_at].join(',');
        });

        // Combine headers and rows
        const csvContent = [headers, ...csvRows].join('\n');

        // Create blob and download
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `News_Letter_${new Date().toISOString().split('T')[0]}.csv`);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
    };

    const getFetchCount = async () => {
        try {
            const url = `${API_ENDPOINTS.GET_ALL_NEWSLETTER_COUNT}`;

            const res = await fetch(url, {
                method: 'GET',
                credentials: 'include',
            });

            const result = await res.json();

            if (result.success) {
                setSeletedItem({
                    subscribedcount: result.data.subscribedcount,
                    unsubscribedcount: result.data.unsubscribedcount,
                    othercount: result.data.othercount,
                    totalcount: result.data.totalcount,
                });
            }
        } catch (err) {
            console.error('getLeadseData failed:', err);
        } finally {
            setLoader(false);
        }
    };

    return (
        <>
            <div className="grid grid-cols-2 items-center justify-between  gap-3 px-3 mt-3 rounded-md bg-[#f1f5f9] py-2">
                <div 
                onClick={() => push('/blog-management')}
                className={`flex  w-full cursor-pointer items-center justify-center rounded-md font-inter text-base font-medium    p-2 text-center
                    
                    ${pathname === '/blog-management' ? 'bg-white  ' : ''}
                    `}>Blog Posts</div>


                                    <div
                                    onClick={() => push('/blog-management/subscribers')}
                                    className={`flex cursor-pointer w-full items-center font-inter text-base font-medium justify-center rounded-md    p-2 text-center
                    
                    ${pathname === '/blog-management/subscribers' ? 'bg-white  ' : ''}
                    `}>Subscribers</div>
 
            </div>

            <div className="mb-5 flex flex-col justify-end gap-4 px-4 pt-5 lg:flex-row lg:items-center lg:px-5">
                <div className="flex w-full flex-col gap-3 font-inter sm:w-auto sm:flex-row">
                    <div className="flex w-full flex-col gap-3 sm:w-auto sm:flex-row">
                        <button
                            onClick={handleDownloadCSV}
                            className="flex items-center justify-center rounded-md border border-[#1D7EB6] px-4 py-2.5 text-sm text-[#1D7EB6] lg:px-6 lg:py-3 lg:text-base"
                        >
                            <Download className="mr-2 h-4 w-4" />
                            Export
                        </button>
                    </div>
                    <div className="flex w-full flex-col gap-3 sm:w-auto sm:flex-row">
                        <button
                            onClick={() => openBulkEmail()}
                            className="flex items-center justify-center rounded-md bg-[#1D7EB6] px-4 py-2.5 text-sm text-white hover:bg-[#1D7EB6]/90 lg:px-6 lg:py-3 lg:text-base"
                        >
                            <Mail className="mr-2 h-4 w-4" />
                            Send Newsletter
                        </button>
                    </div>
                </div>
            </div>

            {/* Stats Cards */}
            <div className="mb-8 rounded-lg bg-white pt-6">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">



                       <div  className="flex items-center justify-between rounded-[20px] border-2 border-[#f0f0f0] bg-[#f8f9fa] p-5 shadow-[0px_4px_20px_0px_rgba(21,32,70,0.05)] transition hover:border-blue-200">
                            <div className="flex flex-col items-start justify-start gap-2">
                                <div className="font-golosText text-5xl font-bold text-black"   >
                                {seletedItem?.totalcount}
                                </div>
                                <div className="font-inter text-base font-medium text-[#555]">Total Subscribers</div>
                            </div>
                        </div>

     <div  className="flex items-center justify-between rounded-[20px] border-2 border-[#f0f0f0] bg-[#f8f9fa] p-5 shadow-[0px_4px_20px_0px_rgba(21,32,70,0.05)] transition hover:border-blue-200">
                            <div className="flex flex-col items-start justify-start gap-2">
                                <div className="font-golosText text-5xl font-bold text-green-600"   >
                                {seletedItem?.subscribedcount}
                                </div>
                                <div className="font-inter text-base font-medium text-[#555]">Active</div>
                            </div>
                        </div>
 

                          <div  className="flex items-center justify-between rounded-[20px] border-2 border-[#f0f0f0] bg-[#f8f9fa] p-5 shadow-[0px_4px_20px_0px_rgba(21,32,70,0.05)] transition hover:border-blue-200">
                            <div className="flex flex-col items-start justify-start gap-2">
                                <div className="font-golosText text-5xl font-bold text-red-600"   >
                                {seletedItem?.unsubscribedcount}
                                </div>
                                <div className="font-inter text-base font-medium text-[#555]">Unsubscribed</div>
                            </div>
                        </div> 
                          <div  className="flex items-center justify-between rounded-[20px] border-2 border-[#f0f0f0] bg-[#f8f9fa] p-5 shadow-[0px_4px_20px_0px_rgba(21,32,70,0.05)] transition hover:border-blue-200">
                            <div className="flex flex-col items-start justify-start gap-2">
                                <div className="font-golosText text-5xl font-bold text-blue-600"   >
                                {seletedItem?.othercount}
                                </div>
                                <div className="font-inter text-base font-medium text-[#555]">Bounced</div>
                            </div>
                        </div>


  
                </div>
            </div>
            {/* Filters Section */}
            <div className="mb-8">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                    <div className="w-full">
                        <SearchInput onChange={handleSearchChange} value={searchTerm} placeholder="Search Subscribers..." />
                    </div>
                    <div className="w-full">
                        <SearchDropDown classes="!h-14 w-full" dropdownOptions={statusDropdown} initail={selectedStatus} setSelectedStatus={setSelectedStatus} />
                    </div>
                    <div className="w-full">
                        <SearchDropDown classes="!h-14 w-full" dropdownOptions={categoryDropdown} initail={selectedCategory} setSelectedStatus={setSelectedCategory} />
                    </div>
                </div>
            </div>

            {/* Grid Container - Made Responsive */}
            <div className="px-4 lg:px-5 ">
                <div className="bg-white">
                    <div ref={tableContainerRef} className="relative  overflow-hidden rounded-lg rounded-tl-lg rounded-tr-lg border shadow-[0px_4px_20px_0px_rgba(21,32,70,0.07)]">
                        {/* Table wrapper with horizontal scroll */}
                        <div className="overflow-x-auto ">
                            <table className="w-full min-w-[1200px] border-collapse font-inter ">
                                <thead className="sticky top-0 z-[5] bg-[#e4e4e4]">
                                    <tr className="w-full rounded-lg rounded-tl-lg rounded-tr-lg border-none">
                                        {columns.map((column) => (
                                            <th
                                                key={column.key}
                                                className={`h-[72px] cursor-pointer border-none px-4 py-4 text-left font-inter text-base font-medium leading-normal text-[#2d2d2e] ${column.width}`}
                                            >
                                                {column.key === 'checkbox' ? (
                                                    <input
                                                        type="checkbox"
                                                        checked={leads?.length > 0 && leads.every((lead) => selectedLeadIds.includes(lead.id.toString()))}
                                                        onChange={handleSelectAll}
                                                        className="h-4 w-4 rounded border-gray-300"
                                                    />
                                                ) : (
                                                    <span className="flex items-center">{column.label}</span>
                                                )}
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                            </table>
                        </div>

                        {/* Table body with vertical scroll */}
                        <div className="overflow-x-auto overflow-y-auto">
                            <table className={`w-full min-w-[1200px] border-collapse font-inter ${loader ? 'pointer-events-none opacity-50' : ''}`}>
                                <tbody>
                                    {leads?.length === 0 && (
                                        <tr>
                                            <td colSpan={10}>
                                                <div className="flex items-center justify-center py-10 text-center text-base font-medium text-[#888]">No Records to Show</div>
                                            </td>
                                        </tr>
                                    )}

                                    {leads?.map((lead, index) => {
                                        /* helpers ----------------------------------------------------------- */
                                        const globalIndex = (pagination.page - 1) * pagination.pageSize + index;

                                        const safeText: (v: any) => string = (v) => (v && String(v).trim() ? v : '—');

                                        const initials = safeText(lead.fullName)
                                            .split(' ')
                                            .filter(Boolean)
                                            .map((n: string) => n[0])
                                            .join('')
                                            .toUpperCase();

                                        return (
                                            <tr key={globalIndex} className="text-grayText border-b border-[#E4E4E4] text-center hover:bg-gray-50">
                                                <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[0].width}`}>
                                                    <input
                                                        type="checkbox"
                                                        checked={selectedLeadIds.includes(lead.id.toString())}
                                                        onChange={(e) => handleSelectLead(lead.id.toString(), e)}
                                                        className="h-4 w-4 rounded border-gray-300"
                                                    />
                                                </td>

                                                <td className={`h-[72px]  border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[1].width}`}>
                                                    <div className="text-sm text-gray-900">
                                                        <div className="mb-1 flex items-start justify-start">
                                                            {lead.email ? (
                                                                <Link href={`mailto:${lead.email}`} className="ml-2 text-xs text-blue-600 hover:underline">
                                                                    {lead.email}
                                                                </Link>
                                                            ) : (
                                                                <span className="ml-2 text-xs text-gray-500">—</span>
                                                            )}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className={`h-[72px]  border-b border-t border-[#e4e4e4]   py-4 text-sm font-normal text-[#636363] ${columns[2].width}`}>
                                                    <div className="mb-1 flex items-start  justify-start text-left">{lead?.status}</div>
                                                </td>

                                                <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal ${columns[3]?.width}`}>
                                                    <div className="">{capitalizeFirst(lead?.source)}</div>
                                                </td>
                                                <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[5].width}`}>
                                                    {lead.created_at ? new Date(lead.created_at).toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }) : '—'}
                                                </td>
                                                <td className={`relative h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[4]?.width}`}>
                                                    <button
                                                        className="text-blue-500"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            setDropdownOpen(dropdownOpen === globalIndex ? null : globalIndex);
                                                        }}
                                                    >
                                                        <ActionIcon />
                                                    </button>

                                                    {dropdownOpen === globalIndex && (
                                                        <div ref={actionDropdownRef} className="absolute right-0 top-full !z-[10] mt-2 w-48 rounded-md border border-gray-200 bg-white shadow-lg">
                                                            <div className="py-1">
                                                                <span
                                                                    onClick={() => {
                                                                        setDropdownOpen(null);
                                                                        setIsBulkEmailPopupOpen(true);
                                                                        setEmailRecipients([{ email: lead?.email, name: '' }]);
                                                                    }}
                                                                    className="flex w-full cursor-pointer items-center px-4 py-2 text-left text-sm hover:bg-gray-100"
                                                                >
                                                                    <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                                        <path
                                                                            strokeLinecap="round"
                                                                            strokeLinejoin="round"
                                                                            strokeWidth={2}
                                                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                                                        />
                                                                    </svg>
                                                                    Send Email
                                                                </span>
                                                                <span
                                                                    onClick={() => {
                                                                        handleUnsubscribe(lead);
                                                                        setDropdownOpen(null);
                                                                    }}
                                                                    className="flex w-full cursor-pointer items-center px-4 py-2 text-left text-sm hover:bg-gray-100"
                                                                >
                                                                    <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path
                                                                            strokeLinecap="round"
                                                                            strokeLinejoin="round"
                                                                            strokeWidth={2}
                                                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                                                        />
                                                                    </svg>
                                                                    {lead.status === 'Activated' ? 'Unsubscribe' : 'Subscribe'}
                                                                </span>

                                                                <span
                                                                    onClick={() => {
                                                                        handleDelete(lead.id);
                                                                        setDropdownOpen(null);
                                                                    }}
                                                                    className={`flex w-full items-center px-4 py-2 text-left text-sm text-[#993333]
                                                                         ${
                                                                             lead.statusName?.toLowerCase() === 'lost'
                                                                                 ? 'pointer-events-none cursor-not-allowed opacity-50'
                                                                                 : 'cursor-pointer hover:bg-gray-100'
                                                                         }`}
                                                                    aria-disabled={lead.statusName?.toLowerCase() === 'lost'}
                                                                >
                                                                    <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" width="24" height="24" viewBox="0 0 24 24">
                                                                        <path fill="currentColor" d="M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6z" />
                                                                    </svg>
                                                                    Delete
                                                                </span>
                                                            </div>
                                                        </div>
                                                    )}
                                                </td>
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>

                            {loader && (
                                <div className="absolute inset-0 z-10 flex items-center justify-center bg-white bg-opacity-60">
                                    <Loading />
                                </div>
                            )}
                        </div>
                    </div>

                    {/* PAGINATION */}
                    {leads?.length !== 0 && !loader && (
                        <div className="flex flex-col items-center gap-4 border-t border-[#E4E4E4] p-4 md:flex-row md:justify-between">
                            {/* ← → & page numbers */}
                            <div className="flex items-center gap-2">
                                {/* previous */}
                                <button onClick={() => setPageSafe(currentPage - 1)} disabled={currentPage === 1} className="rounded-md p-2 disabled:opacity-50">
                                    <PaginationRightIcon />
                                </button>

                                {/* numeric buttons */}
                                <div className="flex space-x-1">
                                    {getPaginationRange(currentPage - 1, totalPages).map((i) => (
                                        <button
                                            key={i}
                                            onClick={() => setPageSafe(i + 1)}
                                            className={`rounded-md px-3 py-1 ${currentPage === i + 1 ? 'bg-[#1D7EB6] text-white' : 'hover:bg-gray-100'}`}
                                        >
                                            {i + 1}
                                        </button>
                                    ))}
                                </div>

                                {/* next */}
                                <button onClick={() => setPageSafe(currentPage + 1)} disabled={currentPage === totalPages} className="rounded-md p-2 disabled:opacity-50">
                                    <PaginationLeftIcon />
                                </button>
                            </div>

                            {/* page-size selector */}
                            <div className="relative flex items-center gap-2">
                                <span className="text-sm text-gray-500">Showing</span>

                                <div className="relative">
                                    <button ref={dropdownButtonRef} onClick={toggleDropdown} className="flex items-center gap-1 rounded-md border border-gray-200 bg-[#EDF5F9] px-2 py-1 text-sm">
                                        {pagination.pageSize}
                                        <PaginationDownIcon />
                                    </button>

                                    {showDropdown && (
                                        <div className={`absolute left-0 z-10 w-16 rounded-md border border-gray-200 bg-white shadow-lg ${dropUp ? 'bottom-full mb-2' : 'top-full mt-2'}`}>
                                            <div className="py-1">
                                                {[10, 20, 50].map((value) => (
                                                    <button
                                                        key={value}
                                                        className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                                        onClick={() => {
                                                            setPagination((prev) => ({ ...prev, page: 1, pageSize: value }));
                                                            setShowDropdown(false);
                                                        }}
                                                    >
                                                        {value}
                                                    </button>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>

                                <span className="text-sm text-gray-500">Leads out of {pagination.total}</span>
                            </div>
                        </div>
                    )}
                </div>
            </div>
            {/* Modals */}
            {isBulkEmailPopupOpen && (
                <SendEmailToSubscriber isOpen={isBulkEmailPopupOpen} onClose={closeEmailModal} emailRecipients={emailRecipients} isIndividual={false} isLead={true} onSubmitSuccess={getLeadseData} />
            )}
        </>
    );
};

export default LeadManagementLayout;
