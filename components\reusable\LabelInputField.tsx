"use client";
import { useState } from "react";

interface InputFieldProps {
  label: string;
  id: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const LabelInputField: React.FC<InputFieldProps> = ({ label, id, value, onChange }) => {
  return (
    <div className="relative w-full">
      <input
        id={id}
        type="text"
        value={value}
        onChange={onChange}
        className="peer w-full border border-gray-300 rounded-lg p-3 pt-3 text-gray-900 focus:outline-none text-sm"
      />
      <label
        htmlFor={id}
        className={`absolute left-3 px-1 bg-white text-grayMain font-normal transition-all duration-300 peer-focus:-top-2 peer-focus:text-xs ${
          value ? "-top-2 text-xs" : "top-3 text-sm"
        }`}
      >
        {label}
      </label>
    </div>
  );
};

export default LabelInputField;