import { TICKETS_API, TICKETS_SUMMARY_API, TICKETS_META_API, TICKET_RESPONSES_API } from '@/app/lib/apiRoutes';

// Types
export interface Ticket {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: string;
  agent_id: string;
  agent_name?: string;
  agent_email?: string;
  created_by?: string;
  admin_id?: string;
  admin_name?: string;
  admin_email?: string;
  created_at: string;
  updated_at: string;
  responses?: TicketResponse[];
}

export interface TicketResponse {
  id: string;
  agent_id?: string;
  admin_id?: string;
  response: string;
  created_at: string;
  agent_name?: string;
  admin_name?: string;
  agent_email?: string;
  admin_email?: string;
}

export interface TicketSummary {
  open: number;
  'in-progress': number;
  resolved: number;
  closed: number;
}

export interface TicketMeta {
  status: string[];
  priority: string[];
  category: string[];
}

export interface CreateTicketRequest {
  agent_id: string;
  agent_name: string;
  agent_email: string;
  title: string;
  priority: string;
  category: string;
  description: string;
}

export interface UpdateTicketRequest {
  status?: string;
  priority?: string;
  agent_id?: string;
  title?: string;
  description?: string;
}

export interface CreateResponseRequest {
  agent_id?: string;
  response: string;
}

// API Functions
export const supportTicketsService = {
  // Get tickets with filters
  async getTickets(params?: {
    status?: string;
    priority?: string;
    category?: string;
    search?: string;
  }): Promise<Ticket[]> {
    const queryParams = new URLSearchParams();
    if (params?.status) queryParams.append('status', params.status);
    if (params?.priority) queryParams.append('priority', params.priority);
    if (params?.category) queryParams.append('category', params.category);
    if (params?.search) queryParams.append('search', params.search);

    const url = `${TICKETS_API}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch tickets: ${response.statusText}`);
    }

    const result = await response.json();
    
    const tickets = result.data || result; // Extract data property or return full response as fallback
    
    return tickets;
  },

  // Get single ticket
  async getTicket(id: string): Promise<Ticket> {
    const response = await fetch(`${TICKETS_API}/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch ticket: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data || result; // Extract data property or return full response as fallback
  },

  // Create ticket
  async createTicket(data: CreateTicketRequest): Promise<Ticket> {
    const response = await fetch(TICKETS_API, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to create ticket: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data || result; // Extract data property or return full response as fallback
  },

  // Update ticket
  async updateTicket(id: string, data: UpdateTicketRequest): Promise<Ticket> {
    const response = await fetch(`${TICKETS_API}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to update ticket: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data || result; // Extract data property or return full response as fallback
  },

  // Delete ticket (soft delete)
  async deleteTicket(id: string): Promise<void> {
    const response = await fetch(`${TICKETS_API}/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`Failed to delete ticket: ${response.statusText}`);
    }
  },

  // Get ticket responses
  async getTicketResponses(id: string): Promise<TicketResponse[]> {
    const url = TICKET_RESPONSES_API.replace(':id', id);
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch ticket responses: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data || result; // Extract data property or return full response as fallback
  },

  // Add response to ticket
  async addTicketResponse(id: string, data: CreateResponseRequest): Promise<TicketResponse> {
    const url = TICKET_RESPONSES_API.replace(':id', id);
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to add response: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data || result; // Extract data property or return full response as fallback
  },

  // Get ticket summary for dashboard
  async getTicketSummary(): Promise<TicketSummary> {
    const response = await fetch(TICKETS_SUMMARY_API, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch ticket summary: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data || result; // Extract data property or return full response as fallback
  },

  // Get ticket meta data (enums)
  async getTicketMeta(): Promise<TicketMeta> {
    const response = await fetch(TICKETS_META_API, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch ticket meta: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data || result; // Extract data property or return full response as fallback
  },
}; 