'use client';
 
import DuplicateIcons from '@/../public/assets/images/Duplicate.svg'; 
import Image from 'next/image'; 
import { CloseIconModel  } from '@/components/icon/Icon';

const DraftConfirmation = ({ onClose, onSuccessfulDelete }: any) => { 
    const handleDelete = ( ) => {
        onClose(true);
      
    };
    return (
        <>
            <div className=" w-full">
                <div className=" relative flex items-center justify-between ">
                    <div className=" relative flex items-start justify-start "></div>
                    <div className=" relative flex items-end justify-end ">
                        <span className="cursor-pointer   pt-5" onClick={handleDelete}>
                            <CloseIconModel />
                        </span>
                    </div>
                </div>
                <div className="h-100 flex flex-col justify-between">
                    <div className=" ">
                        <Image src={DuplicateIcons} alt="Success" width={200} height={200} className="m-auto rounded-full" />
                        <div className=" py-2 text-center">
                            <p className="font-inter text-[26px] font-semibold  text-[#993333]">Save as Draft?</p>
                            <p className="justify-start  self-stretch pt-5 text-center font-inter text-base font-normal leading-normal text-[#636363]">
                            Are you sure you want to save as a draft?
                            </p>
                        </div>
                        <div className="grid grid-cols-2 gap-4 pt-5 max-md:grid-cols-1">
                            <button onClick={handleDelete} type="submit" className="flex items-center justify-center rounded-lg bg-[#1D7EB6] px-6 py-4 text-lg font-medium text-white">
                                No
                            </button>
                            <button onClick={onSuccessfulDelete} className="flex items-center justify-center  rounded-lg border border-[#1D7EB6] px-6 py-4 text-lg font-medium text-[#1D7EB6]">
                                Yes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default DraftConfirmation;
 