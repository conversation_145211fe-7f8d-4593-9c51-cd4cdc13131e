'use client';

import type React from 'react';
import { ActionIcon, DatatableAccendingSortingIcon, DatatableDeccendingSortingIcon, DatatableSortingIcon, PaginationDownIcon, PaginationLeftIcon, PaginationRightIcon } from '@/components/icon/Icon';
import { useState, useRef, useEffect } from 'react';
import Modal from '@/components/reusable/modals/modal';
import SearchInput from '@/components/reusable/SearchBar';
import { usePathname, useRouter } from 'next/navigation';
import Loading from '@/components/layouts/loading';
import SearchDropDown from '@/components/reusable/SearchDropDown';
import DeletePostModal from './DeletePostModal';
import ArchivePostModal from './ArchivePostModal';
import CreatePostModal from './CreatePostModal';
import ViewPostModal from './ViewPostModal';
import { useToast } from '@/components/reusable/Notify';
import API_ENDPOINTS from '@/app/lib/apiRoutes';

const columns = [
    { key: 'title', label: 'Title', width: 'w-[30%]', sortable: true },
    { key: 'author', label: 'Author', width: 'w-[15%]', sortable: true },
    { key: 'category', label: 'Category', width: 'w-[12%]', sortable: true },
    { key: 'status', label: 'Status', width: 'w-[10%]', sortable: true },
    { key: 'views', label: 'Views', width: 'w-[8%]', sortable: true },
    { key: 'likes', label: 'Likes', width: 'w-[8%]', sortable: true },
    { key: 'updated', label: 'Updated', width: 'w-[12%]', sortable: true },
    { key: 'actions', label: 'Actions', width: 'w-[5%]', sortable: false },
];

// Mock blog data
const mockBlogData = [
    {
        id: 1,
        title: 'Top 10 Real Estate Trends in 2024',
        description: 'Discover the latest trends shaping the real estate market in 2024...',
        author: 'Sarah Johnson',
        category: 'Market Trends',
        status: 'Published',
        views: 1250,
        likes: 45,
        updated: '1/15/2024',
        image: 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=400&h=200&fit=crop',
        content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        tags: ['#real estate', '#trends', '#2024', '#market'],
        created: '1/14/2024, 8:30:00 PM',
        published: '1/15/2024, 3:00:00 PM'
    },
    {
        id: 2,
        title: 'How to Choose the Right Real Estate Agent',
        description: 'A comprehensive guide to finding the perfect real estate agent...',
        author: 'Mike Davis',
        category: 'Agent Tips',
        status: 'Published',
        views: 890,
        likes: 32,
        updated: '1/12/2024',
        image: 'https://images.unsplash.com/photo-1582407947304-fd86f028f716?w=400&h=200&fit=crop',
        content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        tags: ['#agent', '#tips', '#guide', '#real estate'],
        created: '1/11/2024, 2:15:00 PM',
        published: '1/12/2024, 9:00:00 AM'
    },
    {
        id: 3,
        title: 'Investment Strategies for First-Time Buyers',
        description: 'Essential strategies for those looking to make their first real estate investment...',
        author: 'Emily Chen',
        category: 'Investment',
        status: 'Draft',
        views: 0,
        likes: 0,
        updated: '1/13/2024',
        image: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=200&fit=crop',
        content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        tags: ['#investment', '#first-time', '#buyers', '#strategy'],
        created: '1/12/2024, 4:45:00 PM',
        published: null
    },
    {
        id: 4,
        title: 'Understanding Property Valuations',
        description: 'Learn how property valuations work and what factors influence them...',
        author: 'David Wilson',
        category: 'Education',
        status: 'Published',
        views: 675,
        likes: 28,
        updated: '1/8/2024',
        image: 'https://images.unsplash.com/photo-1560520653-9e0e4c89eb11?w=400&h=200&fit=crop',
        content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        tags: ['#valuation', '#property', '#education', '#guide'],
        created: '1/7/2024, 10:30:00 AM',
        published: '1/8/2024, 11:00:00 AM'
    },
    {
        id: 5,
        title: 'The Future of Smart Homes',
        description: 'Exploring how smart technology is changing the real estate landscape...',
        author: 'Lisa Rodriguez',
        category: 'Technology',
        status: 'Archived',
        views: 1150,
        likes: 52,
        updated: '1/5/2024',
        image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=200&fit=crop',
        content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        tags: ['#smart homes', '#technology', '#future', '#innovation'],
        created: '1/4/2024, 3:20:00 PM',
        published: '1/5/2024, 8:00:00 AM'
    },
];

const statusDropdown = [
    { label: 'All Statuses' },
    { label: 'Published' },
    { label: 'Draft' },
    { label: 'Archived' }
];

const categoryDropdown = [
    { label: 'All Categories' },
    { label: 'Market Trends' },
    { label: 'Agent Tips' },
    { label: 'Investment' },
    { label: 'Education' },
    { label: 'Technology' }
];

export default function BlogTable() {
    const { push } = useRouter();
      const pathname = usePathname();
    const { showToast } = useToast();
    const [page, setPage] = useState(0);
    const [loader, setLoader] = useState(false);
    const [postsPerPage, setPostsPerPage] = useState(10);
    const [blogPosts, setBlogPosts] = useState([
         {
        id: 1,
        title: '',
        description: '',
        author: '',
        category: '',
        status: '',
        views: 0,
        likes: 0,
        updated: '',
        image: '',
        content: '',
        tags: [],
        created: '',
        published: ''
    },
    ]);
    const [showDropdown, setShowDropdown] = useState(false);
    const [showActionDropdown, setShowActionDropdown] = useState<number | null>(null);
    const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>('bottom');
    const [warningModal, setWarningModal] = useState(false);
    const [archiveModal, setArchiveModal] = useState(false);
    const [createModal, setCreateModal] = useState(false);
    const [editModal, setEditModal] = useState(false);
    const [viewPostModal, setViewPostModal] = useState(false);
    const [selectedPost, setSelectedPost] = useState<any>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedRecordId, setSelectedRecordId] = useState<number>();
    const [selectedStatus, setSelectedStatus] = useState('All Statuses');
    const [selectedCategory, setSelectedCategory] = useState('All Categories');
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

    const dropdownRef = useRef<HTMLDivElement>(null);
    const actionDropdownRef = useRef<HTMLDivElement>(null);
    const actionButtonRefs = useRef<Map<number, HTMLButtonElement>>(new Map());

    // Debounce search term
    useEffect(() => {
        const timeoutId = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
        }, 300);

        return () => {
            clearTimeout(timeoutId);
        };
    }, [searchTerm]);

    // Sorting state
    const [sortConfig, setSortConfig] = useState<{
        key: string;
        direction: 'ascending' | 'descending' | null;
    }>({
        key: '',
        direction: null,
    });

    // Handle sorting
    const requestSort = (key: string) => {
        let direction: 'ascending' | 'descending' | null = 'ascending';

        if (sortConfig.key === key) {
            if (sortConfig.direction === 'ascending') {
                direction = 'descending';
            } else if (sortConfig.direction === 'descending') {
                direction = null;
            }
        }

        setSortConfig({ key, direction });
    };

    // Filter and search posts
 const filteredPosts = blogPosts.filter((post) => {
    const term = debouncedSearchTerm.toLowerCase();

    const matchesSearch =
        (post?.title?.toLowerCase?.() || '').includes(term) ||
        (post?.author?.toLowerCase?.() || '').includes(term) ||
        (post?.category?.toLowerCase?.() || '').includes(term) ||
        (post?.status?.toLowerCase?.() || '').includes(term);

    const matchesStatus =
        selectedStatus === 'All Statuses' || post.status === selectedStatus;

    const matchesCategory =
        selectedCategory === 'All Categories' || post.category === selectedCategory;

    return matchesSearch && matchesStatus && matchesCategory;
});

    // Sort the data
    const sortedPosts = [...filteredPosts].sort((a: any, b: any) => {
        const { key, direction } = sortConfig;
        if (!direction || !key) return 0;

        let aVal = a[key];
        let bVal = b[key];

        // Handle numeric sorting for views and likes
        if (key === 'views' || key === 'likes') {
            aVal = Number(aVal);
            bVal = Number(bVal);
        } else {
            aVal = aVal.toString().toLowerCase();
            bVal = bVal.toString().toLowerCase();
        }

        if (aVal < bVal) return direction === 'ascending' ? -1 : 1;
        if (aVal > bVal) return direction === 'ascending' ? 1 : -1;
        return 0;
    });

    const totalPages = Math.ceil(sortedPosts.length / postsPerPage);

    // Close dropdowns when clicking outside
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setShowDropdown(false);
            }
            if (actionDropdownRef.current && !actionDropdownRef.current.contains(event.target as Node)) {
                setShowActionDropdown(null);
            }
        }

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Handle dropdown positioning
    const handleActionClick = (postId: number, event: React.MouseEvent<HTMLButtonElement>) => {
        actionButtonRefs.current.set(postId, event.currentTarget);

        if (showActionDropdown === postId) {
            setShowActionDropdown(null);
            return;
        }

        const buttonRect = event.currentTarget.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - buttonRect.bottom;

        setDropdownPosition(spaceBelow < 200 ? 'top' : 'bottom');
        setShowActionDropdown(postId);
    };

    const goToPage = (pageNumber: number) => setPage(pageNumber);
    const handlePrev = () => setPage((prev) => Math.max(prev - 1, 0));
    const handleNext = () => setPage((prev) => Math.min(prev + 1, totalPages - 1));

    const paginationRange = () => {
        const range = [];
        let start = Math.max(page - 1, 0);
        let end = Math.min(page + 2, totalPages - 1);

        if (end - start < 3) {
            if (start === 0) {
                end = Math.min(start + 3, totalPages - 1);
            } else {
                start = Math.max(end - 3, 0);
            }
        }

        for (let i = start; i <= end; i++) {
            range.push(i);
        }

        return range;
    };

    const getSortIcon = (key: string) => {
        if (sortConfig.key !== key) {
            return <DatatableSortingIcon />;
        }

        if (sortConfig.direction === 'ascending') {
            return <DatatableAccendingSortingIcon />;
        }

        if (sortConfig.direction === 'descending') {
            return <DatatableDeccendingSortingIcon />;
        }

        return <DatatableSortingIcon />;
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
        setPage(0); // Reset to first page when searching
    };

    const deletePost = async (id: number) => {
        try {
            setLoader(true);
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));
   
                const response = await fetch(API_ENDPOINTS.BLOG_DELETED + id, {
                    method: 'DELETE',
                    credentials: 'include',
                    headers: { 'Content-Type': 'application/json' },
                     
                });

                const result = await response.json();
                if (result.success) {
                    const data = result.data;
                        setBlogPosts((prev :any)=> prev.filter((post:any) => post.id !== id));
                        showToast('Blog post deleted successfully.', 'success');
                } else {
                    console.log('Failed to fetch blog:', result.message);
                }
       

       
            
        } catch (error) {
            console.error('Delete post error:', error);
        } finally {
            setLoader(false);
        }
    };
const handleDeletePost = async(id: number) => {
    
};
    const archivePost = async (id: number) => {
        try {
            setLoader(true);
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));

            setBlogPosts(prev => prev.map(post =>
                post.id === id ? { ...post, status: 'Archived' } : post
            ));
            showToast('Blog post archived successfully.', 'success');
        } catch (error) {
            console.error('Archive post error:', error);
        } finally {
            setLoader(false);
        }
    };

    const createPost = async (postData: any) => {
        try {
            setLoader(true);
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));

            setBlogPosts(prev => [postData, ...prev]);
            showToast('Blog post created successfully.', 'success');
            setCreateModal(false);
        } catch (error) {
            console.error('Create post error:', error);
            showToast('Failed to create blog post.', 'error');
        } finally {
            setLoader(false);
        }
    };

    const updatePost = async (postData: any) => {
        try {
            setLoader(true);
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));

            setBlogPosts(prev => prev.map(post => 
                post.id === postData.id ? postData : post
            ));
            showToast('Blog post updated successfully.', 'success');
            setEditModal(false);
        } catch (error) {
            console.error('Update post error:', error);
            showToast('Failed to update blog post.', 'error');
        } finally {
            setLoader(false);
        }
    };

    const handleViewPost = (post: any) => {
        setSelectedPost(post);
        setViewPostModal(true);
        setShowActionDropdown(null);
    };

    const handleCreatePost = () => {
        setCreateModal(true);
    };

    const handleEditPost = (post: any) => {
        setSelectedPost(post);
        setEditModal(true);
        setShowActionDropdown(null);
    };

    const getStatusBadgeColor = (status: string) => {
        switch (status) {
            case 'Published':
                return 'bg-green-100 text-green-800';
            case 'Draft':
                return 'bg-yellow-100 text-yellow-800';
            case 'Archived':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    // Stats cards data
    const statsCards = [
        { label: 'Total Posts', count: blogPosts.length, color: '#1D7EB6' },
        { label: 'Published', count: blogPosts.filter(p => p.status === 'Published').length, color: '#10B981' },
        { label: 'Total Views', count: blogPosts.reduce((sum, p) => sum + p.views, 0).toLocaleString(), color: '#8B5CF6' },
        { label: 'Total Likes', count: blogPosts.reduce((sum, p) => sum + p.likes, 0), color: '#EF4444' },
    ];

useEffect(() => {
    
  fetchBlogPosts();
}, []);
  const fetchBlogPosts = async () => {
    try {
      const response = await fetch(API_ENDPOINTS.BLOG_ALL, {
        method: 'GET',
        credentials: 'include', // must be outside headers
        
      });
 
      const result = await response.json();
      if(result.success) {
 
          const data = result.data.map((post: any) => ({
                ...post,
                updated: new Date(post.updated_at).toLocaleDateString(),
                created: new Date(post.created_at).toLocaleDateString(),
                image: post.featuredImage,
                views: Number(post.viewsCount) || 0,
                likes: Number(post.likesCount) || 0,
                description: post.excerpt || '',
                status: post.statusId === 22 ? 'Published' : post.statusId === 23 ? 'Draft' : 'Archived',
                published: post.published ? new Date(post.publishDate).toLocaleDateString() : null,
            }));

          setBlogPosts(data);
        }else{
          console.log('Blogs fetched failed:', result);

      }
    } catch (error) {
      console.error('Error fetching blogs:', error);
    }
  };
const handleUpdateStatus = async (id: number, status: string) => {
    try {
        setLoader(true);
        const response = await fetch(API_ENDPOINTS.BLOG_UPDATE_STATUS + id, {
            method: 'PUT',
            credentials: 'include',
            headers: { 'Content-Type': 'application/json' },
           
            body: JSON.stringify({ statusId: status === 'Published' ? 22 : status === 'Draft' ? 23 : 5 }),
        });
        const result = await response.json();
        if (result.success) {
            setBlogPosts(prev => prev.map(post =>
                post.id === id ? { ...post, status } : post
            ));
            showToast(`Blog post ${status.toLowerCase()} successfully.`, 'success');
        } else {
            console.error('Failed to update status:', result.message);
            showToast(`Failed to update status: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('Error updating status:', error);
        showToast('Something went wrong while updating status.', 'error');
    } finally {
        setLoader(false);
    }
};


    return (
        <>
            {loader && <Loading />}

            <Modal isOpen={warningModal} onClose={() => setWarningModal(false)}>
                <DeletePostModal
                    post={blogPosts.find((p :any)=> p?.id === selectedRecordId)}
                    onClose={() => {
                        setWarningModal(false);
                    }}
                    onConfirm={() => {
                        setWarningModal(false);
                        deletePost(selectedRecordId!);
                    }}
                />
            </Modal>

            {/* Archive Post Modal */}
            <Modal isOpen={archiveModal} onClose={() => setArchiveModal(false)}>
                <ArchivePostModal
                    post={blogPosts.find((p:any)=> p?.id === selectedRecordId)}
                    onClose={() => setArchiveModal(false)}
                    onConfirm={() => {
                        setArchiveModal(false);
                        archivePost(selectedRecordId!);
                    }}
                />
            </Modal>

            {/* View Post Modal */}
            <Modal isOpen={viewPostModal} onClose={() => setViewPostModal(false)}>
                <ViewPostModal
                    post={selectedPost}
                    onClose={() => setViewPostModal(false)}
                    onDelete={deletePost}
                />
            </Modal>

            {/* Create Post Modal */}
            <Modal isOpen={createModal} onClose={() => setCreateModal(false)}>
                <CreatePostModal
                    onClose={() => setCreateModal(false)}
                    onSubmit={createPost}
                    fetchBlogPosts={fetchBlogPosts}
                    mode="create"
                />
            </Modal>

            {/* Edit Post Modal */}
            <Modal isOpen={editModal} onClose={() => setEditModal(false)}>
                <CreatePostModal
                    onClose={() => setEditModal(false)}
                    onSubmit={updatePost}
                    post={selectedPost}
                      fetchBlogPosts={fetchBlogPosts}
                    mode="edit"
                />
            </Modal>
      <div className="grid grid-cols-2 items-center justify-between  gap-3 px-3 mt-3 rounded-md bg-[#f1f5f9] py-2">
                <div 
                onClick={() => push('/blog-management')}
                className={`flex  w-full cursor-pointer items-center justify-center rounded-md font-inter text-base font-medium    p-2 text-center
                    
                    ${pathname === '/blog-management' ? 'bg-white  ' : ''}
                    `}>Blog Posts</div>


                                    <div
                                    onClick={() => push('/blog-management/subscribers')}
                                    className={`flex cursor-pointer w-full items-center font-inter text-base font-medium justify-center rounded-md    p-2 text-center
                    
                    ${pathname === '/blog-management/subscribers' ? 'bg-white  ' : ''}
                    `}>Subscribers</div>
 
            </div>
            {/* Header */}
            <div className="mb-8 flex items-center justify-end pt-7">
                {/* <div>
                    <h2 className="font-inter text-xl font-semibold text-[#2d2d2e]">Blog Management</h2>
                    <h4 className="font-inter text-[#636363]">Create and monitor blog postings on your website</h4>
                </div> */}
                <button 
                    onClick={handleCreatePost}
                    className="flex items-center gap-2 rounded-md border border-[#1D7EB6] bg-[#1D7EB6] px-4 py-3 text-sm font-medium text-white transition hover:bg-[#166da0] font-inter"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    Create Post
                </button>
            </div>

            {/* Stats Cards */}
            <div className="mb-8 rounded-lg bg-white pt-6">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                    {statsCards.map((card, index) => (
                        <div
                            key={index}
                            className="flex items-center justify-between rounded-[20px] border-2 border-[#f0f0f0] bg-[#f8f9fa] p-5 shadow-[0px_4px_20px_0px_rgba(21,32,70,0.05)] transition hover:border-blue-200"
                        >
                            <div className="flex flex-col items-start justify-start gap-2">
                                <div className="font-golosText text-5xl font-bold" style={{ color: card.color }}>
                                    {card.count}
                                </div>
                                <div className="font-inter text-base font-medium text-[#555]">{card.label}</div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Filters */}
            <div className="mb-8">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                    <div className="w-full">
                        <SearchInput onChange={handleSearchChange} value={searchTerm} placeholder="Search posts..." />
                    </div>
                    <div className="w-full">
                        <SearchDropDown
                            classes="!h-14 w-full"
                            dropdownOptions={statusDropdown}
                            initail={selectedStatus}
                            setSelectedStatus={setSelectedStatus}
                        />
                    </div>
                    <div className="w-full">
                        <SearchDropDown
                            classes="!h-14 w-full"
                            dropdownOptions={categoryDropdown}
                            initail={selectedCategory}
                            setSelectedStatus={setSelectedCategory}
                        />
                    </div>
                </div>
            </div>

            <div className="pb-4 font-inter font-normal leading-[30px]">
                <span className="text-[#636363]">You have </span>
                <span className="text-[#1D7EB6]">{sortedPosts.length} Posts</span>
            </div>

            <div className="bg-white">
                <div className="relative rounded-lg border shadow-[0px_4px_20px_0px_rgba(21,32,70,0.07)]">
                    <table className="w-full border-collapse font-inter">
                        <thead className="sticky top-0 z-[5] bg-[#e4e4e4]">
                            <tr className="w-full rounded-lg border-none">
                                {columns.map((column) => (
                                    <th
                                        key={column.key}
                                        className={`h-[72px] cursor-pointer border-none px-4 py-4 text-left font-inter text-base font-medium leading-normal text-[#2d2d2e] ${column.width}`}
                                        onClick={() => column.sortable && requestSort(column.key)}
                                    >
                                        <span className="flex items-center">
                                            {column.label}
                                            {column.sortable && getSortIcon(column.key)}
                                        </span>
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody className="max-h-[700px] overflow-y-auto">
                            {sortedPosts.length === 0 && !loader && (
                                <tr>
                                    <td colSpan={8}>
                                        <div className="flex items-center justify-center py-10 text-center text-base font-medium text-[#888]">
                                            No Posts to Show
                                        </div>
                                    </td>
                                </tr>
                            )}

                            {sortedPosts.length > 0 &&
                                sortedPosts
                                    .slice(page * postsPerPage, (page + 1) * postsPerPage)
                                    .map((post:any) => (
                                        <tr key={post?.id} className="border-b border-[#E4E4E4] text-center hover:bg-gray-50">
                                            <td className="h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-left">
                                                <div className="flex flex-col">
                                                    <div className="font-medium text-[#2d2d2e] truncate">
                                                        {post?.title}
                                                    </div>
                                                    <div className="text-sm text-[#636363] truncate max-w-[200px]">
                                                        {post?.description.length > 50 ? `${post?.description?.substring(0, 50)}...` : post?.description}
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363]">
                                                {post?.author}
                                            </td>
                                            <td className="h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363]">
                                                {post?.category}
                                            </td>
                                            <td className="h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4">
                                                <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getStatusBadgeColor(post?.status)}`}>
                                                    {post?.status}
                                                </span>
                                            </td>
                                            <td className="h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363]">
                                                {post?.views?.toLocaleString()}
                                            </td>
                                            <td className="h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363]">
                                                {post?.likes}
                                            </td>
                                            <td className="h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363]">
                                                {post?.updated}
                                            </td>
                                            <td className="relative h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4">
                                                <button
                                                    ref={(el) => el && actionButtonRefs.current.set(post.id, el)}
                                                    className="text-blue-500"
                                                    onClick={(e) => handleActionClick(post?.id, e)}
                                                >
                                                    <ActionIcon />
                                                </button>
                                                {showActionDropdown === post?.id && (
                                                    <div
                                                        ref={actionDropdownRef}
                                                        className={`absolute right-0 z-[99] w-48 rounded-md border border-gray-200 bg-white shadow-lg ${dropdownPosition === 'top' ? 'bottom-full mb-2' : 'top-full mt-2'
                                                            }`}
                                                    >
                                                        <div className="py-1">
                                                            <button
                                                                className="flex w-full items-center px-4 py-2 text-left text-sm hover:bg-gray-100"
                                                                onClick={() => handleEditPost(post)}
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                                </svg>
                                                                Edit Post
                                                            </button>
                                                            <button
                                                                className="flex w-full items-center px-4 py-2 text-left text-sm hover:bg-gray-100"
                                                                onClick={() => handleViewPost(post)}
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                                </svg>
                                                                View Post
                                                            </button>
                                                    
                                                            {post.status !== 'Published' && (
                                                                <button
                                                                    className="flex w-full items-center px-4 py-2 text-left text-sm text-green-600 hover:bg-gray-100"
                                                                    onClick={() => {
                                                                        setShowActionDropdown(null);
                                                                        handleUpdateStatus(post.id, 'Published');
                                                                        // Handle publish action
                                                                    }}
                                                                >
                                                                    <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                    </svg>
                                                                    Publish
                                                                </button>
                                                            )}
                                                            {post.status === 'Published' && (
                                                                <button
                                                                    className="flex w-full items-center px-4 py-2 text-left text-sm   hover:bg-gray-100"
                                                                    onClick={() => {
                                                                        setShowActionDropdown(null);
                                                                        handleUpdateStatus(post.id, 'Draft');
                                                                        // Handle unpublish action
                                                                    }}
                                                                >
                                                                  
                                                                    Unpublish
                                                                </button>
                                                            )}
                                                            
                                                                <button
                                                                    className="flex w-full items-center px-4 py-2 text-left text-sm text-gray-600 hover:bg-gray-100"
                                                                    onClick={() => {
                                                                        setSelectedRecordId(post.id);
                                                                        setShowActionDropdown(null);
                                                                        setArchiveModal(true);
                                                                    }}
                                                                >
                                                                    <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8l4 4 4-4m6 5-1 1a2 2 0 01-2.828 0L12 10.828 8.828 14A2 2 0 016 14l-1-1m0 0V9a2 2 0 012-2h8a2 2 0 012 2v4.172a2 2 0 01-.586 1.414z" />
                                                                    </svg>
                                                                    Archive
                                                                </button>
        <button
                                                                className="flex w-full items-center px-4 py-2 text-left text-sm text-[#993333] hover:bg-gray-100"
                                                                onClick={() => {
                                                                    setSelectedRecordId(post?.id);
                                                                    setShowActionDropdown(null);
                                                                    setWarningModal(true);
                                                                }}
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" width="24" height="24" viewBox="0 0 24 24">
                                                                    <path fill="currentColor" d="M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6z" />
                                                                </svg>
                                                                Delete
                                                            </button>                                                       
                                                        </div>
                                                    </div>
                                                )}
                                            </td>
                                        </tr>
                                    ))}
                        </tbody>
                    </table>
                </div>

                {/* Pagination */}
                {sortedPosts.length > 0 && !loader && (
                    <div className="flex flex-col items-center gap-4 border-t border-[#E4E4E4] p-4 md:flex-row md:justify-between">
                        <div className="flex items-center gap-2">
                            <button onClick={handlePrev} disabled={page === 0} className="rounded-md p-2 disabled:opacity-50">
                                <PaginationRightIcon />
                            </button>
                            <div className="flex space-x-1">
                                {paginationRange().map((i) => (
                                    <button
                                        key={i}
                                        onClick={() => goToPage(i)}
                                        className={`rounded-md px-3 py-1 ${page === i ? 'bg-[#1D7EB6] text-white' : 'hover:bg-gray-100'}`}
                                    >
                                        {i + 1}
                                    </button>
                                ))}
                            </div>
                            <button onClick={handleNext} disabled={page === totalPages - 1} className="rounded-md p-2 disabled:opacity-50">
                                <PaginationLeftIcon />
                            </button>
                        </div>
                        <div className="relative flex items-center gap-2">
                            <span className="text-sm text-gray-500">Showing</span>
                            <div className="relative" ref={dropdownRef}>
                                <button
                                    className="flex items-center gap-1 rounded-md border border-gray-200 bg-[#EDF5F9] px-2 py-1 text-sm"
                                    onClick={() => setShowDropdown(!showDropdown)}
                                >
                                    {postsPerPage}
                                    <PaginationDownIcon />
                                </button>
                                {showDropdown && (
                                    <div className="absolute left-0 z-10 -mt-40 w-16 rounded-md border border-gray-200 bg-white shadow-lg">
                                        <div className="py-1">
                                            {[10, 20, 50].map((value) => (
                                                <button
                                                    key={value}
                                                    className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                                    onClick={() => {
                                                        setPostsPerPage(value);
                                                        setShowDropdown(false);
                                                        setPage(0);
                                                    }}
                                                >
                                                    {value}
                                                </button>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                            <span className="text-sm text-gray-500">Posts out of {sortedPosts.length}</span>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
}