'use client';
import { showMessage } from '@/app/lib/Alert';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';

const ForgetPassword = () => {
    const [email, setEmail] = useState('');
    const [error, setError] = useState('');
    const { push } = useRouter();
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!email) {
            setError('Email is required');
            return;
        } else {
            setError('');
        }

        try {
            const myHeaders = new Headers();
            myHeaders.append('Content-Type', 'application/json');

            const raw = JSON.stringify({
                email,
            });

            const requestOptions: RequestInit = {
                method: 'POST',
                headers: myHeaders,
                body: raw,
                redirect: 'follow',
                credentials: 'include',
            };

            // fetch(API_ENDPOINTS.AGENT_FORGET_PASSWORD, requestOptions)
            //     .then((response) => response.json())
            //     .then((result) => {
            //         if (result.success) {
            //             showMessage(result?.message, 'success');
            //             localStorage.setItem('tempmail', email);
            //             push('/reset-password-otp');
            //         } else if (result.message == 'An OTP has already been sent. Please check your email.') {
            //             localStorage.setItem('tempmail', email);
            //             push('/reset-password-otp');
            //         }
            //     })
            //     .catch((error) => {
            //         console.error(error);
            //         showMessage('Something went wrong', 'error');
            //     });
        } catch (error) {
            console.log(error);
            showMessage('Something went wrong', 'error');
        }
    };

    return (
        <div className="flex w-full items-center justify-center ">
            {/* Title */}
            <div className="font-inter">
                <div className="py-2 text-center font-golosText">
                    <span className="text-[26px] font-semibold text-[#993333]">Forgot</span>
                    <span className="text-[26px] font-semibold text-[#2d2d2e]"> Password</span>
                </div>
                <div className="mb-5 text-center font-inter text-base font-normal text-[#636363]">Enter your registered email to receive a verification code to reset your password.</div>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Email"
                        className="w-full self-stretch rounded-lg border border-[#e4e4e4] px-5 py-4 font-inter focus:outline-[#1D7EB6]"
                    />
                    {error && <div className="!mt-1 text-sm text-[#E53E3E]">{error}</div>}

                    <div className="">
                        <button type="submit" className="flex h-14 w-full items-center justify-center self-stretch rounded-lg bg-[#1d7eb6] text-lg font-medium text-white">
                            Continue
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default ForgetPassword;
