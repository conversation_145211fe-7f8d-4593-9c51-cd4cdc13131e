'use client';

import type React from 'react';
import { ActionIcon, DatatableAccendingSortingIcon, DatatableDeccendingSortingIcon, DatatableSortingIcon, PaginationDownIcon, PaginationLeftIcon, PaginationRightIcon } from '@/components/icon/Icon';
import { useState, useRef, useEffect } from 'react';
import StatusBadge, { statusStyles } from '@/components/reusable/StatusBandage';
import Modal from '@/components/reusable/modals/modal';
import SuccessfullyDeleted from '@/components/reusable/modals/SuccessfullyDeleted';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import SearchInput from '@/components/reusable/SearchBar';
import { useRouter, useSearchParams } from 'next/navigation';
import Loading from '@/components/layouts/loading';
import SearchDropDown from '@/components/reusable/SearchDropDown';
import TextAreaInput from '@/components/reusable/TextAreaInput';
import { DocumentCards } from '@/store/utils.ts/types/AgentProfile';
import { generateDocumentTableData } from '@/store/utils.ts/functions';
import { showMessage } from '@/app/lib/Alert';
import WarningModal from '@/components/reusable/modals/WarningModal';

const columns = [
    { key: 'checkbox', label: '', width: 'w-[3%]' },
    { key: 'document', label: 'Document', width: 'w-[20%]' },
    { key: 'agent', label: 'Agent', width: 'w-[14%]' },
    { key: 'type', label: 'Type', width: 'w-[10%]' },
    { key: 'status', label: 'Status', width: 'w-[10%]' },
    { key: 'uploadDate', label: 'Upload Date', width: 'w-[12%]' },
    { key: 'expiryDate', label: 'Expiry Date', width: 'w-[12%]' },
    { key: 'size', label: 'Size', width: 'w-[8%]' },
    { key: 'action', label: 'Actions', width: 'w-[11%]' },
];

const getRandomColor = () => {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
};

export default function DocumentTable() {
    const dropdown = [{ label: 'All' }, { label: 'Pending' }, { label: 'Verified' }, { label: 'Rejected' }, { label: 'Expired' }, { label: 'Expired Soon' }];

    const dropdown3 = [{ label: 'Pending' }, { label: 'Verified' }, { label: 'Rejected' }, { label: 'Expired' }, { label: 'Expired Soon' }];

    const dropdown2 = [
        { label: 'All Types' },
        { label: 'License' },
        { label: 'Passport' },
        { label: 'Visa' },
        { label: 'Emirates ID' },
        { label: 'Proof of Employment' },
        { label: 'Profile Photo' },
        { label: 'Supporting Document' },
        { label: 'Other' },
    ];

    const { push } = useRouter();
    const [page, setPage] = useState(0);
    const [loader, setLoader] = useState(false);
    const [agentApplicationsPerPage, setAgentApplicationsPerPage] = useState(10);
    const [agentApplications, setFetchedAgentApplications] = useState<any[]>([]);
    const [selectedDocument, setSelectedDocument] = useState<any>(null);
    const [documentCounts, setDocumentCounts] = useState<DocumentCards[]>([]);
    const totalPages = Math.ceil(agentApplications.length / agentApplicationsPerPage);
    const [showDropdown, setShowDropdown] = useState(false);
    const [showActionDropdown, setShowActionDropdown] = useState<number | null>(null);
    const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>('bottom');
    const [updateModalReason, setUpdateModalReason] = useState(false);
    const [updateBulkModalReason, setUpdateBulkModalReason] = useState(false);
    const [updateModalSuccess, setUpdateModalSuccess] = useState(false);
    const [warningModal, setWarningModal] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [reasonError, setReasonError] = useState('');
    const [selectedRecordId, setSelectedRecordId] = useState<number>();
    const [selectedType, setSelectedType] = useState('All Types');
    const [selectedItems, setSelectedItems] = useState<string[]>([]);
    const params = useSearchParams();
    const [reasons, setReasons] = useState<any[]>([]);
    const [expandedReasonId, setExpandedReasonId] = useState<number | null>(null);
    const [selectedDocumentUrl, setSelectedDocumentUrl] = useState<string | null>(null);
    const [selectedDocumentExtension, setSelectedDocumentExtension] = useState<string | null>(null);
    const [showDocumentModal, setShowDocumentModal] = useState(false);
    const [isPrivate, setIsPrivate] = useState(false);

    let status = params.get('status');
    const [selectedStatus, setSelectedStatus] = useState(status || 'All');
    const [selectedStatusToUpdate, setSelectedStatusToUpdate] = useState(status || 'Pending');

    const [successModalData, setSuccessModalData] = useState({
        title: '',
        description: '',
    });

    const [warningModalData, setWarningModalData] = useState({
        title: '',
        description: '',
    });

    const [formValues, setFormValues] = useState({
        status: '',
        rejectionReason: '',
    });

    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
    const dropdownRef = useRef<HTMLDivElement>(null);
    const actionDropdownRef = useRef<HTMLDivElement>(null);
    const tableContainerRef = useRef<HTMLDivElement>(null);
    const actionButtonRefs = useRef<Map<number, HTMLButtonElement>>(new Map());

    useEffect(() => {
        setSelectedItems([]);
        const timeoutId = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
        }, 300); // adjust delay as needed

        return () => {
            clearTimeout(timeoutId); // clear timeout on new keystroke
        };
    }, [searchTerm]);

    // Sorting state
    const [sortConfig, setSortConfig] = useState<{
        key: string;
        direction: 'ascending' | 'descending' | null;
    }>({
        key: '',
        direction: null,
    });

    // Handle sorting
    const requestSort = (key: string) => {
        let direction: 'ascending' | 'descending' | null = 'ascending';

        if (sortConfig.key === key) {
            if (sortConfig.direction === 'ascending') {
                direction = 'descending';
            } else if (sortConfig.direction === 'descending') {
                direction = null;
            }
        }

        setSortConfig({ key, direction });
    };

    const filteredAgentApplications = agentApplications.filter((doc: any) => {
        const term = debouncedSearchTerm.toLowerCase();

        const docName = (doc.document || '').toLowerCase();
        const agent = (doc.agent || '').toLowerCase();
        const type = (doc.type || '').toLowerCase();
        const status = (doc.status || '').toLowerCase();
        const uploadDate = (doc.uploadDate || '').toLowerCase();
        const expiryDate = (doc.expiryDate || '').toLowerCase();

        return docName.includes(term) || agent.includes(term) || type.includes(term) || status.includes(term) || uploadDate.includes(term) || expiryDate.includes(term);
    });

    // Sort the data
    const sortedAgentApplications = [...filteredAgentApplications].sort((a: any, b: any) => {
        const { key, direction } = sortConfig;
        if (!direction || !key) return 0;

        const aVal = (a[key] || '').toString().toLowerCase();
        const bVal = (b[key] || '').toString().toLowerCase();

        if (aVal < bVal) return direction === 'ascending' ? -1 : 1;
        if (aVal > bVal) return direction === 'ascending' ? 1 : -1;
        return 0;
    });

    // Close dropdowns when clicking outside
    useEffect(() => {
        if (status) {
            setSelectedStatus(status);
        }

        function handleClickOutside(event: MouseEvent) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setShowDropdown(false);
            }
            if (actionDropdownRef.current && !actionDropdownRef.current.contains(event.target as Node)) {
                setShowActionDropdown(null);
            }
        }

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Handle dropdown positioning
    const handleActionClick = (applicationId: number, event: React.MouseEvent<HTMLButtonElement>) => {
        // Store the button reference
        actionButtonRefs.current.set(applicationId, event.currentTarget);

        // Toggle dropdown
        if (showActionDropdown === applicationId) {
            setShowActionDropdown(null);
            return;
        }

        // Check position in viewport
        const buttonRect = event.currentTarget.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - buttonRect.bottom;

        // If less than 200px below (approximate dropdown height), position above
        setDropdownPosition(spaceBelow < 200 ? 'top' : 'bottom');
        setShowActionDropdown(applicationId);
    };

    const goToPage = (pageNumber: number) => setPage(pageNumber);
    const handlePrev = () => setPage((prev) => Math.max(prev - 1, 0));
    const handleNext = () => setPage((prev) => Math.min(prev + 1, totalPages - 1));

    const paginationRange = () => {
        const range = [];
        let start = Math.max(page - 1, 0);
        let end = Math.min(page + 2, totalPages - 1);

        if (end - start < 3) {
            if (start === 0) {
                end = Math.min(start + 3, totalPages - 1);
            } else {
                start = Math.max(end - 3, 0);
            }
        }

        for (let i = start; i <= end; i++) {
            range.push(i);
        }

        return range;
    };

    const getSortIcon = (key: string) => {
        if (sortConfig.key !== key) {
            return <DatatableSortingIcon />;
        }

        if (sortConfig.direction === 'ascending') {
            return <DatatableAccendingSortingIcon />;
        }

        if (sortConfig.direction === 'descending') {
            return <DatatableDeccendingSortingIcon />;
        }

        return <DatatableSortingIcon />;
    };

    const fetchData = async () => {
        try {
            setFetchedAgentApplications([]);
            const myHeaders = new Headers();
            setLoader(true);

            const queryParams = new URLSearchParams({
                status: selectedStatus,
            });

            const requestOptions: RequestInit = {
                method: 'GET',
                headers: myHeaders,
                redirect: 'follow',
                credentials: 'include',
            };

            await fetch(`${API_ENDPOINTS.DOCUMENTS}?${queryParams}`, requestOptions)
                .then((response) => response.json())
                .then((result) => {
                    if (result.success) {
                        const { docuCountsStatusWise, documents } = result.data;
                        setFetchedAgentApplications(generateDocumentTableData(documents));

                        // Total documents count
                        const total = Array.isArray(docuCountsStatusWise)
                            ? docuCountsStatusWise.reduce((sum, item) => {
                                  const count = parseInt(item?.mediaCount || '0', 10);
                                  return sum + count;
                              }, 0)
                            : 0;

                        // Transform into cards
                        const documentCards: DocumentCards[] = [
                            {
                                id: 0,
                                type: 'Total Documents',
                                count: total.toString(),
                                color: '#3b82f6', // Fixed color for total
                            },
                            ...(Array.isArray(docuCountsStatusWise)
                                ? docuCountsStatusWise.map((item) => ({
                                      id: item?.statusId ?? -1,
                                      type: item?.statusName ?? 'Unknown',
                                      count: item?.mediaCount ?? '0',
                                      color: statusStyles[item?.statusName]?.text,
                                  }))
                                : []),
                        ];

                        setDocumentCounts(documentCards);
                        setSelectedItems([]);
                    }
                    setLoader(false);
                })
                .catch((error) => {
                    console.error(error);
                    setLoader(false);
                });
            // setFetchedAgentApplications(data);
        } catch (error) {
            console.error(error);
            setLoader(false);
        }
    };

    // Update useEffect to apply filters to dummy data
    useEffect(() => {
        fetchData();
        if (params.get('status')) {
            setTimeout(() => {
                const url = new URL(window.location.href);
                const allParams = url.searchParams;

                // Remove specific query parameters
                allParams.delete('status');
                allParams.delete('startDate');
                allParams.delete('endDate');
                allParams.delete('role');

                // Construct the new URL
                const newUrl = `${url.pathname}${allParams.toString() ? '?' + allParams.toString() : ''}`;

                // Update URL in-place without reloading or navigation
                window.history.replaceState(null, '', newUrl);
            }, 3000);
        }
    }, [selectedStatus]);

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
    };

    // Eye icon component
    const EyeIconTable = () => (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
            />
        </svg>
    );

    // FileIcon component
    const FileIcon = () => (
        <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
        </svg>
    );

    const VerifiedIcon = () => (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" width="24" height="24" viewBox="0 0 24 24">
            <g fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="9" />
                <path d="m8 12l3 3l5-6" />
            </g>
        </svg>
    );

    const RejectedIcon = () => (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M8.4 16.308L12 12.708L15.6 16.308L16.308 15.6L12.708 12L16.308 8.4L15.6 7.692L12 11.292L8.4 7.692L7.692 8.4L11.292 12L7.692 15.6L8.4 16.308ZM12.003 21C10.759 21 9.589 20.764 8.493 20.292C7.39767 19.8193 6.44467 19.178 5.634 18.368C4.82333 17.558 4.18167 16.606 3.709 15.512C3.23633 14.418 3 13.2483 3 12.003C3 10.7577 3.23633 9.58767 3.709 8.493C4.181 7.39767 4.82133 6.44467 5.63 5.634C6.43867 4.82333 7.391 4.18167 8.487 3.709C9.583 3.23633 10.753 3 11.997 3C13.241 3 14.411 3.23633 15.507 3.709C16.6023 4.181 17.5553 4.82167 18.366 5.631C19.1767 6.44033 19.8183 7.39267 20.291 8.488C20.7637 9.58333 21 10.753 21 11.997C21 13.241 20.764 14.411 20.292 15.507C19.82 16.603 19.1787 17.556 18.368 18.366C17.5573 19.176 16.6053 19.8177 15.512 20.291C14.4187 20.7643 13.249 21.0007 12.003 21ZM12 20C14.2333 20 16.125 19.225 17.675 17.675C19.225 16.125 20 14.2333 20 12C20 9.76667 19.225 7.875 17.675 6.325C16.125 4.775 14.2333 4 12 4C9.76667 4 7.875 4.775 6.325 6.325C4.775 7.875 4 9.76667 4 12C4 14.2333 4.775 16.125 6.325 17.675C7.875 19.225 9.76667 20 12 20Z"
                fill="currentColor"
            />
        </svg>
    );

    const NoteIcon = () => (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" width="24" height="24" viewBox="0 0 24 24">
            <path fill="currentColor" d="M3 20.59L6.59 17H18a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2zM3 22H2V6a3 3 0 0 1 3-3h13a3 3 0 0 1 3 3v9a3 3 0 0 1-3 3H7z" />
        </svg>
    );

    const updateDocumentStatus = async (id: number) => {
        setReasonError('');

        if (!id) {
            showMessage('Missing required data.', 'error');
            return;
        }

        if (selectedStatusToUpdate.trim() != '' && selectedStatusToUpdate === 'Rejected' && formValues.rejectionReason.trim() === '') {
            setReasonError('Reason field is required!');
            return;
        }

        try {
            setShowActionDropdown(null);
            setUpdateModalReason(false);

            setLoader(true);
            const formData = new FormData();
            formData.append('status', selectedStatusToUpdate);
            formData.append('rejectionReason', formValues.rejectionReason);
            formData.append('isPrivate', String(isPrivate));

            const requestOptions: RequestInit = {
                method: 'PUT',
                headers: new Headers(),
                redirect: 'follow',
                credentials: 'include',
                body: formData,
            };

            const response = await fetch(`${API_ENDPOINTS.DOCUMENTS}/${id || selectedRecordId}/status`, requestOptions);
            const result = await response.json();

            if (result.success) {
                setUpdateModalSuccess(true);
                setSuccessModalData({
                    title: 'Success',
                    description: `${!isPrivate ? 'Document status updated successfully.' : 'Document note added successfully.'}`,
                });
                setIsPrivate(false);
                fetchData(); // Refresh list
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoader(false);
        }
    };

    const fetchReasonsForDocument = async (docId: number) => {
        try {
            setReasons([]);
            const response = await fetch(`${API_ENDPOINTS.DOCUMENTS}/${docId}/getReasons`, {
                method: 'GET',
                credentials: 'include',
            });
            const result = await response.json();
            if (result.success) {
                console.log(result.data.reasons);
                setReasons(result.data.reasons || []);
            } else {
                console.error('Failed to fetch reasons:', result.message);
                setReasons([]); // reset on error
            }
        } catch (error) {
            console.error('Error fetching reasons:', error);
            setReasons([]); // reset on error
        }
    };

    const addNotesModal = async (documentId: number) => {
        setSelectedRecordId(documentId);
        setIsPrivate(true);
        setSelectedStatusToUpdate('');
        setFormValues({
            status: '',
            rejectionReason: '',
        });
        await fetchReasonsForDocument(documentId);
        setUpdateModalReason(true);
    };

    const documentStatusChange = async (status: string, documentId: number) => {
        setSelectedStatusToUpdate(status);
        setSelectedRecordId(documentId);
        setIsPrivate(false);
        setFormValues({
            status: status,
            rejectionReason: '',
        });

        if (status && status.trim().toLowerCase() === 'rejected') {
            await fetchReasonsForDocument(documentId);
            setUpdateModalReason(true);
        } else {
            updateDocumentStatus(documentId);
        }
    };

    const updateBulkDocumentStatus = async () => {
        setReasonError('');

        if (!selectedStatusToUpdate) {
            showMessage('Please select a status to update.', 'error');
            return;
        }

        if (selectedItems.length === 0) {
            showMessage('No documents selected.', 'error');
            return;
        }

        if (selectedStatusToUpdate === 'Rejected' && formValues.rejectionReason.trim() === '') {
            setReasonError('Reason field is required!');
            return;
        }

        try {
            setUpdateBulkModalReason(false);
            setLoader(true);

            const promises = selectedItems.map(async (docId) => {
                const formData = new FormData();
                formData.append('status', selectedStatusToUpdate);
                formData.append('rejectionReason', formValues.rejectionReason);

                const requestOptions: RequestInit = {
                    method: 'PUT',
                    headers: new Headers(),
                    redirect: 'follow',
                    credentials: 'include',
                    body: formData,
                };

                const res = await fetch(`${API_ENDPOINTS.DOCUMENTS}/${docId}/status`, requestOptions);
                return res.json();
            });

            const results = await Promise.all(promises);
            const allSuccessful = results.every((r) => r.success);

            if (allSuccessful) {
                setUpdateModalSuccess(true);
                setSuccessModalData({
                    title: 'Success',
                    description: 'Selected documents updated successfully.',
                });
                fetchData();
                setSelectedItems([]);
            } else {
                showMessage('Some documents failed to update.', 'error');
            }
        } catch (error) {
            console.error(error);
            showMessage('Something went wrong.', 'error');
        } finally {
            setLoader(false);
        }
    };

    const bulkDocumentStatusChange = async (status: string) => {
        setSelectedStatusToUpdate(status);
        setUpdateBulkModalReason(true);
        setFormValues({
            status: status,
            rejectionReason: '',
        });
    };

    const deleteDocumentById = async (id: number) => {
        if (!id) {
            showMessage('Missing document ID.', 'error');
            return;
        }

        try {
            setLoader(true);

            const requestOptions: RequestInit = {
                method: 'DELETE',
                headers: new Headers({
                    'Content-Type': 'application/json',
                }),
                credentials: 'include', // <-- include this if your API needs authentication
            };

            const response = await fetch(`${API_ENDPOINTS.DOCUMENTS}/${id}`, requestOptions);
            const result = await response.json();

            if (result.success) {
                setSuccessModalData({
                    title: 'Success',
                    description: 'Document deleted successfully.',
                });
                setUpdateModalSuccess(true);
                fetchData(); // refresh document list
            } else {
                showMessage(result.message || 'Failed to delete document.', 'error');
            }
        } catch (error) {
            console.error('Delete document error:', error);
            showMessage('An unexpected error occurred.', 'error');
        } finally {
            setLoader(false);
        }
    };

    return (
        <>
            {loader && <Loading />}
            <Modal isOpen={updateBulkModalReason} onClose={() => setUpdateBulkModalReason(false)} classes="!max-w-3xl">
                {/* Description */}
                <div className="  mb-5 w-full font-inter">
                    <div className="flex flex-col gap-4  pt-4 ">
                        <div className="flex flex-col gap-6 ">
                            <div className="w-full">
                                <SearchDropDown classes="!h-14 w-full" dropdownOptions={dropdown} initail={selectedStatusToUpdate} setSelectedStatus={setSelectedStatusToUpdate} />
                            </div>
                        </div>
                        <div className="flex flex-col gap-6 ">
                            <div>
                                <p className=" text-lg font-bold text-[#2d2d2e]">Reason</p>
                                <div className="grid grid-cols-1 gap-5">
                                    <TextAreaInput
                                        id="address-line-2"
                                        label=""
                                        value={formValues.rejectionReason}
                                        onChange={(e) =>
                                            setFormValues({
                                                ...formValues,
                                                rejectionReason: e.target.value,
                                            })
                                        }
                                        placeholder="Reason*"
                                    />
                                    <span className="text-sm text-red-600">{reasonError}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="flex w-full justify-end md:-ms-5">
                        <div
                            onClick={updateBulkDocumentStatus}
                            className="flex h-[46px] w-40 cursor-pointer items-center justify-center gap-2.5 rounded-md bg-blueMain px-3 text-base font-normal text-white hover:border hover:border-blueMain hover:bg-white hover:text-blueMain"
                        >
                            Submit
                        </div>
                    </div>
                </div>
            </Modal>

            <Modal isOpen={updateModalSuccess} onClose={() => setUpdateModalSuccess(false)}>
                <SuccessfullyDeleted
                    onClose={() => {
                        setUpdateModalSuccess(false);
                        setSuccessModalData({
                            title: '',
                            description: '',
                        });
                    }}
                    title={successModalData.title}
                    desc={successModalData.description}
                />
            </Modal>

            <Modal isOpen={warningModal} onClose={() => setWarningModal(false)}>
                <WarningModal
                    onClose={setWarningModal}
                    onSubmit={() => {
                        setWarningModal(false);
                        setWarningModalData({
                            title: '',
                            description: '',
                        });

                        deleteDocumentById(selectedRecordId!);
                    }}
                    title={warningModalData.title}
                    desc={warningModalData.description}
                />
            </Modal>

            <Modal isOpen={updateModalReason} onClose={() => setUpdateModalReason(false)} classes="!max-w-xl">
                <div className="font-inter">
                    {/* Modal Title */}
                    <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
                        <h2 className="text-lg font-semibold text-[#2d2d2e]">{!isPrivate ? selectedDocument?.document : 'Notes'}</h2>
                        <button onClick={() => setUpdateModalReason(false)} className="text-gray-500 hover:text-gray-700">
                            ✖️
                        </button>
                    </div>
                    {!isPrivate && (
                        <div className="space-y-2 px-6 py-4">
                            {/* Document Information */}
                            <div className="grid grid-cols-2 gap-y-2">
                                <div>
                                    <span className="font-semibold text-[#2d2d2e]">Type:</span> <span className="text-[#555]">{selectedDocument?.type}</span>
                                </div>
                                <div>
                                    <span className="font-semibold text-[#2d2d2e]">Upload Date:</span> <span className="text-[#555]">{selectedDocument?.uploadDate}</span>
                                </div>
                                <div>
                                    <span className="font-semibold text-[#2d2d2e]">Size:</span> <span className="text-[#555]">{selectedDocument?.size}</span>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Comment History */}
                    <div className="px-6 py-4">
                        <h3 className="mb-2 text-base font-semibold text-[#2d2d2e]">Comment History</h3>
                        <div className="space-y-3">
                            {reasons.length > 0 ? (
                                reasons
                                    .filter((r) => r.isPrivate === isPrivate)
                                    .map((reasonItem) => {
                                        const isExpanded = expandedReasonId === reasonItem.id;
                                        const displayText = isExpanded ? reasonItem.reason : reasonItem.reason.length > 50 ? `${reasonItem.reason.substring(0, 50)}...` : reasonItem.reason;

                                        return (
                                            <div key={reasonItem.id} className="rounded-md border border-gray-200 bg-gray-50 p-3">
                                                <div className="flex items-center justify-between">
                                                    <span className="font-semibold text-[#2d2d2e]">
                                                        {reasonItem.firstName} {reasonItem.lastName}
                                                    </span>
                                                    <div className="flex items-center gap-2">
                                                        {reasonItem.created_at && <span className="text-xs text-[#888]">{new Date(reasonItem.created_at).toLocaleString()}</span>}
                                                    </div>
                                                </div>

                                                <div className="mt-1 flex items-center gap-2 text-xs italic text-[#666]">
                                                    {reasonItem.oldStatusName && <span className="font-medium text-[#444]">{reasonItem.oldStatusName}</span>}

                                                    {reasonItem.oldStatusName && reasonItem.newStatusName && reasonItem.oldStatusName !== reasonItem.newStatusName && (
                                                        <>
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                className="h-4 w-4 text-[#888]"
                                                                fill="none"
                                                                viewBox="0 0 24 24"
                                                                stroke="currentColor"
                                                                strokeWidth={2}
                                                            >
                                                                <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                                                            </svg>
                                                            <span className="font-medium text-[#444]">{reasonItem.newStatusName}</span>
                                                        </>
                                                    )}

                                                    {reasonItem.oldStatusName && reasonItem.newStatusName && reasonItem.oldStatusName === reasonItem.newStatusName && (
                                                        <span className="font-medium text-[#444]">(current)</span>
                                                    )}
                                                </div>

                                                <div
                                                    className={`transition-max-height mt-1 overflow-hidden whitespace-pre-wrap break-words text-sm text-[#555] duration-300 ease-in-out ${
                                                        isExpanded ? 'max-h-[1000px]' : 'max-h-[100px]'
                                                    }`}
                                                >
                                                    {displayText}
                                                </div>
                                                {reasonItem.reason.length > 50 && (
                                                    <button
                                                        onClick={() => setExpandedReasonId(isExpanded ? null : reasonItem.id)}
                                                        className="mt-1 text-sm text-blue-500 hover:underline focus:outline-none"
                                                    >
                                                        {isExpanded ? 'Show Less' : 'Show More'}
                                                    </button>
                                                )}
                                            </div>
                                        );
                                    })
                            ) : (
                                <p className="text-sm text-[#888]">No comment history found.</p>
                            )}
                        </div>
                    </div>

                    {/* Rejection Reason */}
                    <div className="  mb-5 w-full font-inter">
                        <div className="flex flex-col gap-4  pt-4 ">
                            {!isPrivate && (
                                <div className="flex flex-col gap-6 ">
                                    <div className="w-full">
                                        <SearchDropDown classes="!h-14 w-full" dropdownOptions={dropdown3} initail={selectedStatusToUpdate} setSelectedStatus={setSelectedStatusToUpdate} />
                                    </div>
                                </div>
                            )}
                            <div className="flex flex-col px-6 pt-4">
                                <p className="text-lg font-bold text-[#2d2d2e]">Reason</p>
                                <div className="grid grid-cols-1 gap-5">
                                    <TextAreaInput
                                        id="rejection-reason"
                                        label=""
                                        value={formValues.rejectionReason}
                                        onChange={(e) =>
                                            setFormValues({
                                                ...formValues,
                                                rejectionReason: e.target.value,
                                            })
                                        }
                                        placeholder="Reason*"
                                    />
                                    <span className="text-sm text-red-600">{reasonError}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-3 border-t border-gray-200 px-6 py-4">
                        <button onClick={() => setUpdateModalReason(false)} className="rounded-md border border-gray-300 px-5 py-2 text-[#2d2d2e] hover:bg-gray-100">
                            Cancel
                        </button>
                        <button onClick={() => updateDocumentStatus(selectedRecordId!)} className="rounded-md bg-red-600 px-5 py-2 text-white hover:bg-red-700">
                            Submit
                        </button>
                    </div>
                </div>
            </Modal>

            {/* Document Cards Section */}
            <div className="mb-8 rounded-lg bg-white pt-6">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-3 lg:grid-cols-5">
                    {documentCounts.map((doc, index) => (
                        <div
                            onClick={(e) => {
                                e.preventDefault();
                                doc.type === 'Total Documents' ? setSelectedStatus('All') : setSelectedStatus(doc.type);
                            }}
                            key={`${index}-${doc.id}`}
                            className={`flex shrink grow basis-0 cursor-pointer items-center justify-between rounded-[20px] border-2 p-5 shadow-[0px_4px_20px_0px_rgba(21,32,70,0.05)] transition
                                    ${selectedStatus === (doc.type === 'Total Documents' ? 'All' : doc.type) ? 'border-blue-500 bg-white' : 'border-[#f0f0f0] bg-[#f8f9fa]'}`}
                        >
                            <div className="flex flex-col items-start justify-start gap-2">
                                <div className="font-golosText text-5xl font-bold" style={{ color: doc.color }}>
                                    {doc.count}
                                </div>
                                <div className="font-inter text-base font-medium text-[#555]">{doc.type}</div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Document filters */}
            <div className="mb-8">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                    <div className="w-full">
                        <SearchInput onChange={handleSearchChange} value={searchTerm} placeholder="Search documents..." />
                    </div>
                    <div className="w-full">
                        <SearchDropDown classes="!h-14 w-full" dropdownOptions={dropdown} initail={selectedStatus} setSelectedStatus={setSelectedStatus} />
                    </div>
                    <div className="w-full">
                        <SearchDropDown classes="!h-14 w-full" dropdownOptions={dropdown2} initail={selectedType} setSelectedStatus={setSelectedType} />
                    </div>
                </div>
            </div>

            <div className=" mt- pb-4 font-inter font-normal leading-[30px]">
                <span className="text-[#636363]">You have </span>
                <span className="text-[#993333]">{agentApplications.length} Documents</span>
            </div>

            {selectedItems.length > 0 && (
                <div className="mb-4 flex items-center justify-between rounded-md border bg-white px-6 py-4 shadow-sm">
                    <p className="text-sm font-medium text-gray-700">{selectedItems.length} document(s) selected</p>
                    <div className="flex gap-3">
                        <button
                            className="inline-flex items-center gap-2 rounded bg-gray-900 px-4 py-2 text-sm font-medium text-white hover:bg-gray-800"
                            onClick={() => {
                                bulkDocumentStatusChange('Verified');
                            }}
                        >
                            <VerifiedIcon /> <span>Verify All</span>
                        </button>
                        <button
                            className="inline-flex items-center gap-2 rounded border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100"
                            onClick={() => {
                                bulkDocumentStatusChange('Rejected');
                            }}
                        >
                            <RejectedIcon /> <span>Reject All</span>
                        </button>
                    </div>
                </div>
            )}

            <div className="bg-white">
                <div ref={tableContainerRef} className="relative rounded-lg rounded-tl-lg rounded-tr-lg border shadow-[0px_4px_20px_0px_rgba(21,32,70,0.07)]">
                    <table className="w-full border-collapse font-inter">
                        <thead className="sticky top-0 z-[5] bg-[#e4e4e4]">
                            <tr className="w-full rounded-lg rounded-tl-lg rounded-tr-lg border-none">
                                {columns.map((column) => (
                                    <th
                                        key={column.key}
                                        className={`h-[72px] cursor-pointer border-none px-4 py-4 text-left font-inter text-base font-medium leading-normal text-[#2d2d2e] ${column.width}`}
                                        onClick={() => column.key !== 'action' && column.key !== 'checkbox' && requestSort(column.key)}
                                    >
                                        {column.key === 'checkbox' ? (
                                            <input
                                                type="checkbox"
                                                checked={
                                                    selectedItems.length > 0 &&
                                                    sortedAgentApplications
                                                        .filter((doc) => selectedType === 'All Types' || doc.type === selectedType)
                                                        .slice(page * agentApplicationsPerPage, (page + 1) * agentApplicationsPerPage)
                                                        .every((doc) => selectedItems.includes(doc.imgId))
                                                }
                                                onChange={(e) => {
                                                    const visibleIds = sortedAgentApplications
                                                        .filter((doc) => selectedType === 'All Types' || doc.type === selectedType)
                                                        .slice(page * agentApplicationsPerPage, (page + 1) * agentApplicationsPerPage)
                                                        .map((doc) => doc.imgId);

                                                    if (e.target.checked) {
                                                        // Add only missing visibleIds
                                                        setSelectedItems((prev) => Array.from(new Set([...prev, ...visibleIds])));
                                                    } else {
                                                        // Remove all visibleIds
                                                        setSelectedItems((prev) => prev.filter((id) => !visibleIds.includes(id)));
                                                    }
                                                }}
                                                className="h-4 w-4 rounded border-gray-300"
                                            />
                                        ) : (
                                            <span className="flex items-center">
                                                {column.label}
                                                {column.key !== 'action' && column.key !== 'checkbox' && getSortIcon(column.key)}
                                            </span>
                                        )}
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody className="max-h-[700px] overflow-y-auto max-lg:overflow-x-auto">
                            {sortedAgentApplications.length === 0 && !loader && (
                                <tr>
                                    <td colSpan={9}>
                                        <div className="flex items-center justify-center py-10 text-center text-base font-medium text-[#888]">No Records to Show</div>
                                    </td>
                                </tr>
                            )}

                            {sortedAgentApplications.length > 0 &&
                                sortedAgentApplications
                                    .filter((doc) => selectedType === 'All Types' || doc.type === selectedType)
                                    .slice(page * agentApplicationsPerPage, (page + 1) * agentApplicationsPerPage)
                                    .map((doc, index) => (
                                        <tr key={doc.id} className="text-grayText border-b border-[#E4E4E4] text-center hover:bg-gray-50">
                                            <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[0].width}`}>
                                                <input
                                                    type="checkbox"
                                                    value={doc.imgId}
                                                    checked={selectedItems.includes(doc.imgId)}
                                                    onChange={(e) => {
                                                        const id = doc.imgId;
                                                        if (e.target.checked) {
                                                            setSelectedItems((prev) => [...prev, id]);
                                                        } else {
                                                            setSelectedItems((prev) => prev.filter((item) => item !== id));
                                                        }
                                                    }}
                                                    className="h-4 w-4 rounded border-gray-300"
                                                />
                                            </td>
                                            <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[1].width}`}>
                                                <div className="flex items-center">
                                                    {doc.document && <FileIcon />}
                                                    {doc.document}
                                                </div>
                                            </td>
                                            <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[2].width}`}>{doc.agent}</td>
                                            <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[3].width}`}>{doc.type}</td>
                                            <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal ${columns[4].width}`}>
                                                <div className="flex flex-col items-center space-y-1">
                                                    <StatusBadge status={doc.status} />
                                                </div>
                                            </td>
                                            <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[5].width}`}>{doc.uploadDate}</td>
                                            <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[6].width}`}>{doc.expiryDate}</td>
                                            <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[7].width}`}>{doc.size}</td>
                                            <td className={`relative h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 ${columns[8].width}`}>
                                                <div className="flex items-center justify-center space-x-2">
                                                    <button
                                                        title="Preview Document"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            const extension = doc.extension?.toLowerCase();
                                                            if (doc.type === 'pdf' || extension === 'pdf') {
                                                                // Open PDF directly in new tab
                                                                window.open(doc.imgUrl, '_blank');
                                                            } else {
                                                                // Open in modal
                                                                setSelectedDocumentUrl(doc.imgUrl);
                                                                setSelectedDocumentExtension(extension);
                                                                setShowDocumentModal(true);
                                                            }
                                                        }}
                                                        className="text-blue-500"
                                                    >
                                                        <EyeIconTable />
                                                    </button>

                                                    <button
                                                        title="Verify Document"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            setSelectedDocument(doc);
                                                            documentStatusChange('Verified', doc.imgId);
                                                        }}
                                                        className={`transition ${doc.status == 'Verified' ? 'cursor-not-allowed text-gray-400' : 'text-blue-600 hover:text-blue-800'}`}
                                                    >
                                                        <VerifiedIcon />
                                                    </button>

                                                    <button
                                                        title="Reject Document"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            setSelectedDocument(doc);
                                                            documentStatusChange('Rejected', doc.imgId);
                                                        }}
                                                        className={`transition ${doc.status == 'Rejected' ? 'cursor-not-allowed text-gray-400' : 'text-blue-600 hover:text-blue-800'}`}
                                                    >
                                                        <RejectedIcon />
                                                    </button>

                                                    <button
                                                        title="Document note"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            setSelectedDocument(doc);
                                                            addNotesModal(doc.imgId);
                                                        }}
                                                        className={`text-blue-600 transition hover:text-blue-800`}
                                                    >
                                                        <NoteIcon />
                                                    </button>
                                                    <button ref={(el) => el && actionButtonRefs.current.set(doc.id, el)} className="text-blue-500" onClick={(e) => handleActionClick(doc.id, e)}>
                                                        <ActionIcon />
                                                    </button>
                                                </div>
                                                {showActionDropdown === doc.id && (
                                                    <div
                                                        ref={actionDropdownRef}
                                                        className={`absolute right-0 !z-[99] w-48 rounded-md border border-gray-200 bg-white shadow-lg ${
                                                            dropdownPosition === 'top' ? 'bottom-full mb-2' : 'top-full mt-2'
                                                        }`}
                                                    >
                                                        <div className="py-1">
                                                            <span
                                                                className="flex w-full cursor-pointer items-center px-4 py-2 text-left text-sm hover:bg-gray-100"
                                                                onClick={() => {
                                                                    window.open(doc.imgUrl, '_blank');
                                                                }}
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path
                                                                        strokeLinecap="round"
                                                                        strokeLinejoin="round"
                                                                        strokeWidth={2}
                                                                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                                                                    />
                                                                </svg>
                                                                Download
                                                            </span>
                                                            <span
                                                                onClick={() => {
                                                                    fetchReasonsForDocument(doc.imgId);
                                                                    setSelectedStatusToUpdate('');
                                                                    setFormValues({ status: '', rejectionReason: '' });
                                                                    setSelectedDocument(doc);
                                                                    setSelectedRecordId(doc.imgId);
                                                                    setIsPrivate(true);
                                                                    setUpdateModalReason(true);
                                                                }}
                                                                className="flex w-full cursor-pointer items-center px-4 py-2 text-left text-sm hover:bg-gray-100"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path
                                                                        strokeLinecap="round"
                                                                        strokeLinejoin="round"
                                                                        strokeWidth={2}
                                                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                                                    />
                                                                </svg>
                                                                Edit Notes & Request Reupload
                                                            </span>
                                                            <span
                                                                onClick={() => {
                                                                    setSelectedRecordId(doc.imgId);
                                                                    setWarningModalData({
                                                                        title: `Are you sure you want to delete '${doc.document}'?`,
                                                                        description: 'This action is irreversible. The document will be permanently deleted and cannot be recovered.',
                                                                    });
                                                                    setShowActionDropdown(null);
                                                                    setWarningModal(true);
                                                                }}
                                                                className="flex w-full cursor-pointer items-center px-4 py-2 text-left text-sm text-[#993333] hover:bg-gray-100"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" width="24" height="24" viewBox="0 0 24 24">
                                                                    <path fill="currentColor" d="M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6z" />
                                                                </svg>
                                                                Delete
                                                            </span>
                                                        </div>
                                                    </div>
                                                )}
                                            </td>
                                        </tr>
                                    ))}
                        </tbody>
                    </table>
                </div>

                {sortedAgentApplications.length != 0 && !loader && (
                    <div className="flex flex-col items-center gap-4 border-t border-[#E4E4E4] p-4 md:flex-row md:justify-between">
                        <div className="flex items-center gap-2">
                            <button onClick={handlePrev} disabled={page === 0} className="rounded-md p-2 disabled:opacity-50">
                                <PaginationRightIcon />
                            </button>
                            <div className="flex space-x-1">
                                {paginationRange().map((i) => (
                                    <button key={i} onClick={() => goToPage(i)} className={`rounded-md px-3 py-1 ${page === i ? 'bg-[#1D7EB6] text-white' : 'hover:bg-gray-100'}`}>
                                        {i + 1}
                                    </button>
                                ))}
                            </div>
                            <button onClick={handleNext} disabled={page === totalPages - 1} className="rounded-md p-2 disabled:opacity-50">
                                <PaginationLeftIcon />
                            </button>
                        </div>
                        <div className="relative flex items-center gap-2">
                            <span className="text-sm text-gray-500">Showing</span>
                            <div className="relative" ref={dropdownRef}>
                                <button className="flex items-center gap-1 rounded-md border border-gray-200 bg-[#EDF5F9] px-2 py-1 text-sm" onClick={() => setShowDropdown(!showDropdown)}>
                                    {agentApplicationsPerPage}
                                    <PaginationDownIcon />
                                </button>
                                {showDropdown && (
                                    <div className="absolute left-0 z-10 -mt-40 w-16 rounded-md border border-gray-200 bg-white shadow-lg">
                                        <div className="py-1">
                                            {[10, 20, 50].map((value) => (
                                                <button
                                                    key={value}
                                                    className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                                    onClick={() => {
                                                        setAgentApplicationsPerPage(value);
                                                        setShowDropdown(false);
                                                        setPage(0); // Reset to first page when changing items per page
                                                    }}
                                                >
                                                    {value}
                                                </button>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                            <span className="text-sm text-gray-500">Documents out of {agentApplications.length}</span>
                        </div>
                    </div>
                )}
            </div>

            {showDocumentModal && selectedDocumentUrl && (
                <Modal isOpen={showDocumentModal} onClose={() => setShowDocumentModal(false)} classes="!max-w-4xl">
                    <div className="mb-4 flex items-center justify-between">
                        <h2 className="text-lg font-semibold">Document Preview</h2>
                        <button onClick={() => setShowDocumentModal(false)} className="text-gray-500 hover:text-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    {selectedDocumentExtension && ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(selectedDocumentExtension) ? (
                        <div className="flex items-center justify-center">
                            <img src={selectedDocumentUrl} alt="Document" className="max-h-[80vh] max-w-full object-contain" />
                        </div>
                    ) : (
                        <div className="flex flex-col items-center justify-center">
                            <p className="mb-4">This file cannot be previewed. You can download it below:</p>
                            <a href={selectedDocumentUrl} download className="rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700">
                                Download File
                            </a>
                        </div>
                    )}
                </Modal>
            )}
        </>
    );
}
