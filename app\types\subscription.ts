export interface Package {
  id: number;
  name: string;
  description: string;
  statusId: number;
  price: number;
  currency: string;
  userType: 'agent' | 'agency';
  colorTheme: string;
  createdOn: string;
  modifiedOn: string;
  name_count: string;
  interval: string; // 'month' | 'year'
}

export interface Feature {
  featureId: number;
  featureName: string;
  featureType: 'TEXT' | 'NUMERIC' | 'BOOLEAN' | 'ENUM';
  featureConstant: string;
  displayOrder: number;
  description: string;
}

export interface FeatureValue {
  packageFeatureId: number;
  packageTypeId: number;
  featureId: number;
  featureValue: string;
  featureName: string;
  featureType: 'TEXT' | 'NUMERIC' | 'BOOLEAN' | 'ENUM';
  featureConstant: string;
  packageName: string;
  userType: 'agent' | 'agency';
} 