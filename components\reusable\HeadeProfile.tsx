import { use, useEffect, useRef, useState } from 'react';

import Image from 'next/image';

import AdminImage from '@/../public/assets/images/main/Profile.png';
import Link from 'next/link';

import { useRouter } from 'next/navigation';
import { UpIcon } from '../icon/Icon';

const Profile = () => {
    const router = useRouter();
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef<any>(null);

    const handleClickOutside = (event: any) => {
        if (dropdownRef?.current && !dropdownRef?.current?.contains(event.target)) {
            setIsOpen(false);
        }
    };

    useEffect(() => {
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);
    return (
        <div className="relative inline-block" ref={dropdownRef}>
            <button onClick={() => setIsOpen(!isOpen)} className="relative z-10 block   rounded-md  border border-transparent text-gray-700    focus:outline-none">
                <div className="  inline-flex   h-11 items-center justify-between gap-2 px-1 sm:gap-3">
                    <div className=" inline-flex justify-between">
                        <Image alt="Image" height={500} width={500} src={AdminImage} className="h-11 w-11 rounded-[10px] object-cover " />
                        <div className="flex   items-center justify-center   ps-2">
                            <h6 className="text-sm font-medium text-white sm:text-base">M Abdullah</h6>
                        </div>
                    </div>
                    <div className="flex items-center justify-end ps-2">
                        <UpIcon />
                    </div>
                </div>
            </button>

            {isOpen && (
                <div onClick={() => setIsOpen(false)} className="absolute  right-0   z-20  mt-2 w-full origin-top-right rounded-md bg-white py-2 shadow-xl  ">
                    <Link
                        href="/profile"
                        className="flex transform items-center justify-center px-3 py-3 text-sm  text-gray-600  transition-colors duration-300 hover:bg-lightred hover:text-redMain   "
                    >
                        <span className="mx-1">My Profile</span>
                    </Link>

                    <Link
                        href="/profile"
                        className="flex  transform items-center justify-center p-3 text-sm text-gray-600  transition-colors duration-300 hover:bg-lightred hover:text-redMain   "
                    >
                        <span className="mx-1">Settings</span>
                    </Link>
                </div>
            )}
        </div>
    );
};

export default Profile;
