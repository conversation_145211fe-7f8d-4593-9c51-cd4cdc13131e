'use client';
import React, { useEffect, useState } from 'react';
import { showMessage } from '@/app/lib/Alert';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
// import getAndDecryptCookie from '@/app/lib/cookies';
import { useRouter } from 'next/navigation';


export default function ProfileRegisteredPage() {
    const [userType, setUserType] = useState<'individual' | 'company' | null>(null);
    const {push} = useRouter();

    // const fetchUserTypeForRegistration = async () => {
    //     try {
    //       const userId = getAndDecryptCookie('userId');
    //         const response = await fetch(API_ENDPOINTS.GETUSER_TYPE+userId, {
    //             method: 'GET',
    //             headers: {
    //                 'Content-Type': 'application/json',
    //             },
    //         });
    //         const result = await response.json();
    //         if(result.status === 202){
    //             showMessage(result.message );
    //             push('');
    //             return;

    //         }
    //         if (result.success) {

    //             const data = result.data;
    //             if (data.accountType === 'Individual') {
    //                 setUserType('individual');
    //             } else if (data.accountType === 'Company/Agency/PropertyDeveloper') {
    //                 setUserType('company');
    //             }
    //         } else {
    //             setUserType(null);
    //             showMessage(result.message, 'error');
    //             console.log('Error fetching user type:', result.message);
    //         }
    //     } catch (error) {
    //         console.log('Error fetching user type:', error);
    //         setUserType(null);
    //     }
    // };

    // useEffect(() => {
    //     fetchUserTypeForRegistration();
    // }, []);

    return (
        <div className="flex min-h-screen items-center justify-center bg-gray-100 p-4 font-inter">
            <div className="w-full max-w-3xl overflow-hidden rounded-lg bg-white shadow-lg">
                <div className="bg-[#1d7eb6] p-4 text-white">
                    <h1 className="text-xl font-semibold">Complete Your Profile</h1>
                    <p className="text-sm opacity-90">Please provide all required information to complete your registration</p>
                </div>

                {/* {userType === "individual" && <ProfileRegistrationForm userType={userType} />}



                {userType === 'company' && (
                        <>
                            <CompanyFormSteps   />
                        </>
                    )} */}

                <div className="border-t bg-gray-50 p-4 text-center text-xs text-gray-500">All information is securely stored according to our privacy policy.</div>
            </div>
        </div>
    );
}
