import React from 'react';

interface Salesperson {
    full_name: string;
    email: string;
    phone: string;
    referral_id: string;
    commission_rate: number;
    status_name: string;
    notes?: string;
    created_at: string;
}

interface Props {
    isOpen: boolean;
    onClose: () => void;
    person: Salesperson | null;
}

const SalespersonDetailsModal: React.FC<Props> = ({ isOpen, onClose, person }) => {
    if (!isOpen || !person) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="w-full max-w-lg rounded-lg bg-white p-6 shadow-lg">
                <div className="mb-4 flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-[#2d2d2e]">Salesperson Details</h2>
                    <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path
                                fillRule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clipRule="evenodd"
                            />
                        </svg>
                    </button>
                </div>

                <div className="space-y-3 text-sm text-gray-700">
                    <div>
                        <strong>Full Name:</strong> {person.full_name}
                    </div>
                    <div>
                        <strong>Email:</strong> {person.email}
                    </div>
                    <div>
                        <strong>Phone:</strong> {person.phone || '—'}
                    </div>
                    <div>
                        <strong>Referral ID:</strong> {person.referral_id}
                    </div>
                    <div>
                        <strong>Commission Rate:</strong> {person.commission_rate}%
                    </div>
                    <div>
                        <strong>Status:</strong> {person.status_name}
                    </div>
                    <div>
                        <strong>Notes:</strong> {person.notes || '—'}
                    </div>
                    <div>
                        <strong>Created At:</strong> {new Date(person.created_at).toLocaleString()}
                    </div>
                </div>

                <div className="mt-6 text-right">
                    <button onClick={onClose} className="rounded-md bg-[#1D7EB6] px-4 py-2 text-sm text-white hover:bg-[#166da0]">
                        Close
                    </button>
                </div>
            </div>
        </div>
    );
};

export default SalespersonDetailsModal;
