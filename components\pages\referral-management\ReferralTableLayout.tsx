'use client';
import React from 'react';
import { useRouter } from 'next/navigation';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import BreadCrumButton from '@/components/reusable/BreadCrumButton';
import ReferralManagementData from './ReferralManagementData';

const ReferralTableLayout = () => {
    const { push } = useRouter();

    return (
        <DefaultPageLayout>
            <BreadCrums
                mainHeading="Referral Management"
                breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Referral Management' }]}
                ButonComponent={
                    <BreadCrumButton
                        onClick={() => {
                            push('/referral-management');
                        }}
                    />
                }
            />
            <div className="">
                <ReferralManagementData />
            </div>
        </DefaultPageLayout>
    );
};

export default ReferralTableLayout;
