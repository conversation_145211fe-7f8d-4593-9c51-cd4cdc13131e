'use client';
import { Dialog, Transition } from '@headlessui/react';
import { useState, Fragment, use } from 'react';
import * as React from 'react';
import Heading from '../Heading';
import { AdvertisingChargesChartIcon, BootAddBannerIcon, CopyIcon, DateIcon } from '@/components/icon/Icon';
import FileUpload from '../FileUploading';
import Link from 'next/link';
import ReusebleButton from '../Button';

const SearchAdvertisingModel = ({ classes }: any) => {
    const [modal2, setModal2] = useState(true);

    return (
        <>
           <Transition appear show={modal2} as={Fragment}>
            <Dialog as="div" open={modal2} onClose={() => setModal2(false)}>
                <div className="fixed inset-0 z-[999] bg-black/70 flex items-center justify-center">
                    <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0 scale-95"
                        enterTo="opacity-100 scale-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100 scale-100"
                        leaveTo="opacity-0 scale-95"
                    >
                        <Dialog.Panel className="w-screen max-w-2xl rounded-[15px] border border-[#e4e4e4] bg-white p-5 text-black shadow-lg">
                            <ModelData />
                        </Dialog.Panel>
                    </Transition.Child>
                </div>
            </Dialog>
        </Transition>
        </>
    );
};
export default SearchAdvertisingModel;
const ModelData = () => {
    return (
        <div className="inline-flex   w-full flex-col items-start  justify-center    ">
            <div className="flex  flex-col items-end justify-start  self-stretch">
                <div className=" pb-5">
                    <div className="inline-flex items-center justify-start gap-5 self-stretch pr-2.5">
                        <div className="inline-flex shrink grow basis-0 flex-col items-start justify-center gap-2.5">
                            <Heading title1="Advertise" title2="on Search Results" description="Showcase your brand with high-impact banner advertisements." reverse={true} />
                        </div>
                        <BootAddBannerIcon />
                    </div>
                    <div className="h-[0px] self-stretch border border-[#959595]"></div>
                    <div className="flex   max-h-[70vh] flex-col items-start justify-start gap-5 self-stretch overflow-y-auto  ">
                        <div className=" pt-4">
                            <p className=" text-xs font-normal text-[#555555]">Campaign ID:</p>
                            <div className="inline-flex items-center justify-start gap-1">
                                <p className="  text-base font-normal leading-normal text-[#2d2d2e]">FAA218239713</p>
                                <CopyIcon />
                            </div>
                        </div>
                        <div className="inline-flex items-center justify-start gap-5 self-stretch">
                            <div>
                                <p className="  text-xs font-normal text-[#555555]"> Ad Start Date</p>
                                <input
                                    className=" h-14   cursor-pointer items-center justify-start gap-3 rounded-lg border border-[#e4e4e4] px-5 py-4 text-base font-normal leading-normal text-neutral-400 outline-none focus:ring-0 xl:w-80  "
                                    placeholder="Ad Start Date*"
                                    type="date"
                                />
                            </div>
                            <AdDurationSelector />
                        </div>
                        <div className="flex h-14 flex-col items-start justify-start self-stretch rounded-lg border border-[#e4e4e4] px-5 py-4">
                            <input
                                type="text"
                                // value={location}
                                // onChange={(e) => setLocation(e.target.value)}
                                placeholder="Add Location(s)*"
                                className="h-full w-full bg-transparent text-base font-normal text-neutral-400 focus:outline-none"
                            />
                        </div>
                        <div className="flex h-14 flex-col items-start justify-start self-stretch rounded-lg border border-[#e4e4e4] px-5 py-4">
                            <input
                                type="text"
                                // value={location}
                                // onChange={(e) => setLocation(e.target.value)}
                                placeholder="Keywords"
                                className="h-full w-full bg-transparent text-base font-normal text-neutral-400 focus:outline-none"
                            />
                        </div>

                        <FileUpload />
                        <AdvertisingModelFooter />
                    </div>
                </div>
                <div className=" flex items-center justify-end gap-5 self-stretch pb-5">
                    <p
                        className="cursor-pointer text-center text-base font-normal leading-normal   text-[#1d7eb6]"
                        // onClick={() => setModal2(false)}
                    >
                        Cancel
                    </p>

                    <ReusebleButton text="Continue to Pay" />
                </div>
            </div>
        </div>
    );
};
function AdDurationSelector() {
    const [selected, setSelected] = useState('15');

    return (
        <div className="inline-flex flex-col items-start justify-center gap-[3px] self-stretch">
            <div className="  text-xs font-normal text-[#555555]">Select Ad Duration</div>
            <div className="inline-flex items-start justify-start gap-5">
                {['15', '30'].map((duration) => (
                    <label key={duration} className={`inline-flex cursor-pointer flex-col items-start justify-center gap-2.5 rounded-md bg-white px-3   py-1  `}>
                        <div className="inline-flex items-center justify-start gap-3 self-stretch">
                            <div className="relative flex h-6 w-6 items-center justify-center">
                                <input type="radio" name="adDuration" value={duration} checked={selected === duration} onChange={() => setSelected(duration)} className="hidden" />
                                <div className={`h-6 w-6 rounded-full border ${selected === duration ? 'border-[#1d7eb6]' : 'border-[#88939e] bg-white'} flex items-center justify-center`}>
                                    {selected === duration && <div className="h-3 w-3 rounded-full bg-[#1d7eb6]" />}
                                </div>
                            </div>
                            <div className="  text-base font-normal leading-normal text-[#636363]">{duration} Days</div>
                        </div>
                    </label>
                ))}
            </div>
        </div>
    );
}

export const AdvertisingModelFooter = () => {
    return (
        <>
            <div className=" inline-flex flex-col items-start justify-center gap-2.5 rounded-lg bg-neutral-100 px-5 py-2.5">
                <div className="s  ">
                    <div className="flex h-12 shrink grow basis-0 items-center justify-start gap-2.5">
                        <AdvertisingChargesChartIcon />
                        <p className="font-inter"><span className="text-[#636363] text-base font-normal   leading-normal ">Based on your selection you will be charged </span><span className="text-[#993333] text-base font-normal   leading-normal">AED 499</span><span className="text-[#636363] text-base font-normal   leading-normal"> for 15 Days.</span></p>
                         
                    </div>
                </div>
            </div>
            <div className="flex items-center gap-3">
                <input type="checkbox" id="confirmId" className="h-6 w-6 cursor-pointer rounded-md border border-[#88939e]" />
                <p className="      text-base font-normal leading-normal text-[#636363]">
                    <span>By checking this box, you agree to the ad payment</span>
                    <span className="ps-2">
                        <Link href="#" className="text-center  text-base  font-normal text-[#1d7eb6] hover:underline">
                            terms and conditions.
                        </Link>
                    </span>
                </p>
            </div>
        </>
    );
};
