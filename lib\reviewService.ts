import { REVIEWS_API, REVIEW_UPDATE_STATUS_API, REVIEW_DELETE_API, REVIEW_ADD_NOTE_API, REVIEW_RESTORE_API, REVIEW_FLAG_API } from '@/app/lib/apiRoutes';

interface Review {
    id: number;
    reviewerId: number;
    revieweeId: number;
    reviewText: string;
    rating: number;
    statusId: number;
    created_at: string;
    updated_at: string;
    reviewerFirstName: string | null;
    reviewerLastName: string | null;
    reviewerEmail: string;
    revieweeFirstName: string | null;
    revieweeLastName: string | null;
    revieweeEmail: string;
    revieweeType: string;
    agencyName: string | null;
    statusName: string;
    flagged?: boolean;
}

interface ReviewsResponse {
    status: number;
    success: boolean;
    message: string;
    data: {
        reviews: Review[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    };
}

interface ApiResponse {
    status: number;
    success: boolean;
    message: string;
    data?: any;
}

class ReviewService {
    private getAuthHeaders() {
        return {
            'Content-Type': 'application/json',
        };
    }

    async getReviews(page: number = 1, limit: number = 10, search?: string, status?: string, type?: string, rating?: string): Promise<ReviewsResponse> {
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString(),
            });

            if (search) params.append('search', search);
            if (status && status !== 'All Statuses') params.append('status', status);
            if (type && type !== 'All Types') params.append('type', type);
            if (rating && rating !== 'All Ratings') {
                const ratingNumber = rating.replace(' Star', '').replace('s', '');
                params.append('rating', ratingNumber);
            }

            const response = await fetch(`${REVIEWS_API}?${params.toString()}`, {
                method: 'GET',
                headers: this.getAuthHeaders(),
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ReviewsResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching reviews:', error);
            throw error;
        }
    }

    async approveReview(id: number): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            formData.append('statusName', 'Published');

            const response = await fetch(REVIEW_UPDATE_STATUS_API.replace(':id', id.toString()), {
                method: 'PATCH',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error approving review:', error);
            throw error;
        }
    }

    async rejectReview(id: number): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            formData.append('statusName', 'Rejected');

            const response = await fetch(REVIEW_UPDATE_STATUS_API.replace(':id', id.toString()), {
                method: 'PATCH',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error rejecting review:', error);
            throw error;
        }
    }

    async archiveReview(id: number): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            formData.append('statusName', 'Archived');

            const response = await fetch(REVIEW_UPDATE_STATUS_API.replace(':id', id.toString()), {
                method: 'PATCH',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error archiving review:', error);
            throw error;
        }
    }

    async deleteReview(id: number): Promise<ApiResponse> {
        try {
            const response = await fetch(REVIEW_DELETE_API.replace(':id', id.toString()), {
                method: 'DELETE',
                headers: this.getAuthHeaders(),
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error deleting review:', error);
            throw error;
        }
    }

    async hideReview(id: number, reason: string): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            formData.append('statusName', 'Hidden');
            formData.append('reason', reason);

            const response = await fetch(REVIEW_UPDATE_STATUS_API.replace(':id', id.toString()), {
                method: 'PATCH',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error hiding review:', error);
            throw error;
        }
    }

    // ✅ FIXED: Now using dedicated restore endpoint instead of status update
    async restoreReview(id: number): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            // Optional: You can specify a status, otherwise it will restore to previous status
            // formData.append('newStatus', '1'); // Uncomment if you want to force a specific status

            const response = await fetch(REVIEW_RESTORE_API.replace(':id', id.toString()), {
                method: 'PATCH',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error restoring review:', error);
            throw error;
        }
    }

    async addNote(id: number, note: string): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            formData.append('note', note);

            const response = await fetch(REVIEW_ADD_NOTE_API.replace(':id', id.toString()), {
                method: 'POST',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error adding note to review:', error);
            throw error;
        }
    }

    async flagReview(id: number): Promise<ApiResponse> {
        try {
            const response = await fetch(REVIEW_FLAG_API.replace(':id', id.toString()), {
                method: 'PATCH',
                headers: this.getAuthHeaders(),
                credentials: 'include',
                body: JSON.stringify({ flagged: true }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error flagging review:', error);
            throw error;
        }
    }

    async unflagReview(id: number): Promise<ApiResponse> {
        try {
            const response = await fetch(REVIEW_FLAG_API.replace(':id', id.toString()), {
                method: 'PATCH',
                headers: this.getAuthHeaders(),
                credentials: 'include',
                body: JSON.stringify({ flagged: false }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error unflagging review:', error);
            throw error;
        }
    }
}

export default new ReviewService();
export type { Review, ReviewsResponse, ApiResponse };
