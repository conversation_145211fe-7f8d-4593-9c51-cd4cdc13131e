import { EditIcon } from '@/components/icon/Icon';

export default function SubscriptionInfo() {
    return (
        <div className="flex flex-col gap-4 rounded-lg border p-5 font-inter py-7">
            {/* Header */}
            <div className="flex items-center justify-between gap-2 flex-wrap">
                <p className="flex flex-wrap text-[20px] md:text-[26px] font-semibold leading-7">
                    <span className="text-redMain">Subscription </span>
                    <span className="text-[#2d2d2e]">&nbsp;Information</span>
                </p>
              
            </div>

            {/* Subscription Details */}
            <div className="text-grayText flex flex-col gap-4 text-sm">
                {/* First Row */}
                <div className="flex flex-wrap gap-4">
                    <Details head={'Account Type:'} details={'Agent (Individual/Freelance)'} />
                    <Details head={'Industry:'} details={'Real Estate'} />
                    <Details head={'Plan:'} details={'Pro (AED 199/month)'} />
                    <Details head={'Seat:'} details={'1'} />
                </div>

                <div className="h-px border border-[#e4e4e4]"></div>

                {/* Second Row */}
                <div className="flex flex-wrap gap-4">
                    <Details head={'Billing Cycle:'} details={'Yearly'} />
                    <Details head={'Subscription Date:'} details={'January 1, 2025'} />
                    
                </div>
            </div>
        </div>
    );
}

export const Details = ({ head, details }: any) => {
    return (
        <div className="flex flex-wrap items-center gap-1">
            <span className="text-base font-semibold text-[#636363]">{head}</span>
            <span className="text-base font-normal text-[#636363]">{details}</span>
        </div>
    );
};