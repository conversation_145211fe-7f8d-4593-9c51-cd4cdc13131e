'use client';
import { CloseIconModel, SuccessfullyDeletedIcon } from '@/components/icon/Icon';

const SuccesfullCreation = ({ onClose, title, desc }: any) => {
    const handleDelete = () => {
        onClose(true);
    };
    return (
        <>
            <div className=" w-full " >
                <div className=" relative flex items-center justify-between ">
                    <div className=" relative flex items-start justify-start "></div>
                    <div className=" relative flex items-end justify-end ">
                        <span className="cursor-pointer   " onClick={handleDelete}>
                            <CloseIconModel />
                        </span>
                    </div>
                </div>
                <div className="h-100 flex flex-col justify-between">
                    <div className=" px-6">
                        <div className="flex items-center justify-center pb-5">
                            <SuccessfullyDeletedIcon />
                        </div>
                        <div className=" py-2 text-center">
                            <p className="font-inter text-[26px] font-semibold  text-[#993333]">{title}</p>
                            <p className="justify-start  self-stretch pt-5 text-center font-inter text-base font-normal leading-normal text-[#636363]">{desc}</p>
                        </div>

                        <div
                            onClick={handleDelete}
                            className="mt-5  flex h-[46px] w-full cursor-pointer items-center justify-center gap-2.5 rounded-md bg-blueMain px-3 text-base font-normal text-white hover:border hover:border-blueMain hover:bg-blueMain hover:text-white"
                        >
                            Continue
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default SuccesfullCreation;
