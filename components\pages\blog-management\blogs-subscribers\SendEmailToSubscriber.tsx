'use client';
import { useEffect, useRef, useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { showMessage } from '@/app/lib/Alert';
import API_ENDPOINTS, { USERS_BULK_EMAIL_API, USERS_INDIVIDUAL_EMAIL_API } from '@/app/lib/apiRoutes';
import Modal from '@/components/reusable/modals/modal';
import { Mail } from 'lucide-react';
export const SendEmailToSubscriber = ({ isOpen, onClose, emailRecipients = [], isIndividual = false, isLead = false, onSubmitSuccess , }: any) => {
    const [to, setTo] = useState('');

    const [subject, setSubject] = useState('');
    const [message, setMessage] = useState('');
    const [attachments, setAttachments] = useState<File[]>([]);
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
         
        if (isOpen && emailRecipients.length > 0) {
            if (emailRecipients) {
                setTo(emailRecipients.map((r: any) => r.email).join(', '));
            } else {
                setTo('');
            }
        } else if (!isOpen) {
            setTo('');

            setSubject('');
            setMessage('');
            setAttachments([]);
            setIsSubmitting(false);
        }
    }, [isOpen, emailRecipients]);

    const handleSendEmail = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

 
        let payload: any = {
            subject,
            message,
        };

        if (isIndividual) {
            if (!to.trim()) {
                showMessage('Please enter an email address in the To field', 'error');
                setIsSubmitting(false);
                return;
            }
           
            payload.to = to.split(',').map((email) => email.trim());
        } else {
     
            if (to.trim()) payload.to = to.split(',').map((email) => email.trim());
        }

        const formData = new FormData();
        formData.append('subject', subject);
        formData.append('message', message); 
      
        formData.append('to', JSON.stringify(payload.to || []));
 

        console.log(`Sending   email:`, payload);

        try {
            const response = await fetch(API_ENDPOINTS.SEND_BULK_NEWSLETTER_EMAIL, {
                method: 'POST',
                body: formData,
                credentials: 'include', // Important for sending cookies
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || `Failed to send   email.`);
            }

            const result = await response.json();
            showMessage(  `Email sent successfully!`, 'success');
            onSubmitSuccess?.();
            onClose();
        } catch (error: any) {
            console.error(`Error sending  email:`, error);
            showMessage(error.message || `Failed to send   email.`, 'error');
        } finally {
            setIsSubmitting(false);
        }
    };

    // Remove email from a field

    return (
        <Modal isOpen={isOpen} onClose={onClose} classes="">
            <div className="max-h-[80vh] max-w-lg rounded-md  bg-white p-6 ">
                <div className="max-h-[75vh] overflow-y-auto">
                    <h2 className="mb-4 flex items-center gap-2 text-xl font-semibold">
                        <Mail className="h-5 w-5 text-gray-600" />
                        {isIndividual ? 'Contact User' : 'Send Newsletter'}
                    </h2>
                    <form onSubmit={handleSendEmail} className="space-y-4">
                        {/* To field */}
                        <div>
                            <label htmlFor="bulk-to" className="block text-sm font-medium text-gray-700">
                                To
                            </label>
                            <EmailInputChips value={to} onChange={setTo} placeholder="Enter email address(es) separated by commas" />
                            <p className="mt-1 text-xs text-gray-500">Separate multiple email addresses with commas</p>
                        </div>

                        {/* Subject field */}
                        <div>
                            <label htmlFor="bulk-subject" className="block text-sm font-medium text-gray-700">
                                Subject
                            </label>
                            <input
                                id="bulk-subject"
                                value={subject}
                                onChange={(e) => setSubject(e.target.value)}
                                placeholder="Enter email subject"
                                className="mt-1 w-full rounded border px-3 py-2"
                                required
                            />
                        </div>

                        {/* Message field */}
                        <div>
                            <label htmlFor="bulk-message" className="block text-sm font-medium text-gray-700">
                                Message
                            </label>

                            <ReactQuill theme="snow" value={message} onChange={(value) => setMessage(value)} />
                        </div>

                        <div className="flex justify-end gap-3 pt-4">
                            <button type="button" onClick={onClose} className="rounded border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-100">
                                Cancel
                            </button>
                            <button type="submit" className="rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:opacity-50" disabled={isSubmitting}>
                                {isSubmitting ? 'Sending...' : 'Send Newletter'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </Modal>
    );
};
function EmailInputChips({ value, onChange, placeholder }: { value: string; onChange: (val: string) => void; placeholder: string }) {
    const inputRef = useRef<HTMLInputElement>(null);
    const emails = value
        .split(',')
        .map((e) => e.trim())
        .filter(Boolean);
    const [input, setInput] = useState('');

    // Simple email validation
    const isValidEmail = (email: string) => {
        return /^\S+@\S+\.\S+$/.test(email);
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setInput(e.target.value);
    };
    const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if ((e.key === 'Enter' || e.key === ',' || e.key === 'Tab' || e.key === ' ') && input.trim()) {
            e.preventDefault();
            if (!emails.includes(input.trim()) && isValidEmail(input.trim())) {
                onChange([...emails, input.trim()].join(', '));
            }
            setInput('');
        } else if (e.key === 'Backspace' && !input && emails.length > 0) {
            onChange(emails.slice(0, -1).join(', '));
        }
    };
    const handleInputBlur = () => {
        if (input.trim() && isValidEmail(input.trim()) && !emails.includes(input.trim())) {
            onChange([...emails, input.trim()].join(', '));
            setInput('');
        }
    };
    const removeEmail = (email: string) => {
        onChange(emails.filter((e) => e !== email).join(', '));
    };
    return (
        <div className="flex min-h-[42px] flex-wrap items-center gap-1 rounded border bg-white px-2 py-1 focus-within:ring-2 focus-within:ring-blue-400">
            {emails.map((email, idx) => (
                <span key={idx} className="mb-1 mr-1 flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                    {email}
                    <button type="button" className="ml-1 text-blue-500 hover:text-red-600 focus:outline-none" onClick={() => removeEmail(email)}>
                        <svg className="h-3 w-3" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </span>
            ))}
            <input
                ref={inputRef}
                className="min-w-[120px] flex-1 border-none bg-transparent px-2 py-1 text-sm outline-none"
                value={input}
                onChange={handleInputChange}
                onKeyDown={handleInputKeyDown}
                onBlur={handleInputBlur}
                placeholder={emails.length === 0 ? placeholder : ''}
            />
        </div>
    );
}
