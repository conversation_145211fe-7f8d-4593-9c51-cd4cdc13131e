
'use client';
import { useState } from 'react';
import { DeleteRedIcon, FileUploadIcon, } from '../icon/Icon';
import { ButtonBorder } from './ButtonBorder';
import Image from 'next/image';

export default function FileUpload({ allowImages }: any) {
    const [files, setFiles] = useState<File[]>([]);
    const [dragging, setDragging] = useState(false);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFiles = event.target.files;
        if (selectedFiles) {

            const newFiles = Array.from(selectedFiles);
            setFiles(prev => {
                const combined = [...prev, ...newFiles];
                return combined.slice(0, allowImages ? allowImages : 4);
            });
        }
    };

    const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        setDragging(false);
        const droppedFiles = event.dataTransfer.files;
        if (droppedFiles) {

            const newFiles = Array.from(droppedFiles);
            setFiles(prev => {
                const combined = [...prev, ...newFiles];
                return combined.slice(0, allowImages ? allowImages : 4);
            });
        }
    };

    const removeFile = (index: number) => {
        setFiles(prev => prev.filter((_, i) => i !== index));
    };

    return (
        <div className="relative flex min-h-[300px] w-full flex-col items-center justify-center font-inter">
            <div className="flex min-h-[300px] w-full flex-col items-center justify-center rounded-lg bg-white py-4">
                <div
                    className={`relative flex h-full w-full cursor-pointer flex-col items-center justify-center gap-5 rounded-lg border-2 border-dashed border-gray-300 p-4 ${dragging ? 'bg-gray-100' : ''
                        }`}
                    onDragOver={(e) => {
                        e.preventDefault();
                        setDragging(true);
                    }}
                    onDragLeave={() => setDragging(false)}
                    onDrop={handleDrop}
                    onClick={() => document?.getElementById('fileInput')?.click()}
                >
                    <FileUploadIcon />
                    <p className="text-center text-base font-medium text-[#2d2d2e]">Select a file or drag and drop here</p>
                    <p className="text-center text-sm font-normal text-[#636363]">File format should be in JPG or PNG.</p>
                    <div className="relative flex items-center justify-center">
                        <ButtonBorder
                            value="Upload"
                            className="w-36 px-3"
                            onClick={(e: React.MouseEvent) => {
                                e.stopPropagation();
                                document?.getElementById('fileInput')?.click();
                            }}
                        />
                    </div>
                </div>
                <input
                    type="file"
                    id="fileInput"
                    className="hidden"
                    onChange={handleFileChange}
                    multiple
                    accept=".jpg,.jpeg,.png"
                />

                {files.length > 0 && (
                    <div className="mt-6 w-full grid grid-cols-2 md:grid-cols-4 gap-4">
                        {files.map((file, index) => (
                            <div key={index} className="relative rounded-lg overflow-hidden">
                                <div className="aspect-square relative">
                                    <Image
                                        src={URL.createObjectURL(file) || "/placeholder.svg"}
                                        alt={`Preview ${index + 1}`}
                                        fill
                                        className="object-cover"
                                    />
                                </div>
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        removeFile(index);
                                    }}
                                    className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md"
                                >
                                    <DeleteRedIcon />
                                </button>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}
