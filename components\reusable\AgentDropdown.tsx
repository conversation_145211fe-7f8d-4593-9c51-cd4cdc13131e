"use client";
import React, { useState } from "react";
import Select, { components } from "react-select";

interface Agent {
  label: string;
  value: string;
}

interface AgentDropdownProps {
  data: Agent[];
  label: string;
  onHandleClick: (selectedAgents: Agent[]) => void;
}

export const AgentDropdown = ({ data, label, onHandleClick }: AgentDropdownProps) => {
  const [hasValue, setHasValue] = useState(false);

  const handleChange = (selected: readonly Agent[] | null) => {
    setHasValue(!!selected && selected.length > 0);
    onHandleClick(selected ? [...selected] : []);
  };

  const colourStyles = {
    control: (styles: any) => ({
      ...styles,
      width: "100%",
      backgroundColor: "transparent",
      borderRadius: "4px",
      borderColor: "#1D7EB6",
      boxShadow: "none",
      fontSize: "14px",
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      paddingRight: "10px",
      position: "relative",
      padding: "5px",
    }),
    menu: (styles: any) => ({
      ...styles,
      backgroundColor: "#F1F6F5",
      borderRadius: "4px",
      zIndex: 1000,
    }),
    menuList: (styles: any) => ({
      ...styles,
    }),    
    option: (styles: any, { isSelected }: { isSelected: boolean }) => ({
      ...styles,
      backgroundColor: isSelected ? "#cacace" : "#EEEDF4",
      ":hover": { backgroundColor: "#cacace" },
    }),
  };


  const ValueContainer = ({ children, ...props }: any) => {
    const selected = props.getValue();
    const displayText =
      selected.length > 0
        ? selected
          .map((s: Agent) => s.label)
          .join(", ")
          .substring(0, 20) + (selected.length > 3 ? "..." : "")
        : props.selectProps.placeholder;

    return (
      <components.ValueContainer {...props}>
        <div style={{ display: "flex", alignItems: "center", width: "100%", position: "relative" }} className="text-sm">
          <div
            style={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              flexGrow: 1,
            }}
          >
            {displayText}
          </div>
          {selected.length > 0 && (
            <div
              style={{
                backgroundColor: "#A94442", // Dark red color
                color: "white",
                borderRadius: "50%",
                minWidth: "20px",
                height: "20px",
                fontSize: "12px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                marginLeft: "10px",
                fontWeight: "bold",
              }}
            >
              {selected.length}
            </div>
          )}
        </div>
      </components.ValueContainer>
    );
  };

  return (
    <div className="" style={{ position: "relative", width: "100%" }}>
      <label
        className="text-black font-normal"
        style={{
          position: "absolute",
          top: hasValue ? "-8px" : "50%",
          left: "12px",
          backgroundColor: "white",
          fontSize: hasValue ? "12px" : "14px",
          padding: "0 4px",
          transition: "all 0.2s ease-in-out",
          transform: hasValue ? "translateY(0)" : "translateY(-50%)",
          pointerEvents: "none",
          zIndex: 10, // Ensures it stays above the border
        }}
      >
        {label}
      </label>

      <Select
        options={data}
        placeholder=""
        styles={colourStyles}
        onChange={handleChange}
        isMulti
        components={{ ValueContainer }}
      />
    </div>
  );
};