

import { FC } from 'react';

interface IconRefreshProps {
    className?: string;
}

const ProgressIcon: FC<IconRefreshProps> = ({ className }) => {
    return (
        <svg width="72" height="73" viewBox="0 0 72 73" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
            <path d="M72.0003 20.3916V50.6579L62.1252 55.2852L52.25 50.8426V20.5778C58.5132 20.5183 54.4308 20.5568 72.0003 20.3916Z" fill="#ACCDFF" />
            <path d="M62.1252 25.0203V55.2852L52.25 50.8426V20.5778L57.1534 20.5312L62.1252 25.0203Z" fill="#81B0FE" />
            <path d="M62.1247 16.1345L71.9996 20.3922L62.1247 25.0197L52.25 20.5771L62.1247 16.1345Z" fill="#6396FF" />
            <path d="M59.4183 34.6922V56.4877L49.5431 61.1165L39.668 56.6739V34.8769C51.1768 34.7687 46.7625 34.81 59.4183 34.6922Z" fill="#F79FB2" />
            <path d="M49.5431 34.8769V61.1165L39.668 56.6739V34.8769L49.2468 34.7869L49.5431 34.8769Z" fill="#FA8292" />
            <path d="M49.5446 30.4342L59.4195 34.6916L49.5446 39.3193L39.6699 34.8767L49.5446 30.4342Z" fill="#FF4C4C" />
            <path d="M46.5691 48.9913V62.4572L36.6955 67.086C34.4239 66.064 28.9698 63.6103 26.8203 62.6434V49.176C32.1981 49.1251 28.3123 49.1614 46.5691 48.9913Z" fill="#FAE26B" />
            <path d="M36.6955 53.6186V67.086L28.2277 63.2765L26.8203 61.3322V49.176L30.9138 49.1372L36.6955 53.6186Z" fill="#EDD15A" />
            <path d="M36.6952 44.7338L46.5699 48.9912L36.6952 53.6189L26.8203 49.1764L36.6952 44.7338Z" fill="#EAC736" />
            <path d="M33.7191 59.5431V67.8728L23.8439 72.5L13.9688 68.0574V59.7278C16.1849 59.7079 12.5672 59.7416 33.7191 59.5431Z" fill="#C8C8FC" />
            <path d="M23.8439 64.1703V72.5L13.9688 68.0574V59.7277L15.5267 59.7138L23.8439 64.1703Z" fill="#C8C8FC" />
            <path d="M23.8454 55.285L33.7203 59.5425L23.8454 64.1702L13.9707 59.7276L23.8454 55.285Z" fill="#8484FF" />
            <path d="M59.4186 6.30965L44.5345 25.5495L30.5984 31.266L19.4989 47.1153L5.81583 52.6736C5.81583 52.6736 -0.381752 54.9733 0.018593 49.7129L13.7017 44.1546L24.8012 28.3053L38.7372 22.5888L53.6213 3.34896L59.4186 6.30965Z" fill="#79E9B3" />
            <path d="M48.609 4.49879L58.6187 0.625223C59.6293 0.234033 60.8162 0.805999 60.8162 1.68427V9.79079C60.8162 10.6878 59.5827 11.2584 58.5698 10.83L48.5599 6.59703C47.4788 6.13989 47.5072 4.92521 48.609 4.49879Z" fill="#14D2AA" />
            <path d="M44.5341 25.5495L30.598 31.266L24.8008 28.3053L38.7368 22.5888L44.5341 25.5495Z" fill="#14D2AA" />
            <path d="M19.4989 47.1153L5.81583 52.6736C5.81583 52.6736 -0.381752 54.9733 0.018593 49.7129L13.7017 44.1546L19.4989 47.1153Z" fill="#14D2AA" />
        </svg>
    );
};

export default ProgressIcon;
