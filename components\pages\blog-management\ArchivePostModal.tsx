'use client';

import type React from 'react';

interface ArchivePostModalProps {
    post: any;
    onClose: () => void;
    onConfirm: () => void;
}

export default function ArchivePostModal({ post, onClose, onConfirm }: ArchivePostModalProps) {
    if (!post) return null;

    return (
        <div className="w-full max-w-md mx-auto bg-white rounded-lg p-6">
            {/* Header */}
            <div className="flex justify-between items-start mb-4">
                <h2 className="text-lg font-semibold text-[#2d2d2e] font-inter">Archive Post</h2>
                <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600 text-xl w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100"
                >
                    ×
                </button>
            </div>

            {/* Archive Icon */}
            <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8l4 4 4-4m6 5-1 1a2 2 0 01-2.828 0L12 10.828 8.828 14A2 2 0 016 14l-1-1m0 0V9a2 2 0 012-2h8a2 2 0 012 2v4.172a2 2 0 01-.586 1.414z" />
                    </svg>
                </div>
            </div>

            {/* Content */}
            <div className="text-center mb-6">
                <p className="text-sm text-[#636363] font-inter leading-relaxed">
                    Are you sure you want to archive '<span className="font-medium text-[#2d2d2e]">{post.title}</span>'? This will move the post to the archived section.
                </p>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3">
                <button
                    onClick={onClose}
                    className="px-4 py-2 text-sm font-medium text-[#636363] bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 transition-colors font-inter"
                >
                    Cancel
                </button>
                <button
                    onClick={onConfirm}
                    className="px-4 py-2 text-sm font-medium text-white bg-[#1D7EB6] border border-[#1D7EB6] rounded hover:bg-[#1D7EB6] transition-colors font-inter"
                >
                    Archive
                </button>
            </div>
        </div>
    );
} 