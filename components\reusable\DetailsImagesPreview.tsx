import Image from 'next/image';
import React from 'react';
import images from '@/public/assets/images/main/Preview-image.png';
const DetailsImagesPreview = () => {
    const imagesArray = [images, images, images]
    return (
        <div>
            <div className="grid  ">

                <div className='w-full h-[360px] rounded-lg'>
                    <Image src={imagesArray[0]} alt="Picture of the author" className='h-full w-full object-cover rounded-lg' width={1000} height={1000} />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 pt-5">
                    {imagesArray.map((image, index) => (
                        <div key={index} className="h-[186px]">
                            <Image
                                src={image}
                                alt="Picture of the author"
                                className="h-full w-full object-cover rounded-lg"
                                width={1000}
                                height={1000}
                            />
                        </div>
                    ))}
                </div>


            </div>
        </div>
    );
};

export default DetailsImagesPreview;
