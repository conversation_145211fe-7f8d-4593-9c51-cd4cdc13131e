'use client';
import Image from 'next/image';
import { FileUploadIcon } from '../icon/Icon';
import { useEffect, useState } from 'react';
import Link from 'next/link';

export default function FilePreview({ frontFile }: any) {
    const [fileType, setFileType] = useState<string | null>(null);
    const [isValidImage, setIsValidImage] = useState(true);

    useEffect(() => {
        if (typeof frontFile === 'string') {
            const extension = frontFile.split('.').pop()?.toLowerCase();
            if (extension?.includes('pdf')) {
                setFileType('pdf');
            } else if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'].includes(extension || '')) {
                setFileType('image');
            } else {
                setFileType('other');
            }
        }
        setIsValidImage(true); // Reset image validity when file changes
    }, [frontFile]);

    // Optional: Basic URL validation
    function isValidUrl(url: string) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    return (
        <div className="relative flex min-h-[300px] w-full flex-col items-center justify-center font-inter">
            <div className="flex min-h-[300px] w-full flex-col items-center justify-center rounded-lg bg-white px-2 py-4">
                {!frontFile ? (
                    <div className="text-gray-500">No file selected</div>
                ) : fileType === 'image' ? (
                    isValidImage && isValidUrl(frontFile) ? (
                        <Image
                            src={frontFile}
                            alt="Preview"
                            loading="lazy"
                            quality={100}
                            width={1000}
                            height={1000}
                            className="rounded-lg object-contain"
                            onError={() => setIsValidImage(false)}
                        />
                    ) : (
                        <div className="text-gray-500">Invalid or broken image URL</div>
                    )
                ) : fileType === 'pdf' ? (
                    <iframe src={frontFile} className="h-[500px] w-full rounded-lg border" title="PDF Preview"></iframe>
                ) : (
                    <div className="flex flex-col items-center">
                        <FileUploadIcon className="h-16 w-16 text-gray-400" />
                        <p className="mt-2 text-gray-500">File type not supported for preview</p>
                        <Link href={frontFile || ''} target="_blank" rel="noopener noreferrer" className="mt-2 text-blue-600 underline">
                            Download File
                        </Link>
                    </div>
                )}
            </div>
        </div>
    );
}
