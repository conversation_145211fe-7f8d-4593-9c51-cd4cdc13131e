'use client';
import { useRouter } from 'next/navigation';
import React from 'react';
import { EyeIcon, EyeIconPassword } from '../icon/Icon';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import { showMessage } from '@/app/lib/Alert';
import Link from 'next/link';

const NewPassword = () => {
    const [forgetPassword, setForgetPassword] = React.useState('');
    const { push } = useRouter();
    const [forgetConfirmPasswod, setForgetConfirmPassword] = React.useState('');
    const [showForgetPassword, setShowForgetPassword] = React.useState(false);
    const [showForgetConfirmPassword, setShowForgetConfirmPassword] = React.useState(false);

    const doPasswordsMatch = (password: string, confirmPassword: string) => {
        return password === confirmPassword;
    };

    const PasswordRules = ({ password }: { password: string }) => {
        const rules = [
            { test: /.{8,}/, message: 'At least 8 characters' },
            { test: /[A-Z]/, message: 'At least one uppercase letter' },
            { test: /[a-z]/, message: 'At least one lowercase letter' },
            { test: /\d/, message: 'At least one number' },
            { test: /[@$!%*?&]/, message: 'At least one special character' },
        ];

        if (password.length === 0) return null;

        const firstUnmetRule = rules.find((rule) => !rule.test.test(password));

        return firstUnmetRule ? <div className="mt-2 text-red-500">{firstUnmetRule.message}</div> : <div></div>;
    };

    const handleNewPassword = async () => {
        if (!doPasswordsMatch(forgetPassword, forgetConfirmPasswod)) {
            showMessage('Passwords do not match');
            return;
        }

        if (!forgetPassword || !forgetConfirmPasswod) {
            showMessage('Please enter and confirm your new password.');
            return;
        }

        try {
            // const response = await fetch(API_ENDPOINTS.RESET_PASSWRD, {
            //     method: 'POST',
            //     headers: {
            //         'Content-Type': 'application/json',
            //     },
            //     body: JSON.stringify({
            //         email: localStorage.getItem('tempmail') || '',
            //         newPassword: forgetPassword,
            //         confirmPassword: forgetConfirmPasswod,
            //     }),
            // });

            // const data = await response.json();

            // if (response.ok) {
            //     showMessage('Password reset successfully.', 'success');
            //     localStorage.removeItem('tempmail');
            //     push('/login');
            // } else {
            //     showMessage(data.message, 'error');
            // }
        } catch (error) {
            console.error('Error resetting password:', error);
            showMessage('Something went wrong. Please try again.', 'error');
        }
    };

    return (
        <div>
            <div className=" pb-2 pt-10  text-center">
                <span className="text-[26px] font-semibold text-[#993333]  ">Enter</span>
                <span className="text-[26px] font-semibold text-[#222222]  "> </span>
                <span className="text-[26px] font-semibold text-[#2d2d2e]  ">New Password</span>
            </div>

            <div className="flex h-full flex-col justify-between   pt-4">
                <div className="space-y-4">
                    <div className="relative">
                        <input
                            type={showForgetPassword ? 'text' : 'password'}
                            value={forgetPassword}
                            onChange={(e) => setForgetPassword(e.target.value)}
                            placeholder="Enter Password"
                            required
                            className="text-black-200  h-[53px]  w-full self-stretch  rounded-lg border border-[#e4e4e4] px-5 font-inter focus:outline-blue-500"
                        />
                        <div className="absolute right-3 top-5 flex w-fit items-center justify-end">
                            <span onClick={() => setShowForgetPassword(!showForgetPassword)} className="cursor-pointer font-inter text-base font-medium leading-tight text-[#636363]">
                                {showForgetPassword ? <EyeIconPassword /> : <EyeIcon />}
                            </span>
                        </div>
                        <PasswordRules password={forgetPassword} />
                    </div>
                    <div className="relative">
                        <input
                            type={showForgetConfirmPassword ? 'text' : 'password'}
                            value={forgetConfirmPasswod}
                            onChange={(e) => setForgetConfirmPassword(e.target.value)}
                            placeholder="Re Enter Password"
                            required
                            className="text-black-200  h-[53px]  w-full self-stretch rounded-lg border border-[#e4e4e4] px-5 font-inter focus:outline-blue-500"
                        />
                        <div className="absolute right-3 top-5 flex w-fit items-center justify-end">
                            <span onClick={() => setShowForgetConfirmPassword(!showForgetConfirmPassword)} className="cursor-pointer font-inter text-base font-medium leading-tight text-[#636363]">
                                {showForgetConfirmPassword ? <EyeIconPassword /> : <EyeIcon />}
                            </span>
                        </div>
                        {forgetConfirmPasswod && (
                            <p className={doPasswordsMatch(forgetPassword, forgetConfirmPasswod) ? 'hidden' : 'text-xs text-red-500'}>
                                {doPasswordsMatch(forgetPassword, forgetConfirmPasswod) ? 'Passwords match' : 'Passwords do not match'}
                            </p>
                        )}
                    </div>

                    <button onClick={handleNewPassword} className="flex h-14 w-full items-center justify-center self-stretch rounded-lg bg-[#1d7eb6]">
                        <span className="text-lg font-medium text-white">Continue</span>
                    </button>
                </div>

                <div className=" absolute bottom-5  ">
                    <div className="flex -flex items-center  justify-center gap-1.5  ps-10">
                        <p className="text-base font-medium leading-tight  text-[#636363]">Already have an account?</p>

                        <Link href={'/login'} className="text-center text-lg font-medium leading-normal text-[#1d7eb6]">
                            Log In
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default NewPassword;
